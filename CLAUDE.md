# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Development Commands

```bash
# Development server
pnpm dev

# Build the application
pnpm build

# Start production server
pnpm start

# Lint the codebase
pnpm lint
```

## Project Architecture

### Core Technologies
- **Next.js 15** with App Router
- **TypeScript** with strict mode
- **Clerk** for authentication
- **Legend State** for reactive state management (replacing Zustand)
- **TailwindCSS** + **Radix UI** for styling
- **React Query** for server state
- **@gbozee/ultimate** library for trading functionality

### State Management Migration
The project is currently migrating from React Context to Legend State:
- **New**: `lib/stores/legend-trading-account-store.ts` - Legend State implementation
- **Legacy**: `contexts/trading-account-context.tsx` - React Context (being phased out)
- Use Legend State for all new features

### Key Directories
- `app/` - Next.js App Router pages and API routes
- `components/` - Reusable UI components (Radix UI based)
- `lib/` - Core utilities, stores, and services
  - `stores/` - Legend State stores
  - `hooks/` - Custom React hooks
  - `client/` - API client implementations
  - `database/` - Database services (PocketBase)
- `contexts/` - Legacy React Context providers

### Authentication & Routing
- Uses Clerk for authentication
- Middleware protects routes except public ones (auth, pricing, test pages)
- Public test routes available for development: `/test-*`

### Trading Account System
- Multi-account support with account switching
- Two-step setup process: account creation + onboarding
- Account data includes exchange credentials, proxy settings, trading parameters
- Uses mock data in development

### API Integration
- External trading library: `@gbozee/ultimate`
- PocketBase as database backend
- RESTful API routes in `app/api/`
- Environment variables required: `POCKETBASE_HOST`, `POCKETBASE_EMAIL`, `POCKETBASE_PASSWORD`, `SALT`

### Important Notes
- ESLint and TypeScript errors ignored during builds (development configuration)
- Uses `pnpm` as package manager
- Image optimization disabled in Next.js config
- Webpack configured with Node.js polyfills for client-side