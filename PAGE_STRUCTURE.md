# Page Structure Guidelines

## Overview
This document defines the standard architecture for pages in the Trading App. This structure ensures consistency, maintainability, and optimal performance across all features.

## Standard Page Architecture

### Directory Structure
```
/app/[route]/
  ├── page.tsx           # Server component (auth, data fetching)
  ├── [route]-page.tsx   # Main client component
  └── actions.ts         # Server actions (database operations)

/components/[feature]/
  ├── component-1.tsx    # Reusable UI components
  ├── component-2.tsx    # Feature-specific components
  └── ...               # Additional components as needed
```

### Component Types and Responsibilities

#### 1. Server Component (`page.tsx`)
- **Purpose**: Entry point for the route, handles server-side operations
- **Responsibilities**:
  - Authentication checks (Clerk)
  - Authorization (role-based access)
  - Initial data fetching
  - Rendering the main client component
- **Example**:
```typescript
import { currentUser } from "@clerk/nextjs/server";
import { redirect } from "next/navigation";
import { FeaturePage } from "./feature-page";
import { getInitialData } from "./actions";

export default async function Page() {
  const user = await currentUser();
  if (!user) redirect("/sign-in");
  
  const data = await getInitialData();
  return <FeaturePage initialData={data} />;
}
```

#### 2. Client Component (`[route]-page.tsx`)
- **Purpose**: Main interactive UI component
- **Location**: Same directory as page.tsx
- **Responsibilities**:
  - Client-side state management
  - User interactions
  - Calling server actions
  - Composing child components
- **Naming Convention**: `[route-name]-page.tsx` (e.g., `invitees-page.tsx`)

#### 3. Server Actions (`actions.ts`)
- **Purpose**: Server-side operations and database interactions
- **Responsibilities**:
  - Database CRUD operations (via PocketBase MCP)
  - Business logic validation
  - Security checks
  - Data transformation
- **Important**: Always use `"use server"` directive

#### 4. Reusable Components (`/components/[feature]/`)
- **Purpose**: Modular, reusable UI components
- **Organization**: Group by feature or domain
- **Examples**:
  - `/components/admin/` - Admin-specific components
  - `/components/ui/` - Generic UI components (shadcn)
  - `/components/trading/` - Trading-related components

## Implementation Guidelines

### 1. Authentication & Authorization
- Use Clerk for authentication in server components
- Implement role-based checks using PocketBase user data
- Redirect unauthorized users appropriately

### 2. Data Fetching Strategy
- **Initial Load**: Fetch in server component when possible
- **Dynamic Updates**: Use server actions with React Query
- **Optimistic Updates**: Combine Legend State with server actions for smooth UX

### 3. State Management with Legend State
- **Server State**: Use React Query for server-side data fetching and caching
- **Client State**: Use Legend State for reactive local state management
- **Component State**: Use Legend State observables instead of useState
- **Form State**: React Hook Form for complex forms, Legend State for simple forms
- **Observable Stores**: Create reactive state stores in `/lib/stores/`
- **Custom Hooks**: Provide clean interfaces to stores via custom hooks
- **Observer Components**: Wrap components with `observer()` HOC for reactivity

### 4. Error Handling
- Implement error boundaries for client components
- Use try-catch in server actions
- Provide meaningful error messages to users

### 5. Legend State Implementation Guide

#### Store Structure
Create observable stores in `/lib/stores/` with the following pattern:

```typescript
import { observable } from "@legendapp/state";

// Define interfaces
interface FeatureState {
  items: Item[];
  selectedId: string | null;
  isLoading: boolean;
  filters: FilterState;
}

// Create observable store
export const featureState$ = observable<FeatureState>({
  items: [],
  selectedId: null,
  isLoading: false,
  filters: { search: "", status: "all" }
});

// Define actions
export const featureActions = {
  setItems: (items: Item[]) => featureState$.items.set(items),
  setLoading: (loading: boolean) => featureState$.isLoading.set(loading),
  selectItem: (id: string) => featureState$.selectedId.set(id),
  updateFilter: (key: keyof FilterState, value: any) => 
    featureState$.filters[key].set(value),
};
```

#### Custom Hooks
Create hooks in `/lib/hooks/` to provide clean component interfaces:

```typescript
"use client";
import { featureState$, featureActions } from "../stores/feature-store";

export function useFeature() {
  // Get reactive values (will update when changed)
  const items = featureState$.items.get();
  const isLoading = featureState$.isLoading.get();
  
  return {
    // State
    items,
    isLoading,
    // Actions
    ...featureActions,
  };
}
```

#### Component Integration
Components using Legend State must be wrapped with `observer()`:

```typescript
"use client";
import { observer } from "@legendapp/state/react";
import { useFeature } from "@/lib/hooks/use-feature";

const FeatureComponent = observer(function FeatureComponent() {
  const { items, isLoading, setItems } = useFeature();
  
  // Component will automatically re-render when items change
  return <div>{items.length} items</div>;
});
```

#### Server Action Integration
Combine Legend State with server actions for optimistic updates:

```typescript
const handleAction = async (data: ActionData) => {
  // Optimistic update
  featureActions.setLoading(true);
  
  try {
    const result = await serverAction(data);
    if (result.success) {
      featureActions.setItems(result.data);
    }
  } catch (error) {
    // Handle error, maybe revert optimistic update
  } finally {
    featureActions.setLoading(false);
  }
};
```

### 6. Performance Optimization
- Leverage React Server Components for static content
- Use dynamic imports for heavy client components
- Implement proper loading states
- Use Legend State's fine-grained reactivity for optimal re-renders

## Example: Admin Invitees Feature with Legend State

```
/app/admin/invitees/
  ├── page.tsx           # Check admin role, fetch invitees
  ├── invitees-page.tsx  # Main UI (wrapped with observer())
  └── actions.ts         # getInvitees, approveUser, etc.

/lib/stores/
  └── invitees-store.ts  # Observable state and actions

/lib/hooks/
  └── use-invitees.ts    # Custom hook for store access

/components/admin/
  ├── invitee-table.tsx       # Table (wrapped with observer())
  ├── invitee-actions.tsx     # Action buttons
  ├── invite-code-display.tsx # Code display component
  └── invitee-filters.tsx     # Filter controls
```

### Legend State Implementation Example:

```typescript
// /lib/stores/invitees-store.ts
export const inviteesState$ = observable<InviteesState>({
  invitees: [],
  filteredInvitees: [],
  selectedIds: [],
  isLoading: false,
  filters: { status: "all", search: "" }
});

// /lib/hooks/use-invitees.ts
export const useInvitees = () => {
  const invitees = inviteesState$.invitees.get();
  const isLoading = inviteesState$.isLoading.get();
  
  return { invitees, isLoading, ...inviteesActions };
};

// /app/admin/invitees/invitees-page.tsx
const InviteesPage = observer(function InviteesPage() {
  const { invitees, approveInvitee } = useInvitees();
  // Component automatically re-renders when invitees change
});
```

## Migration Strategy

When refactoring existing pages to this structure:

1. **Identify Components**: Break down the page into logical components
2. **Separate Concerns**: Move server logic to actions.ts
3. **Extract Reusables**: Move shared components to /components
4. **Update Imports**: Ensure all imports reflect new structure
5. **Test Thoroughly**: Verify functionality after restructuring

## Benefits

1. **Consistency**: Predictable structure across all pages
2. **Maintainability**: Clear separation of concerns
3. **Performance**: Optimal use of Next.js 14 features
4. **Scalability**: Easy to add new features
5. **Testing**: Simplified unit and integration testing
6. **Developer Experience**: Clear patterns for new team members

## Exceptions

Some pages may require variations:
- **Simple Static Pages**: May not need client components
- **Complex Features**: May have additional subdirectories
- **Shared Layouts**: Use layout.tsx for common UI elements

Always prioritize clarity and maintainability over strict adherence to the pattern.