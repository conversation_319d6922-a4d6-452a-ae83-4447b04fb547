import { expect, test, beforeEach } from "bun:test";
import { DBService, getDBService } from "@/lib/services/db";

let dbService: DBService;

beforeEach(async () => {
  dbService = await getDBService();
});

test("DBService should be defined", async () => {
  expect(dbService).toBeDefined();
});

test("DBService should return an OtpId when verifyEmail() is called.", async () => {
  const email = "<EMAIL>";
  const result = await dbService.requestOtp(email);
  expect(result).toBeDefined();
  expect(result).not.toBeNull();
  expect(result.otpId).toBeDefined();
});

test("DBService.determineMaxAge should return the correct maxAge in seconds", async () => {
  const token =
    "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJjb2xsZWN0aW9uSWQiOiJfcGJfdXNlcnNfYXV0aF8iLCJleHAiOjE3NTUxNjAxMDIsImlkIjoicGYwNjM2NHhjZHNjZ2NsIiwicmVmcmVzaGFibGUiOnRydWUsInR5cGUiOiJhdXRoIn0.ZbJEa5efJY1mLSthf19oPaJBZSLyTHhVsgsLRdsV4ls";
  const maxAge = await dbService.determineMaxAge(token);
  console.log(maxAge);
  expect(maxAge).toBeLessThanOrEqual(604799);
  expect(maxAge).toBeGreaterThan(0);
});

test("DbService.getUserFromToken should return the correct user when a valid token is provided", async () => {
  const token =
    "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJjb2xsZWN0aW9uSWQiOiJfcGJfdXNlcnNfYXV0aF8iLCJleHAiOjE3NTU2OTczMDksImlkIjoicGYwNjM2NHhjZHNjZ2NsIiwicmVmcmVzaGFibGUiOnRydWUsInR5cGUiOiJhdXRoIn0.7b-KFusSxO_nXd1HARyYIyN---gWNCRp-2fEKIwM-_g";
  const dbUser = await dbService.getUserFromToken(token);
  const user = dbUser!;

  // console.log("userer", user);

  // expect(user).not.toBeNull();

  // expect(user.admin).toEqual("");
  // expect(user.approved).toEqual(true);
  expect(user.avatar).toEqual("");
  expect(user.collectionId).toEqual("_pb_users_auth_");
  expect(user.collectionName).toEqual("users");
  expect(user.created).toEqual("2025-08-05 09:18:51.886Z");
  expect(user.email).toEqual("<EMAIL>");
  expect(user.emailVisibility).toEqual(false);
  expect(user.id).toEqual("pf06364xcdscgcl");
  expect(user.invite_code).toEqual("");
  // expect(user.name).toEqual("Tayo");
  // expect(user.role).toEqual("admin");
  // expect(user.updated).toEqual("2025-08-06 21:29:16.708Z");
  // expect(user.verified).toEqual(true);
});

test("DBService should return inValid or expired token if provided", async () => {
  const token =
    "geyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJjb2xsZWN0aW9uSWQiOiJfcGJfdXNlcnNfYXV0aF8iLCJleHAiOjE3NTQyOTYxMDIsImlkIjoicGYwNjM2NHhjZHNjZ2NsIiwicmVmcmVzaGFibGUiOnRydWUsInR5cGUiOiJhdXRoIn0.OhEripXMTRbzavNNek-gf0em7F6H-ocUJSxNE75lnHU";

  const user = await dbService.getUserFromToken(token);
  expect(user).toBeNull();
});


test("DBService should return the correct user when getInviteCodeOwner() is called.", async () => {
  const inviteCode = "OnePieceMembers";
  const user = await dbService.getInviteCodeOwner(inviteCode);

  console.log("userinvite", user)
  // expect(user).not.toBeNull();
});

test("DBService should return the updated userRecord when updateUserRecord() is called.", async () => {
  const userId = "pf06364xcdscgcl";
  const user = await dbService.updateUserRecord({
    userId: userId,
    updates: { type: "beginner",admin:"xs9vzwxe5c04vbx" },
  });
  expect(user).not.toBeNull();
  expect(user?.name).toEqual("Tayo Oyeniyi");
});

test("DBService should return the correct user when getUserByEmail() is called.", async () => {
  const email = "<EMAIL>";
  const user = await dbService.getUserByEmail(email);
  console.log("user",user)
  expect(user).not.toBeNull();
  expect(user?.email).toEqual(email);
});
