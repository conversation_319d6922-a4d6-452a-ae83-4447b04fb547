import { cookies } from "next/headers";

export const USER_KEY = "logged-in-user";
export const setCookie = async ({
  key,
  value,
  maxAge = 60 * 15,
}: {
  key: string;
  value: string;
  maxAge?: number;
}) => {
  const cookieStore = await cookies();
  cookieStore.set(key, value, {
    httpOnly: true,
    secure: process.env.NODE_ENV === "production",
    sameSite: "strict",
    maxAge,
  });
};

export const setMultipleCookies = async (
  cookies_: {
    key: string;
    value: string;
    maxAge?: number;
  }[]
) => {
  const cookieStore = await cookies();
  cookies_.forEach((cookie) => {
    cookieStore.set(cookie.key, cookie.value, {
      httpOnly: true,
      secure: process.env.NODE_ENV === "production",
      sameSite: "strict",
      maxAge: cookie.maxAge || 60 * 15,
    });
  });
};

export const getCookie = async (key: string) => {
  const cookieStore = await cookies();
  return cookieStore.get(key)?.value;
};

export const getMultipleCookies = async (keys: string[]) => {
  const cookieStore = await cookies();
  const allCookies = cookieStore.getAll();

  return keys.reduce((result, key) => {
    const cookie = allCookies.find((c) => c.name === key);
    result[key] = cookie?.value ?? null;
    return result;
  }, {} as Record<string, string | null>);
};

export const deleteCookie = async (key: string) => {
  const cookieStore = await cookies();
  cookieStore.delete(key);
};

export const deleteMultipleCookies = async (keys: string[]) => {
  const cookieStore = await cookies();
  keys.forEach((key) => {
    cookieStore.delete(key);
  });
};
