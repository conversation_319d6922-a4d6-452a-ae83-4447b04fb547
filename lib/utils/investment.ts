import { differenceInDays, format } from "date-fns";
import { InvestorProfile } from "../constants/investment";
export function calculateInvestmentProgress({
  startDate,
  investmentPeriod,
}: {
  startDate: string;
  investmentPeriod: number;
}) {
  const start = new Date(startDate);
  const today = new Date();

  let elapsed = differenceInDays(today, start);

  // Clamp within 0 → investmentPeriod
  if (elapsed < 0) elapsed = 0;
  if (elapsed > investmentPeriod) elapsed = investmentPeriod;

  const remaining = investmentPeriod - elapsed;

  return {
    daysElapsed: elapsed,
    daysRemaining: remaining,
  };
}

export function calculateInvestmentDays(
  accounts: InvestorProfile[],
  today: string | Date = new Date()
) {
  const todayDate = typeof today === "string" ? new Date(today) : today;

  if (!accounts || accounts.length === 0) {
    return { totalDays: 0, daysElapsed: 0, daysRemaining: 0, startDate: "" };
  }

  const startDates = accounts.map((acc) => new Date(acc.startDate).getTime());
  const endDates = accounts.map(
    (acc) =>
      new Date(acc.startDate).getTime() +
      acc.investmentPeriod * 24 * 60 * 60 * 1000
  );

  const earliestStart = new Date(Math.min(...startDates));
  const latestEnd = new Date(Math.max(...endDates));

  const totalDays = Math.ceil(
    (latestEnd.getTime() - earliestStart.getTime()) / (1000 * 60 * 60 * 24)
  );

  const cappedToday = todayDate > latestEnd ? latestEnd : todayDate;
  const daysElapsed = Math.ceil(
    (cappedToday.getTime() - earliestStart.getTime()) / (1000 * 60 * 60 * 24)
  );

  const daysRemaining = totalDays - daysElapsed;

  return {
    totalDays,
    daysElapsed,
    daysRemaining,
    startDate: formatDate(earliestStart),
  };
}

export function formatDate(dateInput: string | Date): string {
  const date = typeof dateInput === "string" ? new Date(dateInput) : dateInput;

  return date.toLocaleDateString("en-GB", {
    day: "2-digit",
    month: "short",
    year: "numeric",
  });
}

export function getInvestmentStartInfo(startDate: string) {
  const date = new Date(startDate);

  return {
    month: format(date, "LLLL"),
    fullDate: format(date, "MMMM d, yyyy"), 
  };
}
