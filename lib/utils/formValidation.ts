export const validateName = (name: string): string | undefined => {
  if (!name.trim()) {
    return "Name is required";
  }
  if (name.trim().length < 2) {
    return "Name must be at least 2 characters long";
  }
  if (name.trim().length > 50) {
    return "Name must be less than 50 characters";
  }
  if (!/^[a-zA-Z\s'-]+$/.test(name.trim())) {
    return "Name can only contain letters, spaces, hyphens, and apostrophes";
  }
  return undefined;
};

export const validateEmail = (email: string): string | undefined => {
  if (!email.trim()) {
    return "Email address is required";
  }
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  if (!emailRegex.test(email.trim())) {
    return "Please enter a valid email address";
  }
  if (email.length > 254) {
    return "Email address is too long";
  }
  return undefined;
};

export const validateInvestmentName = (name: string): string | undefined => {
  if (!name.trim()) {
    return "Investment name is required";
  }
  if (name.trim().length < 3) {
    return "Investment name must be at least 3 characters long";
  }
  if (name.trim().length > 100) {
    return "Investment name must be less than 100 characters";
  }
  return undefined;
};

export const validateAmount = (amount: string): string | undefined => {
  if (!amount.trim()) {
    return "Amount is required";
  }
  const numAmount = Number.parseFloat(amount);
  if (isNaN(numAmount)) {
    return "Please enter a valid amount";
  }
  if (numAmount < 100) {
    return "Minimum investment amount is $100 USDT";
  }
  if (numAmount > 1000000) {
    return "Maximum investment amount is $1,000,000 USDT";
  }
  return undefined;
};

export const validateBeneficiaryRate = (rate: string): string | undefined => {
  if (!rate.trim()) {
    return "Profit rate is required";
  }
  const numRate = Number.parseFloat(rate);
  if (isNaN(numRate)) {
    return "Please enter a valid rate";
  }
  if (numRate < 1) {
    return "Minimum profit rate is 1%";
  }
  if (numRate > 10) {
    return "Maximum profit rate is 10%";
  }
  return undefined;
};

export const validateCardNumber = (cardNumber: string): string | undefined => {
  const cleaned = cardNumber.replace(/\s/g, "");
  if (!cleaned) {
    return "Card number is required";
  }
  if (!/^\d+$/.test(cleaned)) {
    return "Card number must contain only digits";
  }
  if (cleaned.length < 13 || cleaned.length > 19) {
    return "Please enter a valid card number";
  }
  return undefined;
};

export const validateExpiryDate = (expiryDate: string): string | undefined => {
  if (!expiryDate.trim()) {
    return "Expiry date is required";
  }
  const regex = /^(0[1-9]|1[0-2])\/([0-9]{2})$/;
  if (!regex.test(expiryDate)) {
    return "Please enter date in MM/YY format";
  }
  const [month, year] = expiryDate.split("/");
  const currentDate = new Date();
  const currentYear = currentDate.getFullYear() % 100;
  const currentMonth = currentDate.getMonth() + 1;
  const expYear = Number.parseInt(year);
  const expMonth = Number.parseInt(month);

  if (
    expYear < currentYear ||
    (expYear === currentYear && expMonth < currentMonth)
  ) {
    return "Card has expired";
  }
  return undefined;
};

export const validateCVV = (cvv: string): string | undefined => {
  if (!cvv.trim()) {
    return "CVV is required";
  }
  if (!/^\d{3,4}$/.test(cvv)) {
    return "CVV must be 3 or 4 digits";
  }
  return undefined;
};

export const validateCardName = (cardName: string): string | undefined => {
  if (!cardName.trim()) {
    return "Cardholder name is required";
  }
  if (cardName.trim().length < 2) {
    return "Name must be at least 2 characters long";
  }
  if (!/^[a-zA-Z\s'-]+$/.test(cardName.trim())) {
    return "Name can only contain letters, spaces, hyphens, and apostrophes";
  }
  return undefined;
};

//  export const validateWalletAddress = (address: string, network: string): string | undefined => {
//     if (!address || !address.trim()) {
//       return "Wallet address is required"
//     }

//     // Basic wallet address validation based on network
//     if (network === "TRC20" && !address.startsWith("T")) {
//       return "TRC20 addresses must start with 'T'"
//     }
//     if (network === "ERC20" && !address.startsWith("0x")) {
//       return "ERC20 addresses must start with '0x'"
//     }
//     if (network === "BEP20" && !address.startsWith("0x")) {
//       return "BEP20 addresses must start with '0x'"
//     }

//     if (address.length < 26 || address.length > 42) {
//       return "Please enter a valid wallet address"
//     }

//     return undefined
//   }

export const validateWalletAddress = (
  address: string,
  network: string
): string | undefined => {
  if (!address || !address.trim()) {
    return "Wallet address is required.";
  }

  const validators: Record<string, { regex: RegExp; message: string }> = {
    TRC20: {
      regex: /^T[a-zA-Z0-9]{33}$/,
      message:
        "Invalid TRC20 address. It should start with 'T' and be 34 characters long.",
    },
    ERC20: {
      regex: /^0x[a-fA-F0-9]{40}$/,
      message:
        "Invalid ERC20 address. It should start with '0x' followed by 40 hexadecimal characters.",
    },
    BEP20: {
      regex: /^0x[a-fA-F0-9]{40}$/,
      message:
        "Invalid BEP20 address. It should start with '0x' followed by 40 hexadecimal characters.",
    },
    BTC: {
      regex: /^(1|3|bc1)[a-zA-Z0-9]{25,39}$/,
      message:
        "Invalid BTC address. Must start with '1', '3', or 'bc1' and be 26–42 characters long.",
    },
    LTC: {
      regex: /^(L|M|ltc1)[a-zA-Z0-9]{25,39}$/,
      message:
        "Invalid LTC address. Must start with 'L', 'M', or 'ltc1' and be 26–42 characters long.",
    },
    XRP: {
      regex: /^r[0-9a-zA-Z]{24,34}$/,
      message:
        "Invalid XRP address. It should start with 'r' and be 25–35 characters long.",
    },
  };

  const validator = validators[network];
  if (!validator) {
    return "Unsupported network.";
  }

  if (!validator.regex.test(address)) {
    return validator.message;
  }

  return undefined;
};

export const validateVerificationCode = (code: string): string | undefined => {
  if (!code.trim()) {
    return "Verification code is required";
  }
  if (code.length !== 8) {
    return "Verification code must be exactly 8 digits";
  }
  if (!/^\d{8}$/.test(code)) {
    return "Verification code must contain only numbers";
  }
  return undefined;
};

export const formatCurrency = (amount: number) => {
  return new Intl.NumberFormat("en-US", {
    style: "currency",
    currency: "USD",
  }).format(amount);
};

export const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString("en-US", {
    month: "short",
    day: "numeric",
    year: "numeric",
  });
};

export const getDaysRemaining = (endDate: string) => {
  const end = new Date(endDate);
  const now = new Date();
  const diffTime = end.getTime() - now.getTime();
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  return Math.max(0, diffDays);
};

export const formatCardNumber = (value: string) => {
  const v = value.replace(/\s+/g, "").replace(/[^0-9]/gi, "");
  const matches = v.match(/\d{4,16}/g);
  const match = (matches && matches[0]) || "";
  const parts = [];

  for (let i = 0, len = match.length; i < len; i += 4) {
    parts.push(match.substring(i, i + 4));
  }

  if (parts.length) {
    return parts.join(" ");
  } else {
    return v;
  }
};

export const formatExpiryDate = (value: string) => {
  const v = value.replace(/\s+/g, "").replace(/[^0-9]/gi, "");
  if (v.length >= 2) {
    return `${v.substring(0, 2)}/${v.substring(2, 4)}`;
  }
  return v;
};
