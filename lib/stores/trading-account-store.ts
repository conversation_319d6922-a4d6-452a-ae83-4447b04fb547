import { create } from "zustand";
import { devtools, persist } from "zustand/middleware";

export interface TradingAccount {
  id: string;
  name: string;
  exchange: string;
  apiKey: string;
  balance: string;
  tradingPair: string;
  isActive: boolean;
  setupComplete: boolean;
  createdAt: string;
  // Additional trading account fields
  usdt?: number;
  usdc?: number;
  proxy?: string;
  bullish?: boolean;
  bearish?: boolean;
  totalRisk?: number;
  movePercent?: number;
  max_non_essential?: number;
  profit_percent?: number;
  risk_reward?: number;
  exclude_coins?: string[];
  include_delisted?: boolean;
}

interface TradingAccountState {
  accounts: TradingAccount[];
  activeAccountId: string | null;
  isLoading: boolean;

  // Actions
  setAccounts: (accounts: TradingAccount[]) => void;
  setActiveAccountId: (accountId: string | null) => void;
  setLoading: (loading: boolean) => void;
  addAccount: (account: TradingAccount) => void;
  updateAccount: (accountId: string, updates: Partial<TradingAccount>) => void;
  deleteAccount: (accountId: string) => void;
  getActiveAccount: () => TradingAccount | null;
  switchAccount: (accountId: string) => void;
  createAccount: (accountData: Omit<TradingAccount, 'id' | 'createdAt'>) => TradingAccount;
  resumeAccountSetup: (accountId: string) => void;
}

export const useTradingAccountStore = create<TradingAccountState>()(
  devtools(
    persist(
      (set, get) => ({
        accounts: [],
        activeAccountId: null,
        isLoading: false,

        setAccounts: (accounts) =>
          set(
            { accounts },
            false,
            "setAccounts"
          ),

        setActiveAccountId: (activeAccountId) =>
          set(
            { activeAccountId },
            false,
            "setActiveAccountId"
          ),

        setLoading: (isLoading) =>
          set(
            { isLoading },
            false,
            "setLoading"
          ),

        addAccount: (account) =>
          set(
            (state) => ({
              accounts: [...state.accounts, account],
            }),
            false,
            "addAccount"
          ),

        updateAccount: (accountId, updates) =>
          set(
            (state) => ({
              accounts: state.accounts.map((account) =>
                account.id === accountId ? { ...account, ...updates } : account
              ),
            }),
            false,
            "updateAccount"
          ),

        deleteAccount: (accountId) =>
          set(
            (state) => {
              const newAccounts = state.accounts.filter((account) => account.id !== accountId);
              const newActiveAccountId = state.activeAccountId === accountId 
                ? (newAccounts.length > 0 ? newAccounts[0].id : null)
                : state.activeAccountId;
              
              return {
                accounts: newAccounts,
                activeAccountId: newActiveAccountId,
              };
            },
            false,
            "deleteAccount"
          ),

        getActiveAccount: () => {
          const state = get();
          return state.accounts.find((account) => account.id === state.activeAccountId) || null;
        },

        switchAccount: (accountId) => {
          const state = get();
          const account = state.accounts.find((acc) => acc.id === accountId);
          if (account) {
            set(
              { activeAccountId: accountId },
              false,
              "switchAccount"
            );
          }
        },

        createAccount: (accountData) => {
          const newAccount: TradingAccount = {
            ...accountData,
            id: `account-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
            createdAt: new Date().toISOString(),
          };

          set(
            (state) => ({
              accounts: [...state.accounts, newAccount],
              activeAccountId: newAccount.id,
            }),
            false,
            "createAccount"
          );

          return newAccount;
        },

        resumeAccountSetup: (accountId) => {
          const state = get();
          const account = state.accounts.find((acc) => acc.id === accountId);
          if (account) {
            set(
              { activeAccountId: accountId },
              false,
              "resumeAccountSetup"
            );
            
            // Set a flag in localStorage to indicate we're setting up this account
            if (typeof window !== "undefined") {
              localStorage.setItem("accountSetupId", accountId);
            }
          }
        },
      }),
      {
        name: "trading-account-storage",
        partialize: (state) => ({
          accounts: state.accounts,
          activeAccountId: state.activeAccountId,
        }),
      }
    ),
    {
      name: "trading-account-store",
    }
  )
);

// Convenience hook that combines the store with computed values
export function useTradingAccount() {
  const store = useTradingAccountStore();
  
  return {
    ...store,
    // Add any computed values or additional logic here
    hasAccounts: store.accounts.length > 0,
    activeAccount: store.getActiveAccount(),
  };
}
