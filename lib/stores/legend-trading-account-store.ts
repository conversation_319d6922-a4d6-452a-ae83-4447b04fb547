import { observable } from "@legendapp/state"

export interface TradingAccount {
  id: string
  name: string
  exchange: string
  apiKey: string
  balance: string
  tradingPair: string
  isActive: boolean
  setupComplete: boolean
  createdAt: string
  // Additional trading account fields
  usdt?: number
  usdc?: number
  proxy?: string
  bullish?: boolean
  bearish?: boolean
  totalRisk?: number
  movePercent?: number
  max_non_essential?: number
  profit_percent?: number
  risk_reward?: number
  exclude_coins?: string[]
  include_delisted?: boolean
}

export type UserType = "beginner" | "experienced";
export type ApprovalStatus = "pending" | "approved" | "rejected";

interface TradingAccountState {
  accounts: TradingAccount[]
  activeAccountId: string | null
  isLoading: boolean
  hasCompletedOnboarding: boolean
  userType: UserType | null
  approvalStatus: ApprovalStatus | null
  inviteCode: string | null
}

// Create the mock account data
const mockAccount: TradingAccount = {
  id: "account-1",
  name: "Main Trading Account",
  exchange: "binance",
  apiKey: "mock-key",
  balance: "$10,245.67",
  tradingPair: "BTC/USDT",
  isActive: true,
  setupComplete: true,
  createdAt: new Date().toISOString(),
}

// Create the observable state with initial mock data
export const tradingAccountState$ = observable<TradingAccountState>({
  accounts: [mockAccount],
  activeAccountId: mockAccount.id,
  isLoading: false,
  hasCompletedOnboarding: false,
  userType: null,
  approvalStatus: null,
  inviteCode: null,
})

// Note: Persistence removed to avoid localStorage usage as per user preference
// State will be managed in memory and reset on page refresh

// No initialization needed since we start with mock data

// Actions
export const tradingAccountActions = {
  // Set accounts
  setAccounts: (accounts: TradingAccount[]) => {
    tradingAccountState$.accounts.set(accounts)
  },

  // Set active account ID
  setActiveAccountId: (accountId: string | null) => {
    tradingAccountState$.activeAccountId.set(accountId)
    
    // Update isActive flag for all accounts
    const accounts = tradingAccountState$.accounts.get()
    const updatedAccounts = accounts.map(account => ({
      ...account,
      isActive: account.id === accountId
    }))
    tradingAccountState$.accounts.set(updatedAccounts)
  },

  // Set loading state
  setLoading: (loading: boolean) => {
    tradingAccountState$.isLoading.set(loading)
  },

  // Set onboarding completion status
  setHasCompletedOnboarding: (completed: boolean) => {
    tradingAccountState$.hasCompletedOnboarding.set(completed)
  },

  // Set user type
  setUserType: (userType: UserType | null) => {
    tradingAccountState$.userType.set(userType)
  },

  // Set approval status
  setApprovalStatus: (status: ApprovalStatus | null) => {
    tradingAccountState$.approvalStatus.set(status)
  },

  // Set invite code
  setInviteCode: (code: string | null) => {
    tradingAccountState$.inviteCode.set(code)
  },

  // Add a new account
  addAccount: (account: TradingAccount) => {
    const currentAccounts = tradingAccountState$.accounts.get()
    tradingAccountState$.accounts.set([...currentAccounts, account])
  },

  // Update an existing account
  updateAccount: (accountId: string, updates: Partial<TradingAccount>) => {
    const accounts = tradingAccountState$.accounts.get()
    const updatedAccounts = accounts.map(account =>
      account.id === accountId ? { ...account, ...updates } : account
    )
    tradingAccountState$.accounts.set(updatedAccounts)
  },

  // Delete an account
  deleteAccount: (accountId: string) => {
    const accounts = tradingAccountState$.accounts.get()
    const filteredAccounts = accounts.filter(account => account.id !== accountId)
    tradingAccountState$.accounts.set(filteredAccounts)
    
    // If the deleted account was active, clear active account
    if (tradingAccountState$.activeAccountId.get() === accountId) {
      tradingAccountState$.activeAccountId.set(null)
    }
  },

  // Get active account
  getActiveAccount: (): TradingAccount | null => {
    const activeAccountId = tradingAccountState$.activeAccountId.get()
    if (!activeAccountId) return null
    
    const accounts = tradingAccountState$.accounts.get()
    return accounts.find(account => account.id === activeAccountId) || null
  },

  // Switch to a different account
  switchAccount: (accountId: string) => {
    tradingAccountActions.setActiveAccountId(accountId)
  },

  // Create a new account
  createAccount: (accountData: Omit<TradingAccount, 'id' | 'createdAt'>): TradingAccount => {
    const newAccount: TradingAccount = {
      ...accountData,
      id: `account-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      createdAt: new Date().toISOString(),
    }

    tradingAccountActions.addAccount(newAccount)
    tradingAccountActions.setActiveAccountId(newAccount.id)
    
    return newAccount
  },

  // Resume account setup
  resumeAccountSetup: (accountId: string) => {
    const accounts = tradingAccountState$.accounts.get()
    const account = accounts.find(acc => acc.id === accountId)

    if (account) {
      tradingAccountActions.setActiveAccountId(accountId)
      // Note: localStorage usage removed as per user preference
      // Account setup state is now managed in memory only
    }
  },

  // Complete onboarding
  completeOnboarding: () => {
    tradingAccountActions.setHasCompletedOnboarding(true)
    
    // Mark the active account as complete
    const activeAccountId = tradingAccountState$.activeAccountId.get()
    if (activeAccountId) {
      tradingAccountActions.updateAccount(activeAccountId, { setupComplete: true })
    }
  },

  // Initialize with mock data (for development)
  initializeMockData: () => {
    const mockAccount: TradingAccount = {
      id: "account-1",
      name: "Main Trading Account",
      exchange: "binance",
      apiKey: "mock-key",
      balance: "$10,245.67",
      tradingPair: "BTC/USDT",
      isActive: true,
      setupComplete: true,
      createdAt: new Date().toISOString(),
    }
    
    const currentAccounts = tradingAccountState$.accounts.get()
    if (currentAccounts.length === 0) {
      tradingAccountActions.setAccounts([mockAccount])
      tradingAccountActions.setActiveAccountId(mockAccount.id)
    }
  }
}
