type Wallet = {
  id: string;
  address: string;
  network: string;
  blacklisted:boolean;
  created: string;
  updated: string;
};

type UserProps = {
  admin: string;
  approved: boolean;
  avatar: string;
  collectionId: string;
  collectionName: string;
  created: string;
  email: string;
  emailVisibility: boolean;
  id: string;
  invite_code: string;
  name: string;
  role: string;
  updated: string;
  verified: boolean;
  type: "beginner" | "experienced";
  settings?: any; // JSON object containing user settings
  wallet?: string;
  expand?: {
    admin?: {
      id: string;
      email: string;
      emailVisibility: boolean;
      verified: boolean;
      name: string;
      avatar: string;
      role: string;
      invite_code: string;
      approved: boolean;
      created: string;
      updated: string;
    };
    wallet?: Wallet;
  };
};
