/**
 * Centralized types for invite-based user management system
 */

export interface InviteUser {
  id: string;
  email: string;
  emailVisibility: boolean;
  verified: boolean;
  name: string;
  avatar?: string;
  role: 'user' | 'admin';
  admin?: string; // RELATION_RECORD_ID
  invite_code: string;
  approved: boolean;
  created: string;
  updated: string;
}

// Alias for backward compatibility and cleaner naming in admin contexts
export type Invitee = InviteUser;

export interface CreateUserRequest {
  email: string;
  name: string;
  invite_code: string;
  password: string;
  passwordConfirm: string;
}

export interface ApprovalStatusUpdate {
  action: 'create' | 'update' | 'delete';
  record: InviteUser;
}

/**
 * Invitations table schema for tracking admin-invitee relationships
 */
export interface Invitation {
  id: string;
  admin: string; // RELATION_RECORD_ID to users table (admin who created the invitation)
  invitee: string; // RELATION_RECORD_ID to users table (user who was invited)
  invite_code: string; // The invite code used
  status: 'pending' | 'approved' | 'rejected';
  invited_at: string; // When the invitation was created (user signed up)
  responded_at?: string; // When admin approved/rejected
  created: string;
  updated: string;
}

export interface CreateInvitationRequest {
  admin_id: string;
  invitee_id: string;
  invite_code: string;
}

export interface InvitationWithDetails extends Invitation {
  expand?: {
    admin?: InviteUser;
    invitee?: InviteUser;
  };
}

export interface InviteFilters {
  status: "all" | "pending" | "approved" | "rejected";
  search: string;
}

export interface AdminUser {
  id: string;
  email: string;
  name: string;
  role: string;
  invite_code: string;
}

export interface InviteActionResult {
  success: boolean;
  message: string;
}

export interface BulkApprovalResult extends InviteActionResult {
  approved: number;
}

export interface UserApprovalStatusResult {
  success: boolean;
  approved?: boolean;
  user?: InviteUser;
  message: string;
}

export interface InviteValidationResult {
  success: boolean;
  message: string;
  inviterName?: string;
}

export interface GetUsersResult {
  success: boolean;
  users?: InviteUser[];
  message: string;
}

export interface GetUserResult {
  success: boolean;
  user?: InviteUser;
  message: string;
}
