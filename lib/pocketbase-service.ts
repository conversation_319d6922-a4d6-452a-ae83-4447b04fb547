"use server";

import { UltimateClient } from './client/ultimate-client';

// Types for the new user structure
export interface InviteUser {
  id: string;
  email: string;
  emailVisibility: boolean;
  verified: boolean;
  name: string;
  avatar?: string;
  role: 'user' | 'admin';
  admin?: string; // RELATION_RECORD_ID
  invite_code: string;
  approved: boolean;
  created: string;
  updated: string;
}

export interface CreateUserRequest {
  email: string;
  name: string;
  invite_code: string;
  password: string;
  passwordConfirm: string;
}

export interface ApprovalStatusUpdate {
  action: 'create' | 'update' | 'delete';
  record: InviteUser;
}

/**
 * Centralized PocketBase service for invite-based user management
 * Uses Ultimate client for PocketBase operations with proper authentication
 */
export class PocketBaseService {
  private ultimateClient: UltimateClient | null = null;
  private pb: any = null;
  private static instance: PocketBaseService;

  private constructor() {
    // Initialize will be done lazily
  }

  /**
   * Get singleton instance
   */
  static getInstance(): PocketBaseService {
    if (!PocketBaseService.instance) {
      PocketBaseService.instance = new PocketBaseService();
    }
    return PocketBaseService.instance;
  }

  /**
   * Initialize Ultimate client and get PocketBase instance
   */
  private async initializeClient(): Promise<void> {
    if (!this.ultimateClient || !this.pb) {
      // Use a default email for admin operations
      const adminEmail = process.env.NEW_POCKETBASE_EMAIL || '<EMAIL>';

      this.ultimateClient = await UltimateClient.getInstance({
        email: adminEmail,
        salt: process.env.SALT
      });

      // Get the PocketBase instance from Ultimate client
      const app = this.ultimateClient.getApp();
      this.pb = app.app_db.pb;
    }
  }

  /**
   * Get authenticated PocketBase instance
   */
  private async getPocketBase(): Promise<any> {
    await this.initializeClient();
    return this.pb;
  }

  /**
   * Find user by invite code
   */
  async findUserByInviteCode(inviteCode: string): Promise<InviteUser | null> {
    try {
      const pb = await this.getPocketBase();

      const record = await pb.collection('users').getFirstListItem(
        `invite_code = "${inviteCode}"`
      );

      return record as InviteUser;
    } catch (error) {
      console.error('Error finding user by invite code:', error);
      return null;
    }
  }

  /**
   * Check if invite code exists and belongs to an approved user
   */
  async validateInviteCode(inviteCode: string): Promise<{
    valid: boolean;
    user?: InviteUser;
    message: string;
  }> {
    try {
      const user = await this.findUserByInviteCode(inviteCode);
      
      if (!user) {
        return {
          valid: false,
          message: 'Invalid invite code'
        };
      }

      if (!user.approved) {
        return {
          valid: false,
          user,
          message: 'Invite code user is not approved to invite others'
        };
      }

      return {
        valid: true,
        user,
        message: 'Valid invite code'
      };
    } catch (error) {
      console.error('Error validating invite code:', error);
      return {
        valid: false,
        message: 'Error validating invite code'
      };
    }
  }

  /**
   * Create a new user with pending approval status
   */
  async createPendingUser(userData: CreateUserRequest): Promise<{
    success: boolean;
    user?: InviteUser;
    message: string;
  }> {
    try {
      const pb = await this.getPocketBase();

      // First validate the invite code
      const inviteValidation = await this.validateInviteCode(userData.invite_code);
      if (!inviteValidation.valid) {
        return {
          success: false,
          message: inviteValidation.message
        };
      }

      // Check if user already exists
      const existingUser = await this.getUserByEmail(userData.email);
      if (existingUser) {
        return {
          success: false,
          message: 'User with this email already exists'
        };
      }

      // Create user with approved: false
      const newUser = await pb.collection('users').create({
        email: userData.email,
        name: userData.name,
        password: userData.password,
        passwordConfirm: userData.passwordConfirm,
        emailVisibility: true,
        verified: false,
        role: 'user',
        invite_code: userData.invite_code,
        approved: false
      });

      return {
        success: true,
        user: newUser as InviteUser,
        message: 'User created successfully. Awaiting approval.'
      };
    } catch (error) {
      console.error('Error creating pending user:', error);
      return {
        success: false,
        message: error instanceof Error ? error.message : 'Failed to create user'
      };
    }
  }

  /**
   * Get user by email
   */
  async getUserByEmail(email: string): Promise<InviteUser | null> {
    try {
      const pb = await this.getPocketBase();

      const record = await pb.collection('users').getFirstListItem(
        `email = "${email}"`
      );

      return record as InviteUser;
    } catch (error) {
      console.error('Error getting user by email:', error);
      return null;
    }
  }

  /**
   * Get user by ID
   */
  async getUserById(userId: string): Promise<InviteUser | null> {
    try {
      const pb = await this.getPocketBase();

      const record = await pb.collection('users').getOne(userId);
      return record as InviteUser;
    } catch (error) {
      console.error('Error getting user by ID:', error);
      return null;
    }
  }

  /**
   * Approve a user (change approved status to true)
   */
  async approveUser(userId: string): Promise<{
    success: boolean;
    user?: InviteUser;
    message: string;
  }> {
    try {
      const pb = await this.getPocketBase();

      const updatedUser = await pb.collection('users').update(userId, {
        approved: true,
        verified: true
      });

      return {
        success: true,
        user: updatedUser as InviteUser,
        message: 'User approved successfully'
      };
    } catch (error) {
      console.error('Error approving user:', error);
      return {
        success: false,
        message: error instanceof Error ? error.message : 'Failed to approve user'
      };
    }
  }

  /**
   * Get all pending users (approved: false)
   */
  async getPendingUsers(): Promise<InviteUser[]> {
    try {
      const pb = await this.getPocketBase();

      const records = await pb.collection('users').getFullList({
        filter: 'approved = false',
        sort: '-created'
      });

      return records as InviteUser[];
    } catch (error) {
      console.error('Error getting pending users:', error);
      return [];
    }
  }

  /**
   * Subscribe to user approval status changes
   * This will be used on the client side for real-time updates
   */
  async subscribeToUserApproval(
    userId: string,
    callback: (data: ApprovalStatusUpdate) => void
  ): Promise<() => void> {
    const pb = await this.getPocketBase();

    // Subscribe to specific user record changes
    pb.collection('users').subscribe(userId, callback);

    // Return unsubscribe function
    return () => {
      pb.collection('users').unsubscribe(userId);
    };
  }

  /**
   * Subscribe to all user changes (for admin dashboard)
   */
  async subscribeToAllUsers(
    callback: (data: ApprovalStatusUpdate) => void
  ): Promise<() => void> {
    const pb = await this.getPocketBase();

    // Subscribe to all user record changes
    pb.collection('users').subscribe('*', callback);

    // Return unsubscribe function
    return () => {
      pb.collection('users').unsubscribe('*');
    };
  }

  /**
   * Test connection to PocketBase
   */
  async testConnection(): Promise<{ success: boolean; message: string }> {
    try {
      const pb = await this.getPocketBase();

      // Try to fetch a small list to test connection
      await pb.collection('users').getList(1, 1);

      return {
        success: true,
        message: 'Connection successful'
      };
    } catch (error) {
      console.error('Connection test failed:', error);
      return {
        success: false,
        message: error instanceof Error ? error.message : 'Connection failed'
      };
    }
  }

  /**
   * Get raw PocketBase instance for advanced operations
   */
  async getPocketBaseInstance(): Promise<any> {
    return await this.getPocketBase();
  }
}

/**
 * Factory function to get PocketBase service instance
 */
export function getPocketBaseService(): PocketBaseService {
  return PocketBaseService.getInstance();
}
