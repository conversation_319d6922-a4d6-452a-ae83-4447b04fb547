"use server";

import { currentUser } from "@clerk/nextjs/server";
import { initializeUltimateApp, testPocketBaseConnection } from "@/lib/ultimate-app";
import { revalidatePath } from "next/cache";

/**
 * Test PocketBase connection server action
 */
export async function testConnection() {
  try {
    const result = await testPocketBaseConnection();
    return result;
  } catch (error) {
    console.error("Connection test failed:", error);
    return {
      success: false,
      message: error instanceof Error ? error.message : "Connection test failed",
    };
  }
}

/**
 * Initialize user in the system
 */
export async function initializeUser() {
  try {
    const user = await currentUser();
    if (!user) {
      return {
        success: false,
        message: "User not authenticated",
      };
    }

    const email = user.emailAddresses[0]?.emailAddress;
    if (!email) {
      return {
        success: false,
        message: "User email not available",
      };
    }

    // Initialize the Ultimate app for this user
    const app = await initializeUltimateApp({ email });
    
    return {
      success: true,
      message: "User initialized successfully",
      user: {
        id: user.id,
        email,
        name: `${user.firstName} ${user.lastName}`.trim(),
      },
    };
  } catch (error) {
    console.error("User initialization failed:", error);
    return {
      success: false,
      message: error instanceof Error ? error.message : "User initialization failed",
    };
  }
}

/**
 * Create proxy for user
 */
export async function createUserProxy(data: {
  port: string;
  username?: string;
}) {
  try {
    const user = await currentUser();
    if (!user) {
      return {
        success: false,
        message: "User not authenticated",
      };
    }

    const email = user.emailAddresses[0]?.emailAddress;
    const userName = user.firstName || user.lastName || "user";
    
    if (!email) {
      return {
        success: false,
        message: "User email not available",
      };
    }

    // Generate proxy credentials
    // Format: username:password@ip:port
    const proxyUsername = data.username || userName.toLowerCase();
    const proxyPassword = generateProxyPassword();
    const proxyIp = "*************"; // Default IP as mentioned in requirements
    const proxyPort = data.port;

    const proxyString = `${proxyUsername}:${proxyPassword}@${proxyIp}:${proxyPort}`;

    // TODO: Store proxy in database through Ultimate app
    const app = await initializeUltimateApp({ email });
    
    // For now, we'll return the proxy string
    // Later this will be stored in the database
    
    return {
      success: true,
      message: "Proxy created successfully",
      proxy: {
        username: proxyUsername,
        password: proxyPassword,
        ip: proxyIp,
        port: proxyPort,
        fullString: proxyString,
      },
    };
  } catch (error) {
    console.error("Proxy creation failed:", error);
    return {
      success: false,
      message: error instanceof Error ? error.message : "Proxy creation failed",
    };
  }
}

/**
 * Test proxy connection
 */
export async function testProxyConnection(proxyString: string) {
  try {
    // TODO: Implement actual proxy testing
    // For now, just validate the format
    const proxyRegex = /^[\w]+:[\w]+@[\d.]+:\d+$/;
    if (!proxyRegex.test(proxyString)) {
      return {
        success: false,
        message: "Invalid proxy format. Expected: username:password@ip:port",
      };
    }

    // Simulate proxy test
    await new Promise(resolve => setTimeout(resolve, 1000));

    return {
      success: true,
      message: "Proxy connection successful",
    };
  } catch (error) {
    console.error("Proxy test failed:", error);
    return {
      success: false,
      message: error instanceof Error ? error.message : "Proxy test failed",
    };
  }
}

/**
 * Save exchange credentials
 */
export async function saveExchangeCredentials(data: {
  apiKey: string;
  secretKey: string;
  exchange: string;
}) {
  try {
    const user = await currentUser();
    if (!user) {
      return {
        success: false,
        message: "User not authenticated",
      };
    }

    const email = user.emailAddresses[0]?.emailAddress;
    if (!email) {
      return {
        success: false,
        message: "User email not available",
      };
    }

    // TODO: Implement credential storage through Ultimate app
    const app = await initializeUltimateApp({ email });
    
    // For now, just validate the inputs
    if (!data.apiKey || !data.secretKey || !data.exchange) {
      return {
        success: false,
        message: "All fields are required",
      };
    }

    return {
      success: true,
      message: "Exchange credentials saved successfully",
    };
  } catch (error) {
    console.error("Failed to save exchange credentials:", error);
    return {
      success: false,
      message: error instanceof Error ? error.message : "Failed to save credentials",
    };
  }
}

/**
 * Complete onboarding process
 */
export async function completeOnboarding() {
  try {
    const user = await currentUser();
    if (!user) {
      return {
        success: false,
        message: "User not authenticated",
      };
    }

    // TODO: Mark onboarding as complete in database
    
    revalidatePath("/dashboard");
    
    return {
      success: true,
      message: "Onboarding completed successfully",
    };
  } catch (error) {
    console.error("Failed to complete onboarding:", error);
    return {
      success: false,
      message: error instanceof Error ? error.message : "Failed to complete onboarding",
    };
  }
}

/**
 * Generate a secure proxy password
 */
function generateProxyPassword(): string {
  const crypto = require('crypto');
  return crypto.randomBytes(8).toString('hex');
}
