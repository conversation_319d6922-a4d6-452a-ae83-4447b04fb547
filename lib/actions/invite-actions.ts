"use server";

import { getLoggedInUser } from "@/app/auth/actions";
import { getDBService } from "@/lib/services/db";
import {
  GetUserResult,
  GetUsersResult,
  InviteActionResult,
  InviteUser
} from "@/lib/types/invite";
import { currentUser } from "@clerk/nextjs/server";

/**
 * Check if current user is approved
 */
export async function checkUserApprovalStatus() {
  try {
    const user = await getLoggedInUser();
    if (!user?.email) {
      return {
        success: false,
        message: "User not authenticated",
      };
    }

    console.log("invite code ", user);

    const email = user.email;
    const dbService = await getDBService();

    try {
      const user = await dbService.getUserByEmail(email);

      return {
        success: true,
        approved: user?.approved,
        type: user?.type,
        user: user as unknown as InviteUser,
        message: user?.approved ? "User is approved" : "User approval pending",
      };
    } catch (error) {
      return {
        success: false,
        message: "User not found in database",
      };
    }
  } catch (error) {
    console.error("Failed to check approval status:", error);
    return {
      success: false,
      message: "Failed to check approval status",
    };
  }
}

// Note: approveUser function has been consolidated into admin/invitees/actions.ts
// Use approveInvitee from that module for user approval with proper admin validation

/**
 * Get all pending users (admin action)
 * Note: This is a basic implementation. For admin-specific functionality,
 * use getInvitees from app/admin/invitees/actions.ts which includes proper admin validation
 */
export async function getPendingUsers(): Promise<GetUsersResult> {
  try {
    const clerkUser = await currentUser();
    if (!clerkUser) {
      return {
        success: false,
        message: "Not authenticated",
      };
    }

    const dbService = await getDBService();
    const users = await dbService.pb.collection("users").getFullList({
      filter: "approved = false",
      sort: "-created",
    });

    return {
      success: true,
      users: users as unknown as InviteUser[],
      message: `Found ${users.length} pending users`,
    };
  } catch (error) {
    console.error("Failed to get pending users:", error);
    return {
      success: false,
      message: "Failed to get pending users",
    };
  }
}

/**
 * Get user by invite code (for displaying inviter info)
 */
export async function getUserByInviteCode(
  inviteCode: string
): Promise<GetUserResult> {
  try {
    const dbService = await getDBService();

    try {
      const user = await dbService.getInviteCodeOwner(inviteCode);

      return {
        success: true,
        user: user as unknown as InviteUser,
        message: "User found",
      };
    } catch (error) {
      return {
        success: false,
        message: "User with invite code not found",
      };
    }
  } catch (error) {
    console.error("Failed to get user by invite code:", error);
    return {
      success: false,
      message: "Failed to get user",
    };
  }
}

/**
 * Test PocketBase connection
 */
export async function testPocketBaseConnection(): Promise<InviteActionResult> {
  try {
    const dbService = await getDBService();

    // Test basic connection by trying to get health status
    await dbService.pb.health.check();

    return {
      success: true,
      message: "Connection successful",
    };
  } catch (error) {
    console.error("Connection test failed:", error);
    return {
      success: false,
      message: "Connection test failed",
    };
  }
}
