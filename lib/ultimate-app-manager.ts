/**
 * @deprecated This file is being refactored. Use lib/client/ultimate-client.ts instead.
 * This file will be removed in the next version.
 */

import { initApp } from "@gbozee/ultimate";

/**
 * @deprecated Use UltimateClient from lib/client/ultimate-client.ts instead
 */
class UltimateAppManager {
  private static instance: UltimateAppManager;
  private appCache: Map<string, any> = new Map();
  private initializationPromises: Map<string, Promise<any>> = new Map();

  private constructor() {}

  static getInstance(): UltimateAppManager {
    if (!UltimateAppManager.instance) {
      UltimateAppManager.instance = new UltimateAppManager();
    }
    return UltimateAppManager.instance;
  }

  private getCacheKey(email?: string, salt?: string): string {
    return `${email || 'default'}:${salt || process.env.SALT}`;
  }

  async getApp(payload?: {
    email?: string;
    salt?: string;
  }): Promise<any> {
    const { email, salt } = payload || {};
    const cacheKey = this.getCacheKey(email, salt);

    if (this.appCache.has(cacheKey)) {
      return this.appCache.get(cacheKey);
    }

    if (this.initializationPromises.has(cacheKey)) {
      return this.initializationPromises.get(cacheKey);
    }

    const initPromise = this.initializeApp(email, salt);
    this.initializationPromises.set(cacheKey, initPromise);

    try {
      const app = await initPromise;
      this.appCache.set(cacheKey, app);
      this.initializationPromises.delete(cacheKey);
      return app;
    } catch (error) {
      this.initializationPromises.delete(cacheKey);
      throw error;
    }
  }

  private async initializeApp(email?: string, salt?: string): Promise<any> {
    if (!process.env.NEW_POCKETBASE_HOST || !process.env.NEW_POCKETBASE_EMAIL || !process.env.NEW_POCKETBASE_PASSWORD) {
      throw new Error("NEW PocketBase configuration missing in environment variables");
    }

    try {
      const app = await initApp({
        db: {
          host: process.env.NEW_POCKETBASE_HOST!,
          email: process.env.NEW_POCKETBASE_EMAIL!,
          password: process.env.NEW_POCKETBASE_PASSWORD!,
        },
        email,
        salt: salt || process.env.SALT,
        getCredentials: (_account: string, _exchange: string) => {
          return {
            api_key: "",
            api_secret: "",
            email: "",
          };
        },
      });

      return app;
    } catch (error) {
      console.error("Failed to initialize Ultimate app:", error);
      throw new Error("Failed to initialize Ultimate app");
    }
  }

  clearCache(email?: string, salt?: string): void {
    const cacheKey = this.getCacheKey(email, salt);
    this.appCache.delete(cacheKey);
    this.initializationPromises.delete(cacheKey);
  }

  clearAllCache(): void {
    this.appCache.clear();
    this.initializationPromises.clear();
  }
}

// Export singleton instance
export const ultimateAppManager = UltimateAppManager.getInstance();

/**
 * @deprecated Use UltimateClient.getApp() instead
 */
export async function getUltimateApp(payload?: {
  email?: string;
  salt?: string;
}): Promise<any> {
  return ultimateAppManager.getApp(payload);
}

/**
 * @deprecated Use DatabaseService.testConnection() instead
 */
export async function testPocketBaseConnection(email?: string) {
  try {
    const app = await getUltimateApp({ email: email || "<EMAIL>" });
    const pb = (app as any).app_db.pb;
    await pb.collection('users').getList(1, 1);
    return { success: true, message: "PocketBase connection successful" };
  } catch (error) {
    console.error("PocketBase connection test failed:", error);
    return {
      success: false,
      message: error instanceof Error ? error.message : "Unknown error"
    };
  }
}
