export const SUBSCRIPTION_AMOUNTS = [
  { value: 25, label: "$25", cost: 1000, popular: false },
  { value: 30, label: "$30", cost: 1500, popular: false },
  { value: 40, label: "$40", cost: 2000, popular: true },
  { value: 50, label: "$50", cost: 3000, popular: false },
  { value: 60, label: "$60", cost: 5000, popular: false },
  { value: 70, label: "$70", cost: 6500, popular: false },
  { value: 80, label: "$80", cost: 8000, popular: false },
  { value: 90, label: "$90", cost: 9000, popular: false },
  { value: 100, label: "$100", cost: 10000, popular: false },
] as const;

export const INTEREST_RATES = {
  daily: {
    rate: 30.0,
    label: "30% Daily",
    description: "Daily compounding for 30 days",
    icon: "⚡",
  },
} as const;

export const CRYPTO_NETWORKS = {
  TRC20: {
    name: "TRON (TRC20)",
    symbol: "USDT",
    walletAddress: "TQn9Y2khEsLJW1ChVWFMSMeRDow5KcbLSE",
    logo: "/placeholder.svg?height=32&width=32&text=TRX",
    fees: "$1",
    confirmationTime: "1-5 min",
    recommended: true,
    color: "bg-red-500",
    instructions: [
      "Use TRON (TRC20) network only",
      "Send exactly the amount shown",
      "Keep transaction hash for reference",
    ],
    warnings: ["Do not send TRX or other tokens", "Only use TRC20 network"],
  },
  BEP20: {
    name: "BSC (BEP20)",
    symbol: "USDT",
    walletAddress: "******************************************",
    logo: "/placeholder.svg?height=32&width=32&text=BSC",
    fees: "$0.20",
    confirmationTime: "1-3 min",
    recommended: false,
    color: "bg-yellow-500",
    instructions: [
      "Use BSC (BEP20) network only",
      "Send exactly the amount shown",
      "Keep transaction hash for reference",
    ],
    warnings: ["Do not send BNB or other tokens", "Only use BEP20 network"],
  },
  ERC20: {
    name: "Ethereum (ERC20)",
    symbol: "USDT",
    walletAddress: "******************************************",
    logo: "/placeholder.svg?height=32&width=32&text=ETH",
    fees: "$5-15",
    confirmationTime: "2-10 min",
    recommended: false,
    color: "bg-blue-500",
    instructions: [
      "Use Ethereum (ERC20) network only",
      "Send exactly the amount shown",
      "Keep transaction hash for reference",
    ],
    warnings: [
      "Do not send ETH or other tokens",
      "Only use ERC20 network",
      "Higher fees apply",
    ],
  },
} as const;

export const INVESTMENT_PERIOD = 30; // days

// Types
export type SubscriptionAmount = (typeof SUBSCRIPTION_AMOUNTS)[number];
export type CryptoNetwork = keyof typeof CRYPTO_NETWORKS;
export type InterestRateType = keyof typeof INTEREST_RATES;


export type RegularUserAccount = {
  id: string;
  slug: string;
  status: "active" | "inactive" | "pending" | "completed";
  profitFrequency: string;
  interestRate: number;
  investedAmount: number;
  currentValue: number;
  lastUpdate: string;
  startDate: string;
  investmentPeriod: number;
  interestType: "simple" | "compound";
  exchange?: string;
};

export interface PaymentDetails {
  method: "crypto" | "card";
  network?: CryptoNetwork;
  cardDetails?: {
    number: string;
    expiry: string;
    cvv: string;
    name: string;
  };
}

// Utility functions
export const calculateReturns = (
  amount: number,
  days: number = INVESTMENT_PERIOD
) => {
  const rate = 0.3; // 30% daily
  let currentAmount = amount;

  for (let i = 0; i < days; i++) {
    currentAmount = currentAmount * (1 + rate);
  }

  const selectedAmount = SUBSCRIPTION_AMOUNTS.find((a) => a.value === amount);
  const platformCost = selectedAmount?.cost || 0;

  return {
    projected: currentAmount,
    profit: currentAmount - amount,
    period: `${days} days`,
    platformCost: platformCost,
  };
};
