// constants/landing-page.js
import { BarChart2, Shield, Zap } from "lucide-react";

export const FEATURES = [
  {
    icon: Shield,
    title: "Beginner Friendly",
    description:
      "No prior trading experience required. Our platform guides you through every step.",
  },
  {
    icon: Zap,
    title: "Automated Trading",
    description:
      "Our smart algorithms handle the trading for you based on your preferences.",
  },
  {
    icon: BarChart2,
    title: "BTC/USDT Focus",
    description:
      "We specialize in Bitcoin trading against USDT, simplifying your decision-making process.",
  },
];

export const STEPS = [
  {
    number: 1,
    title: "Sign Up",
    description:
      "Create your account with email or use Google/Apple sign-in.",
  },
  {
    number: 2,
    title: "Connect Binance",
    description: "Link your Binance account to enable automated trading.",
  },
  {
    number: 3,
    title: "Choose Settings",
    description:
      "Select recommended settings or customize your trading strategy.",
  },
  {
    number: 4,
    title: "Start Trading",
    description: "Subscribe to a plan and begin your trading journey.",
  },
];

export const FAQS = [
  {
    question: "Do I need trading experience?",
    answer:
      "No, our platform is specifically designed for beginners with no prior trading experience.",
  },
  {
    question: "How do I connect my Binance account?",
    answer:
      "During the onboarding process, we'll guide you through creating API keys in your Binance account and connecting them to our platform.",
  },
  {
    question: "Can I change my trading settings later?",
    answer:
      "Yes, you can update your trading settings at any time through your account settings page.",
  },
  {
    question: "What if I want to trade more than my plan allows?",
    answer:
      "You can upgrade your subscription plan at any time to increase your monthly trading volume limit.",
  },
  {
    question: "Is my investment safe?",
    answer:
      "We never hold your funds. All trades are executed directly through your Binance account, giving you full control over your assets.",
  },
  {
    question: "Can I cancel my subscription?",
    answer:
      "Yes, you can cancel your subscription at any time. Your subscription will remain active until the end of your current billing period.",
  },
];

export const PLANS = [
  {
    name: "Noob",
    price: 150,
    description: "Perfect for beginners starting their trading journey.",
    features: [
      "Up to $5,000 monthly trading volume",
      "Basic trading strategies",
      "Email support",
    ],
    href: "noob",
  },
  {
    name: "Enthusiast",
    price: 500,
    description: "For traders looking to increase their investment.",
    features: [
      "Up to $25,000 monthly trading volume",
      "Advanced trading strategies",
      "Priority email support",
      "Weekly performance reports",
    ],
    href: "enthusiast",
    popular: true,
  },
  {
    name: "Trader",
    price: 1000,
    description: "For serious traders with larger investment capital.",
    features: [
      "Up to $100,000 monthly trading volume",
      "Premium trading strategies",
      "24/7 dedicated support",
      "Daily performance reports",
      "Custom strategy consultation",
    ],
    href: "trader",
  },
];

// Alternative: Group everything under a single object if you prefer
export const LANDING_PAGE_DATA = {
  features: FEATURES,
  steps: STEPS,
  faqs: FAQS,
  plans: PLANS,
};

// config/site.js - For site-wide constants
export const SITE_CONFIG = {
  name: "TradeSmart",
  description: "Cryptocurrency trading made simple for beginners",
  colors: {
    primary: "#245c1a",
    primaryHover: "#1a4513",
    accent: "#e6f0e4",
  },
  links: {
    dashboard: "/dashboard",
    signup: "/auth/signup",
    pricing: "/pricing",
  },
};

// types/landing-page.ts - TypeScript types (if using TypeScript)
export interface Feature {
  icon: React.ComponentType<{ className?: string }>;
  title: string;
  description: string;
}

export interface Step {
  number: number;
  title: string;
  description: string;
}

export interface FAQ {
  question: string;
  answer: string;
}

export interface Plan {
  name: string;
  price: number;
  description: string;
  features: string[];
  href: string;
  popular?: boolean;
}