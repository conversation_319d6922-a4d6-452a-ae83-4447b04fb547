/**
 * Encryption Utilities
 * Provides secure AES-256-GCM encryption/decryption for sensitive data
 * Based on the pattern from demo.ts with proper TypeScript types and error handling
 */

import {
  createCipheriv,
  createDecipheriv,
  randomBytes,
  scryptSync,
} from "crypto";

export interface EncryptionResult {
  success: boolean;
  data?: string;
  error?: string;
}

export interface DecryptionResult<T = any> {
  success: boolean;
  data?: T;
  error?: string;
}

/**
 * Encrypt an object using AES-256-GCM with password-based key derivation
 * @param obj - The object to encrypt
 * @param password - The password to use for encryption
 * @returns Base64 encoded encrypted string or null on failure
 */
export function encryptObject(obj: any, password: string): EncryptionResult {
  try {
    if (!obj || !password) {
      return {
        success: false,
        error: "Object and password are required"
      };
    }

    const jsonString = JSON.stringify(obj);
    const salt = randomBytes(16);
    
    // Derive a 256-bit (32-byte) key from the password and salt
    const key = scryptSync(password, salt, 32);
    
    // Use a 12-byte IV for GCM, which is recommended for performance and security
    const iv = randomBytes(12);
    const cipher = createCipheriv("aes-256-gcm", key, iv);

    const encrypted = Buffer.concat([
      cipher.update(jsonString, "utf8"),
      cipher.final(),
    ]);
    
    const authTag = cipher.getAuthTag(); // Get the authentication tag (16 bytes for GCM)

    // Prepend salt, iv, and authTag to the encrypted data
    const resultBuffer = Buffer.concat([salt, iv, authTag, encrypted]);
    
    return {
      success: true,
      data: resultBuffer.toString("base64")
    };
  } catch (error) {
    console.error("Encryption failed:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Encryption failed"
    };
  }
}

/**
 * Decrypt an encrypted string using AES-256-GCM
 * @param encryptedString - Base64 encoded encrypted string
 * @param password - The password used for encryption
 * @returns Decrypted object or null on failure
 */
export function decryptObject<T = any>(encryptedString: string, password: string): DecryptionResult<T> {
  try {
    if (!encryptedString || !password) {
      return {
        success: false,
        error: "Encrypted string and password are required"
      };
    }

    const dataBuffer = Buffer.from(encryptedString, "base64");

    // Validate minimum buffer size
    if (dataBuffer.length < 44) { // 16 (salt) + 12 (iv) + 16 (authTag) = 44 minimum
      return {
        success: false,
        error: "Invalid encrypted data format"
      };
    }

    // Extract salt, iv, authTag, and encrypted data
    const salt = dataBuffer.subarray(0, 16);
    const iv = dataBuffer.subarray(16, 28); // 12 bytes IV
    const authTag = dataBuffer.subarray(28, 44); // 16 bytes authTag
    const encrypted = dataBuffer.subarray(44);

    // Derive the key using the same salt
    const key = scryptSync(password, salt, 32);

    const decipher = createDecipheriv("aes-256-gcm", key, iv);
    decipher.setAuthTag(authTag); // Set the authentication tag

    const decrypted = Buffer.concat([
      decipher.update(encrypted),
      decipher.final(), // Throws error if authentication fails (e.g., wrong password or tampered data)
    ]);

    const jsonString = decrypted.toString("utf8");
    const parsedData = JSON.parse(jsonString);
    
    return {
      success: true,
      data: parsedData
    };
  } catch (error) {
    console.error("Decryption failed:", error);
    
    // Provide more specific error messages
    let errorMessage = "Decryption failed";
    if (error instanceof Error) {
      if (error.message.includes("auth")) {
        errorMessage = "Invalid password or corrupted data";
      } else if (error.message.includes("JSON")) {
        errorMessage = "Invalid data format";
      } else {
        errorMessage = error.message;
      }
    }
    
    return {
      success: false,
      error: errorMessage
    };
  }
}

/**
 * Validate password strength
 * @param password - Password to validate
 * @returns Validation result with success status and message
 */
export function validatePasswordStrength(password: string): { success: boolean; message: string } {
  if (!password) {
    return { success: false, message: "Password is required" };
  }
  
  if (password.length < 8) {
    return { success: false, message: "Password must be at least 8 characters long" };
  }
  
  if (password.length > 128) {
    return { success: false, message: "Password must be less than 128 characters" };
  }
  
  // Check for at least one number, one letter
  const hasNumber = /\d/.test(password);
  const hasLetter = /[a-zA-Z]/.test(password);
  
  if (!hasNumber || !hasLetter) {
    return { 
      success: false, 
      message: "Password must contain at least one letter and one number" 
    };
  }
  
  return { success: true, message: "Password is strong" };
}
