/**
 * Ultimate Service Layer
 * Orchestrates Ultimate client and database operations
 * Provides clean business logic interface for API layer
 */

import { getApp, getUltimateClient, UltimateClient, } from '../client/ultimate-client';
import { PocketBaseUser, UserSettings, Proxy, ExchangeAccount } from '../database/types';
import { app as pApp, ExchangeType } from '@gbozee/ultimate';
import { User as ClerkUser } from "@clerk/nextjs/server";
import { generateTempPassword } from '../utils/password';

// Legacy interface for backward compatibility
export interface AppUser {
  id: string;
  email: string;
  emailVisibility: boolean;
  verified: boolean;
  name: string;
  avatar: string;
  settings: string; // JSON string containing encrypted credentials and other settings
  createdAt: string;
  updatedAt: string;
}

export interface ServiceResponse<T> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
}

export interface CreateUserRequest {
  email: string;
  name: string;
}

export interface AddCredentialRequest {
  name: string;
  email: string;
  exchange: string;
  api_key: string;
  api_secret: string;
}

export interface CreateProxyRequest {
  ip_address: string;
  type: 'http' | 'socks5';
  userId: string;
}

export interface CreateExchangeAccountRequest {
  exchange: string;
  owner: string;
  email: string;
  proxy?: string;
  userId: string;
}

export class UltimateService {
  private userEmail?: string;

  constructor(userEmail?: string) {
    this.userEmail = userEmail;

  }

  /**
   * Get Ultimate client instance
   */
  private async getUltimateClient(): Promise<pApp.App | null> {
    if (!this.userEmail) {
      return null
    }
    const app = await getApp({ email: this.userEmail });

    return app;
  }

  private async getExchangeAccount() {
    const app = await this.getUltimateClient();
    const user = await app?.app_db.getUserByEmail();
    const exchange_instance = await app?.app_db.pb.collection('exchange_accounts').getFirstListItem(`user = "${user?.id}"`) as ExchangeType;
    if (!exchange_instance) {
      return null
    }
    const exchange_account = await app?.getExchangeAccount(exchange_instance);
    return exchange_account;
  }

  /**
   * Test connection to both Ultimate app and database
   */
  async syncUserFromClerk(clerkUser: ClerkUser) {
    const userData = {
      email: clerkUser.emailAddresses[0]?.emailAddress || "",
      name: `${clerkUser.firstName || ""} ${clerkUser.lastName || ""}`.trim() || "User",
    };

    try {
      // Try to get existing user first by email (our unique identifier)
      const existingUser = await this.getUserByEmail(userData.email);

      if (existingUser) {
        // User already exists, just return it
        return existingUser;
      } else {
        // Create new user
        return await this.createUser(userData);
      }
    } catch (error) {
      console.error("Error syncing user from Clerk:", error);
      throw error;
    }
  }


  async testConnection() {
    try {
      const exchange_account = await this.getExchangeAccount();
      const test_call = await exchange_account?.exchange.getAllOpenSymbols();
      const ultimateTest = {
        success: !!test_call,
        message: test_call ? "Ultimate connection successful" : "Ultimate connection failed",
      };
      return ultimateTest;


    } catch (error) {
      return {
        success: false,
        message: error instanceof Error ? error.message : "Connection test failed",
      };
    }
  }

  /**
   * User operations
   */
  async getUserByEmail(email: string): Promise<ServiceResponse<PocketBaseUser | null>> {
    try {
      const app = await this.getUltimateClient();
      if (!app) {
        return {
          success: false,
          message: "Failed to initialize Ultimate client",
        };
      }

      const user = await app.app_db.getUserByEmail();
      return {
        success: true,
        data: user as unknown as PocketBaseUser,
      };
    } catch (error) {
      return {
        success: false,
        message: error instanceof Error ? error.message : "Failed to get user",
      };
    }
  }

  async createUser(userData: {
    email: string;
    name: string;
  }): Promise<ServiceResponse<PocketBaseUser>> {
    try {
      // First create user in database
      const app = await this.getUltimateClient();
      if (!app) {
        return {
          success: false,
          message: "Failed to initialize Ultimate client",
        };
      }

      console.log(userData,"userData")
      const pb = app.app_db.pb;

      // Generate a temporary password for PocketBase user creation
      const tempPassword = generateTempPassword();

      const result = await pb.collection("users").create({
        email: userData.email,
        name: userData.name,
        emailVisibility: true,
        verified: true,
        settings: { hasCompletedOnboarding: false }, // Initialize with hasCompletedOnboarding set to false
        password: tempPassword,
        passwordConfirm: tempPassword,
      });

      // Generate password using Ultimate app (same pattern as before)
      const client = await getUltimateClient({ email: userData.email });
      await client.generateUserPassword();

      // Get updated user with password
      const updatedUser = await client.getUserByEmail();

      return {
        success: true,
        data: {
          ...result,
          settings: updatedUser.settings || result.settings,
        } as unknown as PocketBaseUser,
        message: "User created successfully",
      };
    } catch (error) {
      console.log(error,"error")
      return {
        success: false,
        message: error instanceof Error ? error.message : "Failed to create user",
      };
    }
  }

  async updateUser(userId: string, updates: Partial<PocketBaseUser>): Promise<ServiceResponse<PocketBaseUser>> {
    try {
      const app = await this.getUltimateClient();
      if (!app) {
        return {
          success: false,
          message: "Failed to initialize Ultimate client",
        };
      }

      const pb = app.app_db.pb;
      const updateData: any = {
        updated: new Date().toISOString(),
      };

      // Map updates to PocketBase fields
      if (updates.email !== undefined) updateData.email = updates.email;
      if (updates.emailVisibility !== undefined) updateData.emailVisibility = updates.emailVisibility;
      if (updates.verified !== undefined) updateData.verified = updates.verified;
      if (updates.name !== undefined) updateData.name = updates.name;
      if (updates.avatar !== undefined) updateData.avatar = updates.avatar;
      if (updates.settings !== undefined) updateData.settings = updates.settings;

      const updatedUser = await pb.collection('users').update(userId, updateData);

      return {
        success: true,
        data: updatedUser as unknown as PocketBaseUser,
        message: "User updated successfully",
      };
    } catch (error) {
      return {
        success: false,
        message: error instanceof Error ? error.message : "Failed to update user",
      };
    }
  }

  async updateUserSettings(
    userId: string,
    settings: UserSettings
  ): Promise<ServiceResponse<PocketBaseUser>> {
    return await this.updateUser(userId, { settings: JSON.stringify(settings) });
  }

  /**
   * Credential operations
   */
  async addCredential(request: AddCredentialRequest): Promise<ServiceResponse<any>> {
    try {
      const client = await getUltimateClient({ email: request.email });
      const result = await client.addNewCredential({
        payload: request,
      });

      return {
        success: true,
        data: result,
        message: "Credential added successfully",
      };
    } catch (error) {
      return {
        success: false,
        message: error instanceof Error ? error.message : "Failed to add credential",
      };
    }
  }

  async getUserCredentials(email: string): Promise<ServiceResponse<any[]>> {
    try {
      const client = await getUltimateClient({ email });
      const credentials = await client.getUserCredentials();

      return {
        success: true,
        data: credentials,
      };
    } catch (error) {
      return {
        success: false,
        message: error instanceof Error ? error.message : "Failed to get credentials",
      };
    }
  }

  /**
   * Proxy operations
   */
  async getProxiesByUser(userId: string): Promise<ServiceResponse<Proxy[]>> {
    try {
      const app = await this.getUltimateClient();
      if (!app) {
        return {
          success: false,
          message: "Failed to initialize Ultimate client",
        };
      }

      const pb = app.app_db.pb;
      const proxies = await pb.collection('proxies').getFullList({
        filter: `user = "${userId}"`,
      });

      return {
        success: true,
        data: proxies as unknown as Proxy[],
      };
    } catch (error) {
      return {
        success: false,
        message: error instanceof Error ? error.message : "Failed to get proxies",
      };
    }
  }

  async createProxy(request: CreateProxyRequest): Promise<ServiceResponse<Proxy>> {
    try {
      const app = await this.getUltimateClient();
      if (!app) {
        return {
          success: false,
          message: "Failed to initialize Ultimate client",
        };
      }

      const pb = app.app_db.pb;
      const proxyData = {
        ip_address: request.ip_address,
        type: request.type,
        user: request.userId,
      };

      const proxy = await pb.collection('proxies').create(proxyData);

      return {
        success: true,
        data: proxy as unknown as Proxy,
        message: "Proxy created successfully",
      };
    } catch (error) {
      return {
        success: false,
        message: error instanceof Error ? error.message : "Failed to create proxy",
      };
    }
  }

  /**
   * Exchange account operations
   */
  async getExchangeAccountsByUser(userId: string): Promise<ServiceResponse<ExchangeAccount[]>> {
    try {
      const app = await this.getUltimateClient();
      if (!app) {
        return {
          success: false,
          message: "Failed to initialize Ultimate client",
        };
      }

      const pb = app.app_db.pb;
      const accounts = await pb.collection('exchange_accounts').getFullList({
        filter: `user = "${userId}"`,
        expand: 'proxy',
      });

      return {
        success: true,
        data: accounts as unknown as ExchangeAccount[],
      };
    } catch (error) {
      return {
        success: false,
        message: error instanceof Error ? error.message : "Failed to get exchange accounts",
      };
    }
  }

  async createExchangeAccount(request: CreateExchangeAccountRequest): Promise<ServiceResponse<ExchangeAccount>> {
    try {
      const app = await this.getUltimateClient();
      if (!app) {
        return {
          success: false,
          message: "Failed to initialize Ultimate client",
        };
      }

      const pb = app.app_db.pb;
      const accountData = {
        exchange: request.exchange,
        owner: request.owner,
        email: request.email,
        proxy: request.proxy,
        user: request.userId,
      };

      const account = await pb.collection('exchange_accounts').create(accountData);

      return {
        success: true,
        data: account as unknown as ExchangeAccount,
        message: "Exchange account created successfully",
      };
    } catch (error) {
      return {
        success: false,
        message: error instanceof Error ? error.message : "Failed to create exchange account",
      };
    }
  }

  async updateExchangeAccount(accountId: string, updates: Partial<ExchangeAccount>): Promise<ServiceResponse<ExchangeAccount>> {
    try {
      const app = await this.getUltimateClient();
      if (!app) {
        return {
          success: false,
          message: "Failed to initialize Ultimate client",
        };
      }

      const pb = app.app_db.pb;
      const account = await pb.collection('exchange_accounts').update(accountId, updates);

      return {
        success: true,
        data: account as unknown as ExchangeAccount,
        message: "Exchange account updated successfully",
      };
    } catch (error) {
      return {
        success: false,
        message: error instanceof Error ? error.message : "Failed to update exchange account",
      };
    }
  }

  // async getExchangeAccount(owner: string, exchange: string): Promise<ServiceResponse<any>> {
  //   try {
  //     const client = await this.getUltimateClient();
  //     const account = await client.getExchangeAccount({ owner, exchange });

  //     return {
  //       success: true,
  //       data: account,
  //     };
  //   } catch (error) {
  //     return {
  //       success: false,
  //       message: error instanceof Error ? error.message : "Failed to get exchange account",
  //     };
  //   }
  // }

  async getAllOpenSymbols(owner: string, exchange: string): Promise<ServiceResponse<any>> {
    try {
      const app = await this.getUltimateClient();
      if (!app) {
        return {
          success: false,
          message: "Failed to initialize Ultimate client",
        };
      }

      const exchangeAccount = await app.getExchangeAccount({ owner, exchange });
      const symbols = await exchangeAccount?.exchange.getAllOpenSymbols();

      return {
        success: true,
        data: symbols,
      };
    } catch (error) {
      return {
        success: false,
        message: error instanceof Error ? error.message : "Failed to get open symbols",
      };
    }
  }

  /**
   * Additional user management methods from UserService
   */
  async getUserUltimateApp(userEmail: string, userSalt?: string): Promise<any> {
    try {
      const app = await getApp({
        email: userEmail,
        salt: userSalt || process.env.SALT,
      });
      return app;
    } catch (error) {
      console.error("Error getting user Ultimate app:", error);
      throw error;
    }
  }

  async addNewCredentials(params: {
    payload: {
      name: string;
      email: string;
      exchange: string;
      api_key: string;
      api_secret: string;
    }
  }): Promise<ServiceResponse<any>> {
    try {
      const app = await getApp({ email: params.payload.email });
      const result = await app.app_db.addNewCredential(params);

      return {
        success: true,
        data: result,
        message: "Credentials added successfully",
      };
    } catch (error) {
      return {
        success: false,
        message: error instanceof Error ? error.message : "Failed to add credentials",
      };
    }
  }

  /**
   * Complete onboarding workflow
   */
  async completeOnboarding(
    email: string,
    credentials: AddCredentialRequest
  ): Promise<ServiceResponse<{ user: PocketBaseUser; credential: any }>> {
    try {
      // Ensure user has password
      const client = await getUltimateClient({ email });
      const user = await client.getUserByEmail();

      if (!user) {
        throw new Error("User not found");
      }

      const settings: UserSettings = JSON.parse(user.settings || '{}');
      if (!settings.password) {
        await client.generateUserPassword();
      }

      // Add credentials
      const credentialResult = await this.addCredential(credentials);
      if (!credentialResult.success) {
        return credentialResult;
      }

      // Get updated user
      const updatedUser = await client.getUserByEmail();

      return {
        success: true,
        data: {
          user: updatedUser,
          credential: credentialResult.data,
        },
        message: "Onboarding completed successfully",
      };
    } catch (error) {
      return {
        success: false,
        message: error instanceof Error ? error.message : "Failed to complete onboarding",
      };
    }
  }

  /**
   * Transform PocketBaseUser to AppUser for backward compatibility
   */
  public transformToAppUser(user: PocketBaseUser): AppUser {
    return {
      id: user.id,
      email: user.email,
      emailVisibility: user.emailVisibility,
      verified: user.verified,
      name: user.name,
      avatar: user.avatar || "",
      settings: user.settings || JSON.stringify({}),
      createdAt: user.created,
      updatedAt: user.updated,
    };
  }

  /**
   * Legacy UserService compatibility methods
   */
  async createUserLegacy(userData: {
    email: string;
    name: string;
  }): Promise<AppUser> {
    const result = await this.createUser(userData);
    console.log(result,"result")
    if (!result.success || !result.data) {
      throw new Error(result.message || "Failed to create user");
    }
    return this.transformToAppUser(result.data);
  }

  async getUserByEmailLegacy(email: string): Promise<AppUser | null> {
    const result = await this.getUserByEmail(email);
    if (!result.success || !result.data) {
      return null;
    }
    return this.transformToAppUser(result.data);
  }

  async updateUserLegacy(userId: string, updates: Partial<AppUser>): Promise<AppUser> {
    const result = await this.updateUser(userId, updates as Partial<PocketBaseUser>);
    if (!result.success || !result.data) {
      throw new Error(result.message || "Failed to update user");
    }
    return this.transformToAppUser(result.data);
  }

  async completeOnboardingLegacy(email: string, credentials: {
    name: string;
    exchange: string;
    api_key: string;
    api_secret: string;
  }): Promise<AppUser> {
    try {
      const app = await getApp({ email });

      // First ensure user has a password
      const user = await app.app_db.getUserByEmail();
      if (!user) {
        throw new Error("User not found");
      }

      const settings: UserSettings = JSON.parse(user.settings || '{}');
      if (!settings.password) {
        await app.app_db.generateUserPassword();
      }

      // Add the credentials
      await app.app_db.addNewCredential({
        payload: {
          name: credentials.name,
          email: credentials.api_key, // This might be the binance email
          exchange: credentials.exchange,
          api_key: credentials.api_key,
          api_secret: credentials.api_secret,
        }
      });

      // Get the updated user
      const updatedUser = await app.app_db.getUserByEmail();

      // Update hasCompletedOnboarding to true
      const appUser = this.transformToAppUser(updatedUser as unknown as PocketBaseUser);
      const currentSettings: UserSettings = typeof appUser.settings === 'string'
        ? JSON.parse(appUser.settings || '{}')
        : appUser.settings || {};

      const finalSettings: UserSettings = {
        ...currentSettings,
        hasCompletedOnboarding: true,
      };

      // Update the user with completed onboarding status
      const finalUpdateResult = await this.updateUserSettings(appUser.id, finalSettings);
      if (finalUpdateResult.success && finalUpdateResult.data) {
        return this.transformToAppUser(finalUpdateResult.data);
      }

      // Fallback to the user without the update if it fails
      return appUser;
    } catch (error) {
      console.error("Error completing onboarding:", error);
      throw error;
    }
  }

  /**
   * Ensure user has hasCompletedOnboarding field in settings
   */
  async ensureOnboardingStatus(user: AppUser): Promise<AppUser> {
    try {
      // Parse settings if it's a string, otherwise use as object
      let settings: UserSettings;
      if (typeof user.settings === 'string') {
        settings = JSON.parse(user.settings || '{}');
      } else {
        settings = user.settings || {};
      }

      // If hasCompletedOnboarding is not set, determine it and update the user
      if (settings.hasCompletedOnboarding === undefined) {
        // Calculate the onboarding status based on existing logic
        const hasCompleted = !!(settings.password && settings.credentials);

        // Update the settings with the hasCompletedOnboarding field
        const updatedSettings: UserSettings = {
          ...settings,
          hasCompletedOnboarding: hasCompleted,
        };

        // Update the user in the database
        const updateResult = await this.updateUserSettings(user.id, updatedSettings);
        if (updateResult.success && updateResult.data) {
          return this.transformToAppUser(updateResult.data);
        }
      }

      return user;
    } catch (error) {
      console.error("Error ensuring onboarding status:", error);
      return user;
    }
  }
}

/**
 * Check if user has completed onboarding based on settings
 */
export function hasCompletedOnboarding(user: AppUser): boolean {
  try {
    // Parse settings if it's a string, otherwise use as object
    let settings: UserSettings;
    if (typeof user.settings === 'string') {
      settings = JSON.parse(user.settings || '{}');
    } else {
      settings = user.settings || {};
    }

    // First check if hasCompletedOnboarding is explicitly set
    if (settings.hasCompletedOnboarding !== undefined) {
      return settings.hasCompletedOnboarding;
    }

    // Fallback to the old logic if not set
    return !!(settings.password && settings.credentials);
  } catch {
    return false;
  }
}

/**
 * Check if user has completed onboarding using Ultimate service
 */
export async function hasCompletedOnboardingAsync(email: string, salt?: string): Promise<boolean> {
  try {
    const service = createUltimateService(email);

    // Check if user has password in settings
    const userResult = await service.getUserByEmail(email);
    if (!userResult.success || !userResult.data || !userResult.data.settings) {
      return false;
    }

    const settings: UserSettings = JSON.parse(userResult.data.settings || '{}');
    if (!settings.password) {
      return false;
    }

    // Check if user has credentials
    const credentialsResult = await service.getUserCredentials(email);
    return !!(credentialsResult.success && credentialsResult.data && Array.isArray(credentialsResult.data));
  } catch {
    return false;
  }
}

/**
 * Factory function to create service instance
 */
export function createUltimateService(userEmail?: string): UltimateService {
  return new UltimateService(userEmail);
}
