/**
 * Dual Database Service
 * Manages connections to both old and new PocketBase instances
 * Ensures users are created in both databases for seamless migration
 */

import { UltimateClient } from '../client/ultimate-client';
import { getDBService } from './db';
import { PocketBaseUser, UserSettings } from '../database/types';
import { generateTempPassword } from '../utils/password';

export interface DualDatabaseConfig {
  userEmail: string;
  salt?: string;
}

export interface CreateUserData {
  email: string;
  name: string;
  emailVisibility?: boolean;
  verified?: boolean;
  avatar?: string;
  settings?: UserSettings;
  invite_code?: string;
  approved?: boolean;
}

export interface DatabaseResult<T> {
  success: boolean;
  data?: T;
  error?: string;
}

export class DualDatabaseService {
  private ultimateClient: UltimateClient | null = null;
  private newDbService: any = null;
  private config: DualDatabaseConfig;

  constructor(config: DualDatabaseConfig) {
    this.config = config;
  }

  /**
   * Initialize connections to both databases
   */
  private async initializeConnections(): Promise<void> {
    try {
      // Initialize Ultimate client (old database)
      if (!this.ultimateClient) {
        this.ultimateClient = await UltimateClient.getInstance({
          email: this.config.userEmail,
          salt: this.config.salt || process.env.SALT
        });
      }

      // Initialize new database service
      if (!this.newDbService) {
        this.newDbService = await getDBService();
      }
    } catch (error) {
      console.error('Failed to initialize database connections:', error);
      throw new Error('Database initialization failed');
    }
  }

  /**
   * Get Ultimate client (old database)
   */
  private async getOldDatabase(): Promise<any> {
    await this.initializeConnections();
    if (!this.ultimateClient) {
      throw new Error('Ultimate client not initialized');
    }
    const app = this.ultimateClient.getApp();
    return app.app_db.pb;
  }

  /**
   * Get new database service
   */
  private async getNewDatabase(): Promise<any> {
    await this.initializeConnections();
    if (!this.newDbService) {
      throw new Error('New database service not initialized');
    }
    return this.newDbService.pb;
  }

  /**
   * Create user in old database (Ultimate)
   */
  private async createUserInOldDb(userData: CreateUserData): Promise<PocketBaseUser> {
    const oldDb = await this.getOldDatabase();
    const tempPassword = generateTempPassword();

    const userRecord = {
      email: userData.email,
      name: userData.name,
      emailVisibility: userData.emailVisibility ?? true,
      verified: userData.verified ?? true,
      avatar: userData.avatar ?? "",
      settings: userData.settings ?? { hasCompletedOnboarding: false },
      password: tempPassword,
      passwordConfirm: tempPassword,
    };

    return await oldDb.collection("users").create(userRecord);
  }

  /**
   * Create user in new database (Invite flow)
   */
  private async createUserInNewDb(userData: CreateUserData): Promise<PocketBaseUser> {
    const newDb = await this.getNewDatabase();
    const tempPassword = generateTempPassword();

    const userRecord = {
      email: userData.email,
      name: userData.name,
      emailVisibility: userData.emailVisibility ?? true,
      verified: userData.verified ?? true,
      avatar: userData.avatar ?? "",
      settings: userData.settings ?? { hasCompletedOnboarding: false },
      invite_code: userData.invite_code ?? "",
      approved: userData.approved ?? false,
      password: tempPassword,
      passwordConfirm: tempPassword,
    };

    return await newDb.collection("users").create(userRecord);
  }

  /**
   * Create user in both databases
   */
  async createUserInBothDatabases(userData: CreateUserData): Promise<{
    oldDb: DatabaseResult<PocketBaseUser>;
    newDb: DatabaseResult<PocketBaseUser>;
  }> {
    const results = {
      oldDb: { success: false } as DatabaseResult<PocketBaseUser>,
      newDb: { success: false } as DatabaseResult<PocketBaseUser>
    };

    // Create in old database
    try {
      const oldUser = await this.createUserInOldDb(userData);
      results.oldDb = { success: true, data: oldUser };
    } catch (error) {
      console.error('Failed to create user in old database:', error);
      results.oldDb = { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      };
    }

    // Create in new database
    try {
      const newUser = await this.createUserInNewDb(userData);
      results.newDb = { success: true, data: newUser };
    } catch (error) {
      console.error('Failed to create user in new database:', error);
      let errorMessage = 'Unknown error';

      if (error instanceof Error) {
        errorMessage = error.message;
        // Check if it's a PocketBase error with more details
        if ('response' in error && error.response && typeof error.response === 'object') {
          const response = error.response as any;
          if (response.data && response.data.email) {
            errorMessage = `Email validation error: ${JSON.stringify(response.data.email)}`;
          } else if (response.message) {
            errorMessage = response.message;
          }
        }
      }

      results.newDb = {
        success: false,
        error: errorMessage
      };
    }

    return results;
  }

  /**
   * Get user from old database
   */
  async getUserFromOldDb(email: string): Promise<PocketBaseUser | null> {
    try {
      const oldDb = await this.getOldDatabase();
      return await oldDb.collection("users").getFirstListItem(`email = "${email}"`);
    } catch (error) {
      return null;
    }
  }

  /**
   * Get user from new database
   */
  async getUserFromNewDb(email: string): Promise<PocketBaseUser | null> {
    try {
      const newDb = await this.getNewDatabase();
      return await newDb.collection("users").getFirstListItem(`email = "${email}"`);
    } catch (error) {
      return null;
    }
  }

  /**
   * Update user in old database
   */
  async updateUserInOldDb(userId: string, updates: Partial<PocketBaseUser>): Promise<DatabaseResult<PocketBaseUser>> {
    try {
      const oldDb = await this.getOldDatabase();
      const updatedUser = await oldDb.collection("users").update(userId, updates);
      return { success: true, data: updatedUser };
    } catch (error) {
      return { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      };
    }
  }

  /**
   * Update user in new database
   */
  async updateUserInNewDb(userId: string, updates: Partial<PocketBaseUser>): Promise<DatabaseResult<PocketBaseUser>> {
    try {
      const newDb = await this.getNewDatabase();
      const updatedUser = await newDb.collection("users").update(userId, updates);
      return { success: true, data: updatedUser };
    } catch (error) {
      return { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      };
    }
  }

  /**
   * Sync user from old database to new database
   */
  async syncUserToNewDb(email: string): Promise<DatabaseResult<PocketBaseUser>> {
    try {
      const oldUser = await this.getUserFromOldDb(email);
      if (!oldUser) {
        return { success: false, error: 'User not found in old database' };
      }

      const existingNewUser = await this.getUserFromNewDb(email);
      if (existingNewUser) {
        return { success: true, data: existingNewUser };
      }

      // Create user in new database based on old database data
      const userData: CreateUserData = {
        email: oldUser.email,
        name: oldUser.name,
        emailVisibility: oldUser.emailVisibility,
        verified: oldUser.verified,
        avatar: oldUser.avatar,
        settings: typeof oldUser.settings === 'string' 
          ? JSON.parse(oldUser.settings) 
          : oldUser.settings,
        approved: false // New users need approval in invite flow
      };

      const newUser = await this.createUserInNewDb(userData);
      return { success: true, data: newUser };
    } catch (error) {
      return { 
        success: false, 
        error: error instanceof Error ? error.message : 'Failed to sync user' 
      };
    }
  }

  /**
   * Get the appropriate database for invite flow operations
   */
  async getInviteFlowDatabase(): Promise<any> {
    return await this.getNewDatabase();
  }

  /**
   * Get the appropriate database for main app operations
   */
  async getMainAppDatabase(): Promise<any> {
    return await this.getOldDatabase();
  }
}

/**
 * Factory function to create dual database service
 */
export function createDualDatabaseService(userEmail: string, salt?: string): DualDatabaseService {
  return new DualDatabaseService({ userEmail, salt });
}
