"use server";

import { getDBService } from '@/lib/services/db';
import type { 
  Invitation, 
  CreateInvitationRequest, 
  InvitationWithDetails,
  InviteUser 
} from '@/lib/types/invite';

/**
 * Service for managing invitations table and admin-invitee relationships
 */
export class InvitationService {
  
  /**
   * Create an invitation record when admin generates an invite code
   */
  static async createInvitation(data: CreateInvitationRequest): Promise<Invitation> {
    const dbService = await getDBService();

    // Verify that the admin_id refers to a user with admin role
    const adminUser = await dbService.pb.collection('users').getOne(data.admin_id);
    if (adminUser.role !== 'admin') {
      throw new Error('Invalid admin: User does not have admin role');
    }

    // Verify that the invitee_id refers to a user with user role
    const inviteeUser = await dbService.pb.collection('users').getOne(data.invitee_id);
    if (inviteeUser.role !== 'user') {
      throw new Error('Invalid invitee: User does not have user role');
    }

    const invitation = await dbService.pb.collection('invitations').create({
      admin: data.admin_id,
      invitee: data.invitee_id,
      invite_code: data.invite_code,
      status: 'pending',
      invited_at: new Date().toISOString()
    });

    return invitation as unknown as Invitation;
  }

  /**
   * Create invitation record when user signs up with invite code
   */
  static async createInvitationOnSignup(adminId: string, inviteeId: string, inviteCode: string): Promise<Invitation> {
    const dbService = await getDBService();

    // Verify that the admin_id refers to a user with admin role
    const adminUser = await dbService.pb.collection('users').getOne(adminId);
    if (adminUser.role !== 'admin') {
      throw new Error('Invalid admin: User does not have admin role');
    }

    // Verify that the invitee_id refers to a user with user role
    const inviteeUser = await dbService.pb.collection('users').getOne(inviteeId);
    if (inviteeUser.role !== 'user') {
      throw new Error('Invalid invitee: User does not have user role');
    }

    const invitation = await dbService.pb.collection('invitations').create({
      admin: adminId,
      invitee: inviteeId,
      invite_code: inviteCode,
      status: 'pending',
      invited_at: new Date().toISOString()
    });

    return invitation as unknown as Invitation;
  }

  /**
   * Get all invitations for a specific admin
   */
  static async getInvitationsByAdmin(adminId: string): Promise<InvitationWithDetails[]> {
    const dbService = await getDBService();
    
    const invitations = await dbService.pb.collection('invitations').getFullList({
      filter: `admin = "${adminId}"`,
      expand: 'invitee',
      sort: '-created'
    });
    
    return invitations as unknown as InvitationWithDetails[];
  }

  /**
   * Get all pending invitations for a specific admin
   */
  static async getPendingInvitationsByAdmin(adminId: string): Promise<InvitationWithDetails[]> {
    const dbService = await getDBService();
    
    const invitations = await dbService.pb.collection('invitations').getFullList({
      filter: `admin = "${adminId}" && status = "pending"`,
      expand: 'invitee',
      sort: '-created'
    });
    
    return invitations as unknown as InvitationWithDetails[];
  }

  /**
   * Update invitation status when admin approves/rejects
   */
  static async updateInvitationStatus(
    invitationId: string, 
    status: 'approved' | 'rejected'
  ): Promise<Invitation> {
    const dbService = await getDBService();
    
    const invitation = await dbService.pb.collection('invitations').update(invitationId, {
      status,
      responded_at: new Date().toISOString()
    });
    
    return invitation as unknown as Invitation;
  }

  /**
   * Find invitation by invitee ID
   */
  static async getInvitationByInvitee(inviteeId: string): Promise<Invitation | null> {
    const dbService = await getDBService();
    
    try {
      const invitation = await dbService.pb.collection('invitations').getFirstListItem(
        `invitee = "${inviteeId}"`
      );
      return invitation as unknown as Invitation;
    } catch (error) {
      return null;
    }
  }

  /**
   * Find admin by invite code (with role verification)
   */
  static async getAdminByInviteCode(inviteCode: string): Promise<InviteUser | null> {
    const dbService = await getDBService();

    try {
      // Explicitly filter by role = "admin" to ensure only admins are returned
      const admin = await dbService.pb.collection('users').getFirstListItem(
        `invite_code = "${inviteCode}" && role = "admin"`
      );

      // Double-check the role for extra security
      if (admin.role !== 'admin') {
        console.warn(`User ${admin.email} has invite code but is not admin (role: ${admin.role})`);
        return null;
      }

      return admin as any;
    } catch (error) {
      return null;
    }
  }

  /**
   * Get invitation statistics for an admin
   */
  static async getInvitationStats(adminId: string): Promise<{
    total: number;
    pending: number;
    approved: number;
    rejected: number;
  }> {
    const dbService = await getDBService();
    
    const [total, pending, approved, rejected] = await Promise.all([
      dbService.pb.collection('invitations').getList(1, 1, {
        filter: `admin = "${adminId}"`
      }).then(result => result.totalItems),
      
      dbService.pb.collection('invitations').getList(1, 1, {
        filter: `admin = "${adminId}" && status = "pending"`
      }).then(result => result.totalItems),
      
      dbService.pb.collection('invitations').getList(1, 1, {
        filter: `admin = "${adminId}" && status = "approved"`
      }).then(result => result.totalItems),
      
      dbService.pb.collection('invitations').getList(1, 1, {
        filter: `admin = "${adminId}" && status = "rejected"`
      }).then(result => result.totalItems)
    ]);
    
    return { total, pending, approved, rejected };
  }

  /**
   * Bulk approve invitations
   */
  static async bulkApproveInvitations(invitationIds: string[]): Promise<{
    approved: number;
    errors: string[];
  }> {
    const dbService = await getDBService();
    let approved = 0;
    const errors: string[] = [];
    
    for (const invitationId of invitationIds) {
      try {
        await dbService.pb.collection('invitations').update(invitationId, {
          status: 'approved',
          responded_at: new Date().toISOString()
        });
        approved++;
      } catch (error) {
        errors.push(`Failed to approve invitation ${invitationId}`);
      }
    }
    
    return { approved, errors };
  }
}
