import { User as ClerkUser } from "@clerk/nextjs/server";
import { createUltimateService } from "./ultimate-service";
import { PocketBaseUser, UserSettings } from "../database/types";
import { getUltimateApp } from "../ultimate-app-manager";

// Legacy interface for backward compatibility
export interface AppUser {
  id: string;
  email: string;
  emailVisibility: boolean;
  verified: boolean;
  name: string;
  avatar: string;
  settings: string; // JSON string containing encrypted credentials and other settings
  createdAt: string;
  updatedAt: string;
}

// Re-export for backward compatibility
export type { UserSettings };

/**
 * Check if user has completed onboarding based on settings
 */
export function hasCompletedOnboarding(user: AppUser): boolean {
  try {
    const settings: UserSettings = JSON.parse(user.settings || '{}');
    return !!(settings.password && settings.credentials);
  } catch {
    return false;
  }
}

/**
 * Check if user has completed onboarding using Ultimate service
 */
export async function hasCompletedOnboardingAsync(email: string, salt?: string): Promise<boolean> {
  try {
    const service = createUltimateService(email);

    // Check if user has password in settings
    const userResult = await service.getUserByEmail(email);
    if (!userResult.success || !userResult.data || !userResult.data.settings) {
      return false;
    }

    const settings: UserSettings = JSON.parse(userResult.data.settings || '{}');
    if (!settings.password) {
      return false;
    }

    // Check if user has credentials
    const credentialsResult = await service.getUserCredentials(email);
    return !!(credentialsResult.success && credentialsResult.data && Array.isArray(credentialsResult.data));
  } catch {
    return false;
  }
}



export class UserService {
  /**
   * Create or update user in PocketBase from Clerk user data
   */
  static async syncUserFromClerk(clerkUser: ClerkUser): Promise<AppUser> {
    const userData = {
      email: clerkUser.emailAddresses[0]?.emailAddress || "",
      name: `${clerkUser.firstName || ""} ${clerkUser.lastName || ""}`.trim() || "User",
    };

    try {
      // Try to get existing user first by email (our unique identifier)
      const existingUser = await this.getUserByEmail(userData.email);

      if (existingUser) {
        // User already exists, just return it
        return existingUser;
      } else {
        // Create new user
        return await this.createUser(userData);
      }
    } catch (error) {
      console.error("Error syncing user from Clerk:", error);
      throw error;
    }
  }

  /**
   * Create a new user in the database
   */
  static async createUser(userData: {
    email: string;
    name: string;
  }): Promise<AppUser> {
    try {
      const service = createUltimateService(userData.email);
      const result = await service.createUser(userData);

      if (!result.success || !result.data) {
        throw new Error(result.message || "Failed to create user");
      }

      // Transform to our AppUser interface for backward compatibility
      const appUser: AppUser = {
        id: result.data.id,
        email: result.data.email,
        emailVisibility: result.data.emailVisibility,
        verified: result.data.verified,
        name: result.data.name,
        avatar: result.data.avatar || "",
        settings: result.data.settings || JSON.stringify({}),
        createdAt: result.data.created,
        updatedAt: result.data.updated,
      };

      return appUser;
    } catch (error) {
      console.error("Error creating user:", error);
      throw error;
    }
  }

  /**
   * Get user by email
   */
  static async getUserByEmail(email: string): Promise<AppUser | null> {
    try {
      const service = createUltimateService(email);
      const result = await service.getUserByEmail(email);

      if (!result.success || !result.data) {
        return null;
      }

      // Transform to our AppUser interface
      const appUser: AppUser = {
        id: result.data.id,
        email: result.data.email,
        emailVisibility: result.data.emailVisibility,
        verified: result.data.verified,
        name: result.data.name,
        avatar: result.data.avatar || "",
        settings: result.data.settings || JSON.stringify({}),
        createdAt: result.data.created,
        updatedAt: result.data.updated,
      };

      return appUser;
    } catch (error) {
      console.error("Error getting user by email:", error);
      return null;
    }
  }

  /**
   * Update user in the database
   */
  static async updateUser(userId: string, updates: Partial<AppUser>): Promise<AppUser> {
    try {
      const app = await getUltimateApp();

      // Access PocketBase through the Ultimate app
      const pb = (app as any).app_db.pb;

      // Prepare update data, converting our interface fields to PocketBase fields
      const updateData: any = {
        updated: new Date().toISOString(),
      };

      // Map our interface fields to PocketBase fields
      if (updates.email !== undefined) updateData.email = updates.email;
      if (updates.emailVisibility !== undefined) updateData.emailVisibility = updates.emailVisibility;
      if (updates.verified !== undefined) updateData.verified = updates.verified;
      if (updates.name !== undefined) updateData.name = updates.name;
      if (updates.avatar !== undefined) updateData.avatar = updates.avatar;
      if (updates.settings !== undefined) updateData.settings = updates.settings;

      // Update user in the 'users' collection
      const updatedUser = await pb.collection('users').update(userId, updateData);

      // Transform PocketBase record to our AppUser interface
      const appUser: AppUser = {
        id: updatedUser.id,
        email: updatedUser.email,
        emailVisibility: updatedUser.emailVisibility,
        verified: updatedUser.verified,
        name: updatedUser.name,
        avatar: updatedUser.avatar || "",
        settings: updatedUser.settings || JSON.stringify({}),
        createdAt: updatedUser.created,
        updatedAt: updatedUser.updated,
      };

      return appUser;
    } catch (error) {
      console.error("Error updating user:", error);
      throw error;
    }
  }

  /**
   * Update user settings (for onboarding completion)
   */
  static async updateUserSettings(userId: string, settings: any): Promise<AppUser> {
    return await this.updateUser(userId, settings);
  }

  /**
   * Complete user onboarding by adding credentials
   */
  static async completeOnboarding(email: string, credentials: {
    name: string;
    exchange: string;
    api_key: string;
    api_secret: string;
  }): Promise<AppUser> {
    try {
      const app = await getUltimateApp({ email });

      // First ensure user has a password
      const user = await app.app_db.getUserByEmail();
      if (!user) {
        throw new Error("User not found");
      }

      const settings: UserSettings = JSON.parse(user.settings || '{}');
      if (!settings.password) {
        await app.app_db.generateUserPassword();
      }

      // Add the credentials
      await app.app_db.addNewCredential({
        payload: {
          name: credentials.name,
          email: credentials.api_key, // This might be the binance email
          exchange: credentials.exchange,
          api_key: credentials.api_key,
          api_secret: credentials.api_secret,
        }
      });

      // Get the updated user
      const updatedUser = await app.app_db.getUserByEmail();

      // Transform to our AppUser interface
      const appUser: AppUser = {
        id: updatedUser.id,
        email: updatedUser.email,
        emailVisibility: updatedUser.emailVisibility,
        verified: updatedUser.verified,
        name: updatedUser.name,
        avatar: updatedUser.avatar || "",
        settings: updatedUser.settings || JSON.stringify({}),
        createdAt: updatedUser.created,
        updatedAt: updatedUser.updated,
      };

      return appUser;
    } catch (error) {
      console.error("Error completing onboarding:", error);
      throw error;
    }
  }

  /**
   * Get user's Ultimate app instance
   */
  static async getUserUltimateApp(userEmail: string, userSalt?: string): Promise<any> {
    return await getUltimateApp({
      email: userEmail,
      salt: userSalt || process.env.SALT,
    });
  }

  /**
   * Add new credentials for a user
   */
  static async addNewCredentials(params: {
    payload: {
      name: string; // unique-name
      email: string; // binance email
      exchange: string;
      api_key: string;
      api_secret: string;
    }
  }): Promise<any> {
    try {
      const app = await getUltimateApp({ email: params.payload.email });
      return await app.app_db.addNewCredential(params);
    } catch (error) {
      console.error("Error adding new credentials:", error);
      throw error;
    }
  }

  /**
   * Get user credentials
   */
  static async getUserCredentials(email: string, salt?: string): Promise<any> {
    try {
      const app = await getUltimateApp({ email, salt });
      return await app.app_db.getUserCredentials();
    } catch (error) {
      console.error("Error getting user credentials:", error);
      throw error;
    }
  }
}
