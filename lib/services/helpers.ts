import { decodedToken } from "../utils/decodeToken";

  export function determineMaxAge(authToken: string) {
    try {
      const tokenDetails = decodedToken(authToken);

      if (!tokenDetails) {
        console.error("Failed to decode token: tokenDetails is null");
        return null;
      }
      const expirationTimestamp = tokenDetails.exp;

      if (!expirationTimestamp) {
        console.error("No expiration timestamp found in token");
        return null;
      }
      const expirationInMs = expirationTimestamp * 1000;
      const now = Date.now();
      const diff = expirationInMs - now;
      const expiringTime = Math.max(0, Math.floor(diff / 1000));

      return expiringTime;
    } catch (error) {
      console.error("Failed to analyze token:", error);
      return null;
    }
  }


    //validate and decode token
  export function validateAndDecodeToken(token: string) {
    const tokenDetails = decodedToken(token);

    if (!tokenDetails?.id) {
      console.error("Failed to decode token: missing ID");
      return null;
    }

    // Check expiration (treat missing exp as invalid)
    if (!tokenDetails.exp || Date.now() >= tokenDetails.exp * 1000) {
      console.warn("Token is missing exp or has expired");
      return null;
    }

    return tokenDetails;
  }