import axios, { AxiosError, AxiosResponse } from "axios";

// Get base URL dynamically
const getBaseURL = () => {
  // For server-side rendering, use localhost
  if (typeof window === "undefined") {
    return process.env.NODE_ENV === "production"
      ? "https://trading-app-tau.vercel.app/api"
      : "http://localhost:3000/api";
  }

  // For client-side, use current origin
  return `${window.location.origin}/api`;
};

// Create axios instance
export const apiClient = axios.create({
  baseURL: getBaseURL(),
  timeout: 10000,
  headers: {
    "Content-Type": "application/json",
  },
});

// Request interceptor - Clerk will handle auth automatically
apiClient.interceptors.request.use(
  (config) => {
    // Clerk handles authentication automatically through cookies/headers
    // No need to manually add tokens
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor
apiClient.interceptors.response.use(
  (response: AxiosResponse) => {
    return response;
  },
  (error: AxiosError) => {
    // Handle common errors
    if (error.response?.status === 401) {
      // Unauthorized - redirect to Clerk login
      if (typeof window !== "undefined") {
        window.location.href = "/auth/login";
      }
    }

    return Promise.reject(error);
  }
);

// API Response types
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
}

// Generic API error type
export interface ApiError {
  message: string;
  status?: number;
  code?: string;
}
