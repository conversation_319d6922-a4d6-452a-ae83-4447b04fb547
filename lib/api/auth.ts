import { apiClient, ApiResponse } from "./client";
import { User } from "../stores/auth-store";

// Auth API request/response types
export interface LoginRequest {
  email: string;
  password: string;
}

export interface SignupRequest {
  email: string;
  password: string;
  name: string;
}

export interface AuthResponse {
  user: User;
  token: string;
}

// Auth API service
export const authApi = {
  // Login user
  login: async (data: LoginRequest): Promise<ApiResponse<AuthResponse>> => {
    const response = await apiClient.post("/auth/login", data);
    return response.data;
  },

  // Register new user
  signup: async (data: SignupRequest): Promise<ApiResponse<AuthResponse>> => {
    const response = await apiClient.post("/auth/signup", data);
    return response.data;
  },

  // Get current user profile
  me: async (): Promise<ApiResponse<User>> => {
    const response = await apiClient.get("/auth/me");
    return response.data;
  },

  // Logout user
  logout: async (): Promise<ApiResponse> => {
    const response = await apiClient.post("/auth/logout");
    return response.data;
  },

  // Refresh token
  refreshToken: async (): Promise<ApiResponse<{ token: string }>> => {
    const response = await apiClient.post("/auth/refresh");
    return response.data;
  },

  // Update user profile
  updateProfile: async (data: Partial<User>): Promise<ApiResponse<User>> => {
    const response = await apiClient.patch("/auth/profile", data);
    return response.data;
  },

  // Complete onboarding
  completeOnboarding: async (): Promise<ApiResponse<User>> => {
    const response = await apiClient.post("/auth/complete-onboarding");
    return response.data;
  },
};
