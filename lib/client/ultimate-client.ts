/**
 * Ultimate Client Library
 * Clean interface for Ultimate app operations with proper separation of concerns
 */

import { initApp, AppDatabase } from "@gbozee/ultimate";

export interface UltimateAppConfig {
  email?: string;
  salt?: string;
}

export interface CredentialPayload {
  name: string;
  email: string;
  exchange: string;
  api_key: string;
  api_secret: string;
}

export interface ExchangeAccountParams {
  owner: string;
  exchange: string;
}

/**
 * Ultimate Client - Clean interface for Ultimate app operations
 */
export class UltimateClient {
  private static instances: Map<string, UltimateClient> = new Map();
  private app: any;
  private config: UltimateAppConfig;

  private constructor(app: any, config: UltimateAppConfig) {
    this.app = app;
    this.config = config;
  }

  /**
   * Get or create Ultimate client instance (singleton per user)
   */
  static async getInstance(config: UltimateAppConfig = {}): Promise<UltimateClient> {
    const cacheKey = `${config.email || 'default'}:${config.salt || process.env.SALT}`;
    
    if (UltimateClient.instances.has(cacheKey)) {
      return UltimateClient.instances.get(cacheKey)!;
    }

    const app = await UltimateClient.initializeApp(config);
    const client = new UltimateClient(app, config);
    UltimateClient.instances.set(cacheKey, client);
    
    return client;
  }

  /**
   * Initialize Ultimate app
   */
  private static async initializeApp(config: UltimateAppConfig): Promise<any> {
    if (!process.env.NEW_POCKETBASE_HOST || !process.env.NEW_POCKETBASE_EMAIL || !process.env.NEW_POCKETBASE_PASSWORD) {
      throw new Error("NEW PocketBase configuration missing in environment variables");
    }

    const app = await initApp({
      db: {
        host: process.env.NEW_POCKETBASE_HOST!,
        email: process.env.NEW_POCKETBASE_EMAIL!,
        password: process.env.NEW_POCKETBASE_PASSWORD!,
      },
      email: config.email,
      salt: config.salt || process.env.SALT,
      getCredentials: async ({ account, exchange, app_db }: {
        account: string;
        exchange: string;
        app_db: AppDatabase;
      }) => {
        const credentials = await app_db.getUserCredentials();
        const credential = credentials.find((c: any) => 
          c.exchange === exchange && c.name === account
        );
        
        if (!credential) {
          throw new Error(`Credential not found for ${account} on ${exchange}`);
        }
        
        return {
          api_key: credential.api_key,
          api_secret: credential.api_secret,
          email: credential.email,
        };
      },
    });

    return app;
  }

  /**
   * Get the raw Ultimate app instance
   */
  getApp(): any {
    return this.app;
  }

  /**
   * Get the app database instance
   */
  getAppDatabase(): AppDatabase {
    return this.app.app_db;
  }

  /**
   * Get PocketBase instance (for database operations)
   */
  getPocketBase(): any {
    return this.app.app_db.pb;
  }

  /**
   * User operations
   */
  async getUserByEmail(): Promise<any> {
    return await this.app.app_db.getUserByEmail();
  }

  async generateUserPassword(): Promise<void> {
    return await this.app.app_db.generateUserPassword();
  }

  async getUserCredentials(): Promise<any[]> {
    return await this.app.app_db.getUserCredentials();
  }

  /**
   * Credential operations
   */
  async addNewCredential(payload: { payload: CredentialPayload }): Promise<any> {
    return await this.app.app_db.addNewCredential(payload);
  }

  /**
   * Exchange operations
   */
  async getExchangeAccount(params: ExchangeAccountParams): Promise<any> {
    return await this.app.getExchangeAccount(params);
  }

  async getAllOpenSymbols(params: ExchangeAccountParams): Promise<any> {
    const exchangeAccount = await this.getExchangeAccount(params);
    return await exchangeAccount.exchange.getAllOpenSymbols();
  }

  /**
   * Test operations
   */
  async testConnection(): Promise<{ success: boolean; message: string }> {
    try {
      const pb = this.getPocketBase();
      await pb.collection('users').getList(1, 1);
      return { success: true, message: "Connection successful" };
    } catch (error) {
      return {
        success: false,
        message: error instanceof Error ? error.message : "Connection failed"
      };
    }
  }

  /**
   * Clear cache for this instance
   */
  static clearCache(config: UltimateAppConfig = {}): void {
    const cacheKey = `${config.email || 'default'}:${config.salt || process.env.SALT}`;
    UltimateClient.instances.delete(cacheKey);
  }

  /**
   * Clear all cached instances
   */
  static clearAllCache(): void {
    UltimateClient.instances.clear();
  }
}

/**
 * Convenience function to get Ultimate client
 */
export async function getUltimateClient(config: UltimateAppConfig = {}): Promise<UltimateClient> {
  return await UltimateClient.getInstance(config);
}

/**
 * Legacy compatibility - matches the demo pattern
 */
export async function getApp(payload: { email?: string; salt?: string } = {}): Promise<any> {
  const client = await getUltimateClient(payload);
  return client.getApp();
}
