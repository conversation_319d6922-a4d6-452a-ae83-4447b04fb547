/**
 * Database service layer
 * Provides high-level database operations using Ultimate app's PocketBase instance
 */

import { DatabaseOperations, createDatabaseOperations } from './pocketbase-client';
import { getUltimateClient } from '../client/ultimate-client';
import {
  PocketBaseUser,
  UserSettings,
  Proxy,
  ExchangeAccount,
  DatabaseResponse
} from './types';

export class DatabaseService {
  private static instance: DatabaseService;
  private dbOps: DatabaseOperations | null = null;
  private userEmail?: string;

  private constructor(userEmail?: string) {
    this.userEmail = userEmail;
  }

  /**
   * Get singleton instance for a specific user
   */
  static getInstance(userEmail?: string): DatabaseService {
    // For now, we'll use a single instance, but this can be extended for per-user instances
    if (!DatabaseService.instance) {
      DatabaseService.instance = new DatabaseService(userEmail);
    }
    return DatabaseService.instance;
  }

  /**
   * Initialize database operations using Ultimate client
   */
  async initialize(email?: string): Promise<void> {
    if (!this.dbOps) {
      const client = await getUltimateClient({ email: email || this.userEmail });
      const ultimateApp = client.getApp();
      this.dbOps = createDatabaseOperations(ultimateApp);
    }
  }

  /**
   * Get database operations instance
   */
  private async getDbOps(email?: string): Promise<DatabaseOperations> {
    if (!this.dbOps) {
      await this.initialize(email);
    }
    return this.dbOps!;
  }

  /**
   * Test database connection
   */
  async testConnection(email?: string): Promise<DatabaseResponse<{ connected: boolean }>> {
    try {
      const dbOps = await this.getDbOps(email);
      const result = await dbOps.testConnection();
      return {
        success: result.success,
        data: { connected: result.success },
        message: result.message,
      };
    } catch (error) {
      return {
        success: false,
        message: error instanceof Error ? error.message : "Connection test failed",
      };
    }
  }

  /**
   * User operations
   */
  async getUserByEmail(email: string): Promise<DatabaseResponse<PocketBaseUser | null>> {
    try {
      const dbOps = await this.getDbOps(email);
      const user = await dbOps.getUserByEmail(email);
      return {
        success: true,
        data: user,
      };
    } catch (error) {
      return {
        success: false,
        message: error instanceof Error ? error.message : "Failed to get user",
      };
    }
  }

  async createUser(userData: {
    email: string;
    name: string;
    emailVisibility?: boolean;
    verified?: boolean;
    avatar?: string;
    settings?: UserSettings;
  }): Promise<DatabaseResponse<PocketBaseUser>> {
    try {
      const dbOps = await this.getDbOps(userData.email);

      // Generate a temporary password for PocketBase user creation
      const tempPassword = this.generateTempPassword();

      const userRecord = {
        email: userData.email,
        name: userData.name,
        emailVisibility: userData.emailVisibility ?? true,
        verified: userData.verified ?? true,
        avatar: userData.avatar ?? "",
        settings: JSON.stringify(userData.settings ?? {}),
        password: tempPassword,
        passwordConfirm: tempPassword,
      };

      const user = await dbOps.createUser(userRecord);
      return {
        success: true,
        data: user,
      };
    } catch (error) {
      return {
        success: false,
        message: error instanceof Error ? error.message : "Failed to create user",
      };
    }
  }

  /**
   * Generate a temporary password for user creation
   */
  private generateTempPassword(): string {
    return Math.random().toString(36).slice(-12) + Math.random().toString(36).slice(-12);
  }

  async updateUserSettings(
    userId: string,
    settings: UserSettings,
    userEmail?: string
  ): Promise<DatabaseResponse<PocketBaseUser>> {
    try {
      const dbOps = await this.getDbOps(userEmail);
      const user = await dbOps.updateUser(userId, {
        settings: JSON.stringify(settings),
        updated: new Date().toISOString(),
      });
      return {
        success: true,
        data: user,
      };
    } catch (error) {
      return {
        success: false,
        message: error instanceof Error ? error.message : "Failed to update user settings",
      };
    }
  }

  /**
   * Proxy operations
   */
  async getProxiesByUser(userId: string, userEmail?: string): Promise<DatabaseResponse<Proxy[]>> {
    try {
      const dbOps = await this.getDbOps(userEmail);
      const proxies = await dbOps.getProxiesByUser(userId);
      return {
        success: true,
        data: proxies,
      };
    } catch (error) {
      return {
        success: false,
        message: error instanceof Error ? error.message : "Failed to get proxies",
      };
    }
  }

  async createProxy(proxyData: {
    ip_address: string;
    type: 'http' | 'socks5';
    user: string;
  }, userEmail?: string): Promise<DatabaseResponse<Proxy>> {
    try {
      const dbOps = await this.getDbOps(userEmail);
      const proxy = await dbOps.createProxy(proxyData);
      return {
        success: true,
        data: proxy,
      };
    } catch (error) {
      return {
        success: false,
        message: error instanceof Error ? error.message : "Failed to create proxy",
      };
    }
  }

  /**
   * Exchange account operations
   */
  async getExchangeAccountsByUser(userId: string, userEmail?: string): Promise<DatabaseResponse<ExchangeAccount[]>> {
    try {
      const dbOps = await this.getDbOps(userEmail);
      const accounts = await dbOps.getExchangeAccountsByUser(userId);
      return {
        success: true,
        data: accounts,
      };
    } catch (error) {
      return {
        success: false,
        message: error instanceof Error ? error.message : "Failed to get exchange accounts",
      };
    }
  }

  async createExchangeAccount(accountData: {
    exchange: string;
    owner: string;
    email: string;
    proxy?: string;
    user: string;
    usdt?: number;
    usdc?: number;
    bullish?: boolean;
    bearish?: boolean;
    totalRisk?: number;
    movePercent?: number;
    max_non_essential?: number;
    profit_percent?: number;
    risk_reward?: number;
    exclude_coins?: string[];
    include_delisted?: boolean;
  }, userEmail?: string): Promise<DatabaseResponse<ExchangeAccount>> {
    try {
      const dbOps = await this.getDbOps(userEmail);

      const accountRecord = {
        ...accountData,
        usdt: accountData.usdt ?? 0,
        usdc: accountData.usdc ?? 0,
        bullish: accountData.bullish ?? false,
        bearish: accountData.bearish ?? false,
        totalRisk: accountData.totalRisk ?? 0,
        movePercent: accountData.movePercent ?? 0,
        max_non_essential: accountData.max_non_essential ?? 0,
        profit_percent: accountData.profit_percent ?? 0,
        risk_reward: accountData.risk_reward ?? 0,
        exclude_coins: accountData.exclude_coins ?? [],
        include_delisted: accountData.include_delisted ?? false,
      };

      const account = await dbOps.createExchangeAccount(accountRecord);
      return {
        success: true,
        data: account,
      };
    } catch (error) {
      return {
        success: false,
        message: error instanceof Error ? error.message : "Failed to create exchange account",
      };
    }
  }

  /**
   * Get raw PocketBase client for Ultimate app compatibility
   */
  async getRawPocketBaseClient(userEmail?: string): Promise<any> {
    const dbOps = await this.getDbOps(userEmail);
    return dbOps.getPocketBase();
  }
}

/**
 * Factory function to get database service instance
 */
export function getDatabaseService(userEmail?: string): DatabaseService {
  if (!process.env.NEW_POCKETBASE_HOST || !process.env.NEW_POCKETBASE_EMAIL || !process.env.NEW_POCKETBASE_PASSWORD) {
    throw new Error("NEW PocketBase configuration missing in environment variables");
  }

  return DatabaseService.getInstance(userEmail);
}
