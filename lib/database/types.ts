/**
 * Database types and interfaces
 * Pure type definitions for database entities
 */

export interface DatabaseConfig {
  host: string;
  email: string;
  password: string;
}

export interface PocketBaseUser {
  id: string;
  email: string;
  emailVisibility: boolean;
  verified: boolean;
  name: string;
  avatar: string;
  settings: string; // JSON string
  created: string;
  updated: string;
}

export interface UserSettings {
  password?: string;
  credentials?: any;
  hasCompletedOnboarding?: boolean;
  [key: string]: any;
}

export interface Proxy {
  id: string;
  ip_address: string; // format: username:password@host:port
  type: 'http' | 'socks5';
  user: string; // relation to user
  created: string;
  updated: string;
}

export interface ExchangeAccount {
  id: string;
  exchange: string;
  owner: string;
  email: string;
  usdt: number;
  usdc: number;
  proxy?: string; // relation to proxy
  bullish: boolean;
  bearish: boolean;
  totalRisk: number;
  movePercent: number;
  max_non_essential: number;
  profit_percent: number;
  risk_reward: number;
  exclude_coins: string[]; // JSON array
  include_delisted: boolean;
  user: string; // relation to user
  created: string;
  updated: string;
}

export interface Credential {
  id: string;
  name: string;
  exchange: string;
  api_key: string;
  api_secret: string;
  email: string;
  created: string;
  updated: string;
}

// Query options
export interface QueryOptions {
  page?: number;
  perPage?: number;
  sort?: string;
  filter?: string;
  expand?: string;
  fields?: string;
}

// API Response wrapper
export interface DatabaseResponse<T> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
}

// Collection names
export const COLLECTIONS = {
  USERS: 'users',
  PROXIES: 'proxies',
  EXCHANGE_ACCOUNTS: 'exchange_accounts',
  CREDENTIALS: 'credentials',
} as const;

export type CollectionName = typeof COLLECTIONS[keyof typeof COLLECTIONS];
