/**
 * Database operations wrapper
 * Uses PocketBase instance from Ultimate app - no separate PocketBase import needed
 */

import {
  PocketBaseUser,
  Proxy,
  ExchangeAccount,
  QueryOptions,
  COLLECTIONS,
  CollectionName
} from './types';

export class DatabaseOperations {
  private pb: any; // PocketBase instance from Ultimate app

  constructor(pocketbaseInstance: any) {
    this.pb = pocketbaseInstance;
  }

  /**
   * Get the PocketBase instance (already authenticated through Ultimate app)
   */
  getPocketBase(): any {
    return this.pb;
  }

  /**
   * Generic collection operations using Ultimate app's PocketBase instance
   */
  async getList<T>(
    collection: CollectionName,
    options: QueryOptions = {}
  ): Promise<{ items: T[]; totalItems: number; totalPages: number }> {
    const { page = 1, perPage = 50, sort, filter, expand, fields } = options;

    const result = await this.pb.collection(collection).getList(page, perPage, {
      sort,
      filter,
      expand,
      fields,
    });

    return {
      items: result.items as T[],
      totalItems: result.totalItems,
      totalPages: result.totalPages,
    };
  }

  async getOne<T>(
    collection: CollectionName,
    id: string,
    options: { expand?: string; fields?: string } = {}
  ): Promise<T> {
    const { expand, fields } = options;
    const result = await this.pb.collection(collection).getOne(id, {
      expand,
      fields,
    });
    return result as T;
  }

  async getFirstListItem<T>(
    collection: CollectionName,
    filter: string,
    options: { expand?: string; fields?: string } = {}
  ): Promise<T> {
    const { expand, fields } = options;
    const result = await this.pb.collection(collection).getFirstListItem(filter, {
      expand,
      fields,
    });
    return result as T;
  }

  async create<T>(
    collection: CollectionName,
    data: Record<string, any>
  ): Promise<T> {
    const result = await this.pb.collection(collection).create(data);
    return result as T;
  }

  async update<T>(
    collection: CollectionName,
    id: string,
    data: Record<string, any>
  ): Promise<T> {
    const result = await this.pb.collection(collection).update(id, data);
    return result as T;
  }

  async delete(collection: CollectionName, id: string): Promise<boolean> {
    await this.pb.collection(collection).delete(id);
    return true;
  }

  /**
   * User-specific operations
   */
  async getUserByEmail(email: string): Promise<PocketBaseUser | null> {
    try {
      return await this.getFirstListItem<PocketBaseUser>(
        COLLECTIONS.USERS, 
        `email = '${email}'`
      );
    } catch (error) {
      return null;
    }
  }

  async createUser(userData: Partial<PocketBaseUser>): Promise<PocketBaseUser> {
    return await this.create<PocketBaseUser>(COLLECTIONS.USERS, userData);
  }

  async updateUser(id: string, updates: Partial<PocketBaseUser>): Promise<PocketBaseUser> {
    return await this.update<PocketBaseUser>(COLLECTIONS.USERS, id, updates);
  }

  /**
   * Proxy operations
   */
  async getProxiesByUser(userId: string): Promise<Proxy[]> {
    const result = await this.getList<Proxy>(COLLECTIONS.PROXIES, {
      filter: `user = "${userId}"`,
    });
    return result.items;
  }

  async createProxy(proxyData: Partial<Proxy>): Promise<Proxy> {
    return await this.create<Proxy>(COLLECTIONS.PROXIES, proxyData);
  }

  /**
   * Exchange account operations
   */
  async getExchangeAccountsByUser(userId: string): Promise<ExchangeAccount[]> {
    const result = await this.getList<ExchangeAccount>(COLLECTIONS.EXCHANGE_ACCOUNTS, {
      filter: `user = "${userId}"`,
      expand: 'proxy',
    });
    return result.items;
  }

  async createExchangeAccount(accountData: Partial<ExchangeAccount>): Promise<ExchangeAccount> {
    return await this.create<ExchangeAccount>(COLLECTIONS.EXCHANGE_ACCOUNTS, accountData);
  }

  async updateExchangeAccount(id: string, updates: Partial<ExchangeAccount>): Promise<ExchangeAccount> {
    return await this.update<ExchangeAccount>(COLLECTIONS.EXCHANGE_ACCOUNTS, id, updates);
  }

  /**
   * Test connection
   */
  async testConnection(): Promise<{ success: boolean; message: string }> {
    try {
      await this.getList(COLLECTIONS.USERS, { page: 1, perPage: 1 });
      return { success: true, message: "Connection successful" };
    } catch (error) {
      return { 
        success: false, 
        message: error instanceof Error ? error.message : "Connection failed" 
      };
    }
  }
}

/**
 * Factory function to create database operations wrapper from Ultimate app
 */
export function createDatabaseOperations(ultimateApp: any): DatabaseOperations {
  const pb = ultimateApp.app_db.pb;
  return new DatabaseOperations(pb);
}
