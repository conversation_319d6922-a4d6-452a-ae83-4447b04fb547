import { initApp } from "@gbozee/ultimate";
import { currentUser } from "@clerk/nextjs/server";

/**
 * Initialize the Ultimate app for a specific user
 * This function handles the connection to PocketBase and user-specific configuration
 */
export async function initializeUltimateApp(payload?: {
  email?: string;
  salt?: string;
}) {
  // Get user from Clerk if not provided
  let userEmail = payload?.email;
  if (!userEmail) {
    const user = await currentUser();
    if (!user?.emailAddresses?.[0]?.emailAddress) {
      throw new Error("User not authenticated or email not available");
    }
    userEmail = user.emailAddresses[0].emailAddress;
  }

  // Use salt from env for now (will be user-specific later)
  const salt = payload?.salt || process.env.SALT;
  if (!salt) {
    throw new Error("Salt not configured");
  }

  // Validate required environment variables
  if (!process.env.NEW_POCKETBASE_HOST || !process.env.NEW_POCKETBASE_EMAIL || !process.env.NEW_POCKETBASE_PASSWORD) {
    throw new Error("NEW PocketBase configuration missing in environment variables");
  }

  try {
    const app = await initApp({
      db: {
        host: process.env.NEW_POCKETBASE_HOST,
        email: process.env.NEW_POCKETBASE_EMAIL,
        password: process.env.NEW_POCKETBASE_PASSWORD,
      },
      email: userEmail,
      salt,
      getCredentials: (_account: string, _exchange: string) => {
        // For now, return empty credentials - will be implemented later
        return {
          api_key: "",
          api_secret: "",
          email: "",
        };
      },
    });

    return app;
  } catch (error) {
    console.error("Failed to initialize Ultimate app:", error);
    throw new Error("Failed to initialize Ultimate app");
  }
}

/**
 * Test PocketBase connection
 */
export async function testPocketBaseConnection() {
  try {
    await initializeUltimateApp();
    // If we get here without error, connection is successful
    return { success: true, message: "PocketBase connection successful" };
  } catch (error) {
    console.error("PocketBase connection test failed:", error);
    return {
      success: false,
      message: error instanceof Error ? error.message : "Unknown error"
    };
  }
}

/**
 * Test PocketBase connection with a test email (no authentication required)
 */
export async function testPocketBaseConnectionSimple() {
  try {
    const testEmail = "<EMAIL>";
    const app = await initializeUltimateApp({ email: testEmail });

    // Access PocketBase through the Ultimate app
    const pb = (app as any).app_db.pb;

    // Test a simple query to verify connection
    await pb.collection('users').getList(1, 1);

    return { success: true, message: "PocketBase connection successful" };
  } catch (error) {
    console.error("PocketBase connection test failed:", error);
    return {
      success: false,
      message: error instanceof Error ? error.message : "Unknown error"
    };
  }
}

/**
 * Get user by email from PocketBase
 */
export async function getUserByEmail(email?: string) {
  try {
    const app = await initializeUltimateApp({ email });

    // Access PocketBase through the Ultimate app
    const pb = (app as any).app_db.pb;

    if (!email) {
      return { success: false, message: "Email is required" };
    }

    // Query users collection by email
    const records = await pb.collection('users').getList(1, 1, {
      filter: `email = "${email}"`
    });

    const user = records.items.length > 0 ? records.items[0] : null;

    return { success: true, user };
  } catch (error) {
    console.error("Failed to get user by email:", error);
    return {
      success: false,
      message: error instanceof Error ? error.message : "Unknown error"
    };
  }
}

/**
 * Generate a secure salt for a user
 */
export function generateUserSalt(): string {
  // Generate a cryptographically secure random salt
  const crypto = require('crypto');
  return crypto.randomBytes(32).toString('hex');
}

/**
 * Get or generate user salt
 */
export function getUserSalt(_email: string): string | undefined {
  // For now, return the global salt
  // Later this will be user-specific and stored in the database
  return process.env.SALT ;
}
