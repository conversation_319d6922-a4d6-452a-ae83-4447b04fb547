import { useState, useEffect } from "react";
import { useAuth } from "@/contexts/auth-context";
import { getAdminUser } from "@/app/admin/invitations/actions";

/**
 * Simple hook that checks if current user has admin role
 */
export function useIsAdmin(): boolean {
  const { user, isAuthenticated } = useAuth();
  const [isAdmin, setIsAdmin] = useState(false);

  useEffect(() => {
    if (!isAuthenticated || !user) {
      setIsAdmin(false);
      return;
    }

    // Check admin role using server action
    const checkAdminRole = async () => {
      try {
        await getAdminUser();
        // If no error thrown, user is admin
        setIsAdmin(true);
      } catch (error) {
        // If error thrown, user is not admin
        setIsAdmin(false);
      }
    };

    checkAdminRole();
  }, [isAuthenticated, user]);

  return isAdmin;
}