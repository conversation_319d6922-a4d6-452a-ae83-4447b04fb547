import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { apiClient } from "../api/client";
import { toast } from "sonner";

// Types
export interface ProxyCreateRequest {
  username?: string;
  password?: string;
  host: string;
  port: number;
  type: "http" | "socks5";
}

export interface Proxy {
  id: string;
  ip_address: string;
  type: "http" | "socks5";
  created: string;
  updated: string;
  user: string;
}

// Query keys
export const proxyKeys = {
  all: ["proxy"] as const,
  list: () => [...proxyKeys.all, "list"] as const,
  detail: (id: string) => [...proxyKeys.all, "detail", id] as const,
};

// Create proxy mutation
export function useCreateProxy() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: ProxyCreateRequest) => {
      const response = await apiClient.post("/proxy/create", data);
      return response.data;
    },
    onSuccess: (response: any) => {
      if (response.success) {
        // Invalidate proxy queries to refresh the list
        queryClient.invalidateQueries({ queryKey: proxyKeys.all });

        toast.success("Proxy created successfully!");
        return response.data;
      } else {
        throw new Error(response.message || "Failed to create proxy");
      }
    },
    onError: (error: any) => {
      const message = error.response?.data?.message || "Failed to create proxy. Please try again.";
      toast.error(message);
    },
  });
}

// Get user's proxies
export function useProxies() {
  return useQuery({
    queryKey: proxyKeys.list(),
    queryFn: async () => {
      const response = await apiClient.get("/proxy/list");
      return response.data;
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

// Get single proxy
export function useProxy(id: string) {
  return useQuery({
    queryKey: proxyKeys.detail(id),
    queryFn: async () => {
      const response = await apiClient.get(`/proxy/${id}`);
      return response.data;
    },
    enabled: !!id,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

// Update proxy mutation
export function useUpdateProxy() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ id, data }: { id: string; data: Partial<ProxyCreateRequest> }) => {
      const response = await apiClient.patch(`/proxy/${id}`, data);
      return response.data;
    },
    onSuccess: (response: any, variables) => {
      if (response.success) {
        // Invalidate proxy queries
        queryClient.invalidateQueries({ queryKey: proxyKeys.all });
        queryClient.invalidateQueries({ queryKey: proxyKeys.detail(variables.id) });

        toast.success("Proxy updated successfully!");
        return response.data;
      } else {
        throw new Error(response.message || "Failed to update proxy");
      }
    },
    onError: (error: any) => {
      const message = error.response?.data?.message || "Failed to update proxy. Please try again.";
      toast.error(message);
    },
  });
}

// Delete proxy mutation
export function useDeleteProxy() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (id: string) => {
      const response = await apiClient.delete(`/proxy/${id}`);
      return response.data;
    },
    onSuccess: (response: any) => {
      if (response.success) {
        // Invalidate proxy queries
        queryClient.invalidateQueries({ queryKey: proxyKeys.all });

        toast.success("Proxy deleted successfully!");
        return response.data;
      } else {
        throw new Error(response.message || "Failed to delete proxy");
      }
    },
    onError: (error: any) => {
      const message = error.response?.data?.message || "Failed to delete proxy. Please try again.";
      toast.error(message);
    },
  });
}
