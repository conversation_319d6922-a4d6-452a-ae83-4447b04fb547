"use client"

import { tradingAccountState$, tradingAccountActions, TradingAccount } from "../stores/legend-trading-account-store"

/**
 * Hook to access trading account state and actions using Legend State
 * Note: Components using this hook should be wrapped with observer() HOC
 */
export function useLegendTradingAccount() {
  // Get values directly from the observable state using .get()
  // This will be reactive when used within an observer() component
  const accounts = tradingAccountState$.accounts.get()
  const activeAccountId = tradingAccountState$.activeAccountId.get()
  const isLoading = tradingAccountState$.isLoading.get()
  const hasCompletedOnboarding = tradingAccountState$.hasCompletedOnboarding.get()

  return {
    // State
    accounts,
    activeAccountId,
    isLoading,
    hasCompletedOnboarding,

    // Actions
    setAccounts: tradingAccountActions.setAccounts,
    setActiveAccountId: tradingAccountActions.setActiveAccountId,
    setLoading: tradingAccountActions.setLoading,
    setHasCompletedOnboarding: tradingAccountActions.setHasCompletedOnboarding,
    addAccount: tradingAccountActions.addAccount,
    updateAccount: tradingAccountActions.updateAccount,
    deleteAccount: tradingAccountActions.deleteAccount,
    getActiveAccount: tradingAccountActions.getActiveAccount,
    switchAccount: tradingAccountActions.switchAccount,
    createAccount: tradingAccountActions.createAccount,
    resumeAccountSetup: tradingAccountActions.resumeAccountSetup,
    completeOnboarding: tradingAccountActions.completeOnboarding,
    initializeMockData: tradingAccountActions.initializeMockData,
  }
}

/**
 * Hook to get only the active account (optimized for components that only need active account)
 * Note: Components using this hook should be wrapped with observer() HOC
 */
export function useActiveAccount() {
  // Get values directly from the observable state using .get()
  const activeAccountId = tradingAccountState$.activeAccountId.get()
  const accounts = tradingAccountState$.accounts.get()

  // Compute active account
  const activeAccount = activeAccountId
    ? accounts.find(account => account.id === activeAccountId) || null
    : null

  return {
    activeAccount,
    activeAccountId,
    switchAccount: tradingAccountActions.switchAccount,
  }
}

/**
 * Hook to get accounts list (optimized for components that only need accounts list)
 */
export function useAccountsList() {
  const accounts = useObservable(tradingAccountState$.accounts)
  const isLoading = useObservable(tradingAccountState$.isLoading)
  
  return {
    accounts,
    isLoading,
    addAccount: tradingAccountActions.addAccount,
    updateAccount: tradingAccountActions.updateAccount,
    deleteAccount: tradingAccountActions.deleteAccount,
    createAccount: tradingAccountActions.createAccount,
  }
}

/**
 * Hook for onboarding-related functionality
 */
export function useOnboarding() {
  const hasCompletedOnboarding = useObservable(tradingAccountState$.hasCompletedOnboarding)
  const activeAccountId = useObservable(tradingAccountState$.activeAccountId)
  
  return {
    hasCompletedOnboarding,
    activeAccountId,
    setHasCompletedOnboarding: tradingAccountActions.setHasCompletedOnboarding,
    resumeAccountSetup: tradingAccountActions.resumeAccountSetup,
    completeOnboarding: tradingAccountActions.completeOnboarding,
  }
}
