import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { apiClient } from "../api/client";
import { useAuthStore } from "../stores/auth-store";
import { useRouter } from "next/navigation";
import { toast } from "sonner";
// Removed Clerk dependency - using custom auth context instead

// Define the interfaces locally to avoid importing from user-service
export interface AppUser {
  id: string;
  email: string;
  emailVisibility: boolean;
  verified: boolean;
  name: string;
  avatar: string;
  settings: string; // JSON string containing encrypted credentials and other settings
  createdAt: string;
  updatedAt: string;
}

export interface UserSettings {
  password?: string;
  credentials?: any;
  [key: string]: any;
}



// Query keys
export const userKeys = {
  all: ["user"] as const,
  sync: () => [...userKeys.all, "sync"] as const,
};

// Sync user with database and get user data
export function useUserSync() {
  const { setUser, setLoading } = useAuthStore();
  // Note: This hook is deprecated - use the new auth context instead

  return useQuery({
    queryKey: userKeys.sync(),
    queryFn: async () => {
      setLoading(true);
      try {
        const response = await apiClient.get("/user/sync");
        if (response.data.success && response.data.data) {
          setUser(response.data.data);
          return {
            user: response.data.data,
            hasCompletedOnboarding: response.data.hasCompletedOnboarding
          };
        }
        throw new Error(response.data.message || "Failed to sync user");
      } catch (error) {
        console.error("User sync failed:", error);
        setLoading(false);
        throw error;
      } finally {
        setLoading(false);
      }
    },
    enabled: false, // Disabled - use new auth context instead
    retry: false,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

// Complete onboarding mutation
export function useCompleteOnboarding() {
  const router = useRouter();
  const queryClient = useQueryClient();
  const { setUser } = useAuthStore();

  return useMutation({
    mutationFn: async (data: { credentials: any }) => {
      const response = await apiClient.post("/user/complete-onboarding", data);
      return response.data;
    },
    onSuccess: (response: any) => {
      if (response.success && response.data) {
        // Update store with the updated user data
        setUser(response.data);

        // Invalidate user query
        queryClient.invalidateQueries({ queryKey: userKeys.sync() });

        // Show success message
        toast.success("Onboarding completed!");

        // Redirect to dashboard
        router.push("/dashboard");
      }
    },
    onError: (error: any) => {
      const message = error.response?.data?.message || "Failed to complete onboarding";
      toast.error(message);
    },
  });
}




