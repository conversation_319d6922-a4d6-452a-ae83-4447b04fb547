import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { apiClient } from "../api/client";
import { toast } from "sonner";

// Types
export interface BinanceConnectRequest {
  apiKey: string;
  secretKey: string;
  binanceEmail: string;
  proxyId?: string; // Optional proxy ID from step 1
}

export interface ExchangeAccount {
  id: string;
  exchange: string;
  owner: string;
  email: string;
  usdt: number;
  usdc: number;
  proxy?: string;
  bullish: boolean;
  bearish: boolean;
  totalRisk: number;
  movePercent: number;
  max_non_essential: number;
  profit_percent: number;
  risk_reward: number;
  exclude_coins: string[];
  include_delisted: boolean;
  user: string;
  created: string;
  updated: string;
}

export interface BinanceConnectionResult {
  success: boolean;
  exchangeAccount?: ExchangeAccount;
  credentials?: {
    name: string;
    exchange: string;
    encrypted: boolean;
  };
  apiValidation?: {
    connected: boolean;
    symbols?: string[];
    error?: string;
  };
}

// Query keys
export const exchangeKeys = {
  all: ["exchange"] as const,
  accounts: () => [...exchangeKeys.all, "accounts"] as const,
  account: (id: string) => [...exchangeKeys.all, "account", id] as const,
  binance: () => [...exchangeKeys.all, "binance"] as const,
};

// Connect Binance account
export function useConnectBinance() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: BinanceConnectRequest) => {
      const response = await apiClient.post("/exchange/binance/connect", data);
      return response.data;
    },
    onSuccess: (response: BinanceConnectionResult) => {
      if (response.success) {
        // Invalidate exchange queries to refresh the list
        queryClient.invalidateQueries({ queryKey: exchangeKeys.all });

        if (response.apiValidation?.connected) {
          toast.success("Binance account connected successfully!");
        } else {
          toast.success("Binance credentials stored, but API validation failed. Please check your credentials.");
        }
        
        return response;
      } else {
        throw new Error("Failed to connect Binance account");
      }
    },
    onError: (error: any) => {
      const message = error.response?.data?.message || "Failed to connect Binance account. Please try again.";
      toast.error(message);
    },
  });
}

// Test Binance API connection
export function useTestBinanceConnection() {
  return useMutation({
    mutationFn: async (data: { owner: string; exchange: string }) => {
      const response = await apiClient.post("/exchange/test-connection", data);
      return response.data;
    },
    onSuccess: (response: any) => {
      if (response.success && response.connected) {
        toast.success("Binance API connection successful!");
      } else {
        toast.error(response.message || "Binance API connection failed");
      }
    },
    onError: (error: any) => {
      const message = error.response?.data?.message || "Failed to test Binance connection";
      toast.error(message);
    },
  });
}

// Get user's exchange accounts
export function useExchangeAccounts() {
  return useQuery({
    queryKey: exchangeKeys.accounts(),
    queryFn: async () => {
      const response = await apiClient.get("/exchange/accounts");
      return response.data;
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

// Get single exchange account
export function useExchangeAccount(id: string) {
  return useQuery({
    queryKey: exchangeKeys.account(id),
    queryFn: async () => {
      const response = await apiClient.get(`/exchange/account/${id}`);
      return response.data;
    },
    enabled: !!id,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

// Update exchange account
export function useUpdateExchangeAccount() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ id, data }: { id: string; data: Partial<ExchangeAccount> }) => {
      const response = await apiClient.patch(`/exchange/account/${id}`, data);
      return response.data;
    },
    onSuccess: (response: any, variables) => {
      if (response.success) {
        // Invalidate exchange queries
        queryClient.invalidateQueries({ queryKey: exchangeKeys.all });
        queryClient.invalidateQueries({ queryKey: exchangeKeys.account(variables.id) });

        toast.success("Exchange account updated successfully!");
        return response.data;
      } else {
        throw new Error(response.message || "Failed to update exchange account");
      }
    },
    onError: (error: any) => {
      const message = error.response?.data?.message || "Failed to update exchange account";
      toast.error(message);
    },
  });
}

// Delete exchange account
export function useDeleteExchangeAccount() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (id: string) => {
      const response = await apiClient.delete(`/exchange/account/${id}`);
      return response.data;
    },
    onSuccess: (response: any) => {
      if (response.success) {
        // Invalidate exchange queries
        queryClient.invalidateQueries({ queryKey: exchangeKeys.all });

        toast.success("Exchange account deleted successfully!");
        return response.data;
      } else {
        throw new Error(response.message || "Failed to delete exchange account");
      }
    },
    onError: (error: any) => {
      const message = error.response?.data?.message || "Failed to delete exchange account";
      toast.error(message);
    },
  });
}

// Test exchange connection
export function useTestExchangeConnection() {
  return useMutation({
    mutationFn: async ({ owner, exchange }: { owner: string; exchange: string }) => {
      const response = await apiClient.post("/exchange/test-connection", {
        owner,
        exchange,
      });
      return response.data;
    },
    onSuccess: (response: any) => {
      if (response.success && response.connected) {
        toast.success(`${response.data?.symbolCount || 0} trading pairs found. Connection successful!`);
      } else {
        toast.error(response.message || "Connection test failed");
      }
    },
    onError: (error: any) => {
      const message = error.response?.data?.message || "Failed to test connection";
      toast.error(message);
    },
  });
}
