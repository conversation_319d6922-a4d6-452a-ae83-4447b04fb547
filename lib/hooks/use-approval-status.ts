"use client";

import { useState, useEffect, useCallback } from 'react';
import { checkUserApprovalStatus, getPendingUsers } from '@/lib/actions/invite-actions';
import { InviteUser } from '@/lib/types/invite';

interface ApprovalStatusHookResult {
  approved: boolean | null;
  loading: boolean;
  error: string | null;
  user: InviteUser | null;
  refetch: () => Promise<void>;
}

/**
 * Hook to monitor user approval status using server actions
 */
export function useApprovalStatus(userEmail?: string): ApprovalStatusHookResult {
  const [approved, setApproved] = useState<boolean | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [user, setUser] = useState<InviteUser | null>(null);

  // Fetch initial approval status using server action
  const fetchApprovalStatus = useCallback(async () => {
    if (!userEmail) return;

    try {
      setLoading(true);
      setError(null);

      // Use server action to check approval status
      const result = await checkUserApprovalStatus();

      if (result.success && result.user) {
        setUser(result.user);
        setApproved(result.approved || false);
      } else {
        setError(result.message);
        setApproved(null);
        setUser(null);
      }
    } catch (err) {
      console.error('Failed to fetch approval status:', err);
      setError('Failed to fetch approval status');
      setApproved(null);
      setUser(null);
    } finally {
      setLoading(false);
    }
  }, [userEmail]);

  // Set up polling for approval status updates
  useEffect(() => {
    if (!userEmail) return;

    // Initial fetch
    fetchApprovalStatus();

    // Set up polling every 5 seconds to check for approval updates
    const pollInterval = setInterval(() => {
      fetchApprovalStatus();
    }, 5000);

    // Cleanup interval on unmount
    return () => {
      clearInterval(pollInterval);
    };
  }, [fetchApprovalStatus, userEmail]);

  return {
    approved,
    loading,
    error,
    user,
    refetch: fetchApprovalStatus
  };
}

/**
 * Hook for admin to monitor all pending users using server actions
 */
export function usePendingUsers() {
  const [pendingUsers, setPendingUsers] = useState<InviteUser[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Fetch pending users using server action
  const fetchPendingUsers = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      const result = await getPendingUsers();

      if (result.success && result.users) {
        setPendingUsers(result.users);
      } else {
        setError(result.message);
        setPendingUsers([]);
      }
    } catch (err) {
      console.error('Failed to fetch pending users:', err);
      setError('Failed to fetch pending users');
      setPendingUsers([]);
    } finally {
      setLoading(false);
    }
  }, []);

  // Set up polling for pending users updates
  useEffect(() => {
    // Initial fetch
    fetchPendingUsers();

    // Set up polling every 10 seconds to check for new pending users
    const pollInterval = setInterval(() => {
      fetchPendingUsers();
    }, 10000);

    // Cleanup interval on unmount
    return () => {
      clearInterval(pollInterval);
    };
  }, [fetchPendingUsers]);

  return {
    pendingUsers,
    loading,
    error,
    refetch: fetchPendingUsers
  };
}
