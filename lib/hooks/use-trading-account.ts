import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { apiClient } from "../api/client";
import { useTradingAccountStore, TradingAccount } from "../stores/trading-account-store";
import { useAuthStore } from "../stores/auth-store";
import { toast } from "sonner";

// Query keys
export const tradingAccountKeys = {
  all: ["trading-accounts"] as const,
  lists: () => [...tradingAccountKeys.all, "list"] as const,
  list: (userId: string) => [...tradingAccountKeys.lists(), userId] as const,
  details: () => [...tradingAccountKeys.all, "detail"] as const,
  detail: (id: string) => [...tradingAccountKeys.details(), id] as const,
};

// Get trading accounts for the current user
export function useTradingAccounts() {
  const { user } = useAuthStore();
  const { setAccounts, setLoading } = useTradingAccountStore();

  return useQuery({
    queryKey: tradingAccountKeys.list(user?.id || ""),
    queryFn: async () => {
      setLoading(true);
      try {
        const response = await apiClient.get("/trading-accounts");
        if (response.data.success && response.data.data) {
          setAccounts(response.data.data);
          return response.data.data;
        }
        throw new Error(response.data.message || "Failed to fetch trading accounts");
      } finally {
        setLoading(false);
      }
    },
    enabled: !!user?.id,
    retry: false,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

// Create a new trading account
export function useCreateTradingAccount() {
  const queryClient = useQueryClient();
  const { user } = useAuthStore();
  const { addAccount, setActiveAccountId } = useTradingAccountStore();

  return useMutation({
    mutationFn: async (accountData: Omit<TradingAccount, 'id' | 'createdAt'>) => {
      const response = await apiClient.post("/trading-accounts", accountData);
      return response.data;
    },
    onSuccess: (response: any) => {
      if (response.success && response.data) {
        // Update store
        addAccount(response.data);
        setActiveAccountId(response.data.id);

        // Invalidate accounts query
        queryClient.invalidateQueries({ 
          queryKey: tradingAccountKeys.list(user?.id || "") 
        });

        // Show success message
        toast.success("Trading account created successfully!");
      }
    },
    onError: (error: any) => {
      const message = error.response?.data?.message || "Failed to create trading account";
      toast.error(message);
    },
  });
}

// Update a trading account
export function useUpdateTradingAccount() {
  const queryClient = useQueryClient();
  const { user } = useAuthStore();
  const { updateAccount } = useTradingAccountStore();

  return useMutation({
    mutationFn: async ({ 
      accountId, 
      updates 
    }: { 
      accountId: string; 
      updates: Partial<TradingAccount> 
    }) => {
      const response = await apiClient.patch(`/trading-accounts/${accountId}`, updates);
      return response.data;
    },
    onSuccess: (response: any, variables) => {
      if (response.success && response.data) {
        // Update store
        updateAccount(variables.accountId, response.data);

        // Invalidate accounts query
        queryClient.invalidateQueries({ 
          queryKey: tradingAccountKeys.list(user?.id || "") 
        });

        // Show success message
        toast.success("Trading account updated successfully!");
      }
    },
    onError: (error: any) => {
      const message = error.response?.data?.message || "Failed to update trading account";
      toast.error(message);
    },
  });
}

// Delete a trading account
export function useDeleteTradingAccount() {
  const queryClient = useQueryClient();
  const { user } = useAuthStore();
  const { deleteAccount } = useTradingAccountStore();

  return useMutation({
    mutationFn: async (accountId: string) => {
      const response = await apiClient.delete(`/trading-accounts/${accountId}`);
      return response.data;
    },
    onSuccess: (response: any, accountId) => {
      if (response.success) {
        // Update store
        deleteAccount(accountId);

        // Invalidate accounts query
        queryClient.invalidateQueries({ 
          queryKey: tradingAccountKeys.list(user?.id || "") 
        });

        // Show success message
        toast.success("Trading account deleted successfully!");
      }
    },
    onError: (error: any) => {
      const message = error.response?.data?.message || "Failed to delete trading account";
      toast.error(message);
    },
  });
}

// Complete account setup
export function useCompleteAccountSetup() {
  const queryClient = useQueryClient();
  const { user } = useAuthStore();
  const { updateAccount } = useTradingAccountStore();

  return useMutation({
    mutationFn: async ({ 
      accountId, 
      setupData 
    }: { 
      accountId: string; 
      setupData: any 
    }) => {
      const response = await apiClient.post(`/trading-accounts/${accountId}/complete-setup`, setupData);
      return response.data;
    },
    onSuccess: (response: any, variables) => {
      if (response.success && response.data) {
        // Update store
        updateAccount(variables.accountId, { 
          ...response.data, 
          setupComplete: true 
        });

        // Invalidate accounts query
        queryClient.invalidateQueries({ 
          queryKey: tradingAccountKeys.list(user?.id || "") 
        });

        // Show success message
        toast.success("Account setup completed successfully!");
      }
    },
    onError: (error: any) => {
      const message = error.response?.data?.message || "Failed to complete account setup";
      toast.error(message);
    },
  });
}

// Hook to get the current active account with real-time updates
export function useActiveAccount() {
  const { getActiveAccount } = useTradingAccountStore();
  return getActiveAccount();
}

// Hook to switch accounts
export function useSwitchAccount() {
  const { switchAccount } = useTradingAccountStore();
  
  return (accountId: string) => {
    switchAccount(accountId);
    toast.success("Switched to account successfully!");
  };
}
