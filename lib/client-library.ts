import { AppDatabase, initApp } from "@gbozee/ultimate";


async function getApp(payload:{email?: string; salt?: string;}) {
    const { email, salt } = payload;
    const app = await initApp({
        db: {
            host: process.env.NEW_POCKETBASE_HOST!,
            email: process.env.NEW_POCKETBASE_EMAIL!,
            password: process.env.NEW_POCKETBASE_PASSWORD!,
        },
        email,
        salt: salt || process.env.SALT,
        getCredentials: async ({account, exchange, app_db }:{account: string, exchange: string, app_db: AppDatabase}) => {
            const credentials = await app_db.getUserCredentials();
            const credential = credentials.find((c: any) => c.exchange === exchange && c.name === account);
            if (!credential) {
                throw new Error("Credential not found");
            }
            return {
                api_key: credential.api_key,
                api_secret: credential.api_secret,
                email: credential.email,
            };
        },
    });
    return app
}

export { getApp }