import { getDBService } from "@/lib/services/db";

export async function validateSuperUserRequest(req: Request) {
  const token = req.headers.get("Authorization")?.replace("Bearer ", "");
  if (!token) {
    throw new Error("No token found");
  }

  const dbService = await getDBService();
  const user = await dbService.getSuperUserFromToken(token);

  if (!user) {
    throw new Error("Unauthorized");
  }
  return { user, token, dbService };
}
