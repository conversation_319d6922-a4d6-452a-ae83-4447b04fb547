"use client";

import { createContext, ReactNode, useContext, useState } from "react";
// Translation types
type TranslationObject = {
  [key: string]: string | TranslationObject;
};

type Translations = {
  [language: string]: TranslationObject;
};

// Helper function to get nested value from object using dot notation
function getNestedValue(
  obj: TranslationObject,
  path: string
): string | undefined {
  return path.split(".").reduce((current: any, key: string) => {
    return current && typeof current === "object" ? current[key] : undefined;
  }, obj) as string | undefined;
}
interface TranslationContextType {
  t: (
    key: string,
    vars?: Record<string, string | number>,
    fallback?: string
  ) => string;
}

const TranslationContext = createContext<TranslationContextType | undefined>(
  undefined
);

interface TranslationProviderProps {
  translations: Translations;
  children: ReactNode;
}

export function TranslationProvider({
  children,
  translations = {},
}: TranslationProviderProps) {
  // Global state for translations

  let [currentLanguage, setCurrentLanguage] = useState<string>("english");
  const currentTranslation = translations[currentLanguage] || {};

  function interpolate(str: string, vars: Record<string, string | number>) {
    return str.replace(/\{\{(.*?)\}\}/g, (_, key) => {
      const trimmedKey = key.trim();
      return vars[trimmedKey] !== undefined ? String(vars[trimmedKey]) : "";
    });
  }

  function translate(
    key: string,
    vars?: Record<string, string | number>,
    fallback?: string
  ): string {
    const translation = getNestedValue(currentTranslation, key);

    if (translation !== undefined) {
      return interpolate(translation, vars || {});
    }

    // If not found, return fallback or the key itself
    if (fallback !== undefined) {
      return fallback;
    }

    console.warn(
      `Translation key "${key}" not found for language "${currentLanguage}"`
    );
    return key; // Return the key as fallback
  }
  const value = { t: translate };

  return (
    <TranslationContext.Provider value={value}>
      {children}
    </TranslationContext.Provider>
  );
}

export function useTranslation() {
  const context = useContext(TranslationContext);
  if (context === undefined) {
    throw new Error("useTranslation must be used within a TranslationProvider");
  }
  return context;
}
