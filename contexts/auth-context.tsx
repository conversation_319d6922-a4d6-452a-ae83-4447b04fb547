"use client"

import { createContext, useContext, useState, useEffect, type ReactNode } from "react"

type TradingAccount = {
  id: string
  name: string
  exchange: string
  apiKey: string
  balance: string
  tradingPair: string
  isActive: boolean
  setupComplete: boolean
  createdAt: string
}

type User = {
  id: string
  name: string
  email: string
  hasCompletedOnboarding: boolean
  accounts: TradingAccount[]
  activeAccountId: string
}

type AuthContextType = {
  user: User | null
  isLoading: boolean
  isAuthenticated: boolean
  login: (email: string, password: string) => Promise<void>
  logout: () => void
  completeOnboarding: () => void
  switchAccount: (accountId: string) => void
  getActiveAccount: () => TradingAccount | undefined
  createAccount: (name: string) => Promise<TradingAccount>
  updateAccount: (accountId: string, data: Partial<TradingAccount>) => void
  deleteAccount: (accountId: string) => Promise<void>
  resumeAccountSetup: (accountId: string) => void
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

export function AuthProvider({ children }: { children: ReactNode }) {
  const [user, setUser] = useState<User | null>(null)
  const [isLoading, setIsLoading] = useState(true)

  // Simulate checking for existing session on mount
  useEffect(() => {
    const checkAuth = async () => {
      try {
        // In a real app, this would be an API call to validate the session
        const storedUser = localStorage.getItem("user")
        if (storedUser) {
          setUser(JSON.parse(storedUser))
        }
      } catch (error) {
        console.error("Authentication error:", error)
      } finally {
        setIsLoading(false)
      }
    }

    checkAuth()
  }, [])

  const login = async (email: string, password: string) => {
    setIsLoading(true)
    try {
      // Simulate API call
      await new Promise((resolve) => setTimeout(resolve, 1000))

      // Mock user data with a default account
      const defaultAccount: TradingAccount = {
        id: "account-1",
        name: "Main BTC Account",
        exchange: "Binance",
        apiKey: "xxxx-xxxx-xxxx-xxxx",
        balance: "$10,245.67",
        tradingPair: "BTC/USDT",
        isActive: true,
        setupComplete: true,
        createdAt: new Date().toISOString(),
      }

      const newUser: User = {
        id: "user-123",
        name: "John Doe",
        email,
        hasCompletedOnboarding: false,
        accounts: [defaultAccount],
        activeAccountId: defaultAccount.id,
      }

      setUser(newUser)
      localStorage.setItem("user", JSON.stringify(newUser))
    } catch (error) {
      console.error("Login error:", error)
      throw error
    } finally {
      setIsLoading(false)
    }
  }

  const logout = () => {
    setUser(null)
    localStorage.removeItem("user")
  }

  const completeOnboarding = () => {
    if (user) {
      // Mark the user as having completed onboarding
      const updatedUser = { ...user, hasCompletedOnboarding: true }

      // Also mark the active account as complete
      const updatedAccounts = user.accounts.map((account) =>
        account.id === user.activeAccountId ? { ...account, setupComplete: true } : account,
      )

      const finalUpdatedUser = {
        ...updatedUser,
        accounts: updatedAccounts,
      }

      setUser(finalUpdatedUser)
      localStorage.setItem("user", JSON.stringify(finalUpdatedUser))
    }
  }

  const switchAccount = (accountId: string) => {
    if (user) {
      const updatedUser = {
        ...user,
        activeAccountId: accountId,
        accounts: user.accounts.map((account) => ({
          ...account,
          isActive: account.id === accountId,
        })),
      }
      setUser(updatedUser)
      localStorage.setItem("user", JSON.stringify(updatedUser))
    }
  }

  const getActiveAccount = () => {
    if (!user) return undefined
    return user.accounts.find((account) => account.id === user.activeAccountId)
  }

  const createAccount = async (name: string): Promise<TradingAccount> => {
    if (!user) throw new Error("User not authenticated")

    // Simulate API call
    await new Promise((resolve) => setTimeout(resolve, 1000))

    // Create a new account with minimal information
    const newAccount: TradingAccount = {
      id: `account-${Date.now()}`,
      name: name.trim(),
      exchange: "Binance", // Default exchange
      apiKey: "", // Empty until setup
      balance: "$0.00", // Default balance
      tradingPair: "BTC/USDT", // Default trading pair
      isActive: false, // Not active by default
      setupComplete: false, // Setup not complete
      createdAt: new Date().toISOString(),
    }

    // Add the new account to the user's accounts
    const updatedAccounts = [...user.accounts, newAccount]

    const updatedUser = {
      ...user,
      accounts: updatedAccounts,
    }

    setUser(updatedUser)
    localStorage.setItem("user", JSON.stringify(updatedUser))

    return newAccount
  }

  const updateAccount = (accountId: string, data: Partial<TradingAccount>) => {
    if (!user) return

    const updatedAccounts = user.accounts.map((account) =>
      account.id === accountId ? { ...account, ...data } : account,
    )

    const updatedUser = {
      ...user,
      accounts: updatedAccounts,
    }

    setUser(updatedUser)
    localStorage.setItem("user", JSON.stringify(updatedUser))
  }

  const deleteAccount = async (accountId: string): Promise<void> => {
    if (!user) throw new Error("User not authenticated")

    // Prevent deleting the last account
    if (user.accounts.length <= 1) {
      throw new Error("Cannot delete the only account")
    }

    // Simulate API call
    await new Promise((resolve) => setTimeout(resolve, 1000))

    // Remove the account
    const updatedAccounts = user.accounts.filter((account) => account.id !== accountId)

    // If the active account is being deleted, switch to another account
    let activeAccountId = user.activeAccountId
    if (activeAccountId === accountId) {
      activeAccountId = updatedAccounts[0].id
      updatedAccounts[0].isActive = true
    }

    const updatedUser = {
      ...user,
      accounts: updatedAccounts,
      activeAccountId,
    }

    setUser(updatedUser)
    localStorage.setItem("user", JSON.stringify(updatedUser))
  }

  const resumeAccountSetup = (accountId: string) => {
    if (!user) return

    // Switch to the account
    switchAccount(accountId)

    // Set a flag in localStorage to indicate we're setting up this account
    localStorage.setItem("accountSetupId", accountId)
  }

  return (
    <AuthContext.Provider
      value={{
        user,
        isLoading,
        isAuthenticated: !!user,
        login,
        logout,
        completeOnboarding,
        switchAccount,
        getActiveAccount,
        createAccount,
        updateAccount,
        deleteAccount,
        resumeAccountSetup,
      }}
    >
      {children}
    </AuthContext.Provider>
  )
}

export function useAuth() {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error("useAuth must be used within an AuthProvider")
  }
  return context
}
