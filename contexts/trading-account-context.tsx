"use client"

import { createContext, useContext, useState, type ReactNode } from "react"

type TradingAccount = {
  id: string
  name: string
  exchange: string
  apiKey: string
  balance: string
  tradingPair: string
  isActive: boolean
  setupComplete: boolean
  createdAt: string
}

type TradingAccountContextType = {
  accounts: TradingAccount[]
  activeAccountId: string | null
  isLoading: boolean
  hasCompletedOnboarding: boolean
  switchAccount: (accountId: string) => void
  getActiveAccount: () => TradingAccount | undefined
  createAccount: (name: string) => Promise<TradingAccount>
  updateAccount: (accountId: string, data: Partial<TradingAccount>) => void
  deleteAccount: (accountId: string) => Promise<void>
  resumeAccountSetup: (accountId: string) => void
  completeOnboarding: () => void
}

const TradingAccountContext = createContext<TradingAccountContextType | undefined>(undefined)

export function TradingAccountProvider({ children }: { children: ReactNode }) {
  const [accounts, setAccounts] = useState<TradingAccount[]>([])
  const [activeAccountId, setActiveAccountId] = useState<string | null>(null)
  const [isLoading, setIsLoading] = useState(false)
  const [hasCompletedOnboarding, setHasCompletedOnboarding] = useState(false)

  const switchAccount = (accountId: string) => {
    const updatedAccounts = accounts.map((account) => ({
      ...account,
      isActive: account.id === accountId,
    }))

    setAccounts(updatedAccounts)
    setActiveAccountId(accountId)
  }

  const getActiveAccount = () => {
    if (!activeAccountId) return undefined
    return accounts.find((account) => account.id === activeAccountId)
  }

  const createAccount = async (name: string): Promise<TradingAccount> => {
    setIsLoading(true)

    try {
      // Simulate API call
      await new Promise((resolve) => setTimeout(resolve, 1000))

      const newAccount: TradingAccount = {
        id: `account-${Date.now()}`,
        name: name.trim(),
        exchange: "Binance",
        apiKey: "",
        balance: "$0.00",
        tradingPair: "BTC/USDT",
        isActive: false,
        setupComplete: false,
        createdAt: new Date().toISOString(),
      }

      const updatedAccounts = [...accounts, newAccount]
      setAccounts(updatedAccounts)

      return newAccount
    } finally {
      setIsLoading(false)
    }
  }

  const updateAccount = (accountId: string, data: Partial<TradingAccount>) => {
    const updatedAccounts = accounts.map((account) =>
      account.id === accountId ? { ...account, ...data } : account
    )
    setAccounts(updatedAccounts)
  }

  const deleteAccount = async (accountId: string): Promise<void> => {
    if (accounts.length <= 1) {
      throw new Error("Cannot delete the only account")
    }

    setIsLoading(true)

    try {
      // Simulate API call
      await new Promise((resolve) => setTimeout(resolve, 1000))

      const updatedAccounts = accounts.filter((account) => account.id !== accountId)

      // If the active account is being deleted, switch to another account
      let newActiveAccountId = activeAccountId
      if (activeAccountId === accountId) {
        newActiveAccountId = updatedAccounts[0]?.id || null
        if (updatedAccounts[0]) {
          updatedAccounts[0].isActive = true
        }
      }

      setAccounts(updatedAccounts)
      setActiveAccountId(newActiveAccountId)
    } finally {
      setIsLoading(false)
    }
  }

  const resumeAccountSetup = (accountId: string) => {
    switchAccount(accountId)
  }

  const completeOnboarding = () => {
    setHasCompletedOnboarding(true)

    // Mark the active account as complete
    if (activeAccountId) {
      updateAccount(activeAccountId, { setupComplete: true })
    }
  }

  return (
    <TradingAccountContext.Provider
      value={{
        accounts,
        activeAccountId,
        isLoading,
        hasCompletedOnboarding,
        switchAccount,
        getActiveAccount,
        createAccount,
        updateAccount,
        deleteAccount,
        resumeAccountSetup,
        completeOnboarding,
      }}
    >
      {children}
    </TradingAccountContext.Provider>
  )
}

export function useTradingAccount() {
  const context = useContext(TradingAccountContext)
  if (context === undefined) {
    throw new Error("useTradingAccount must be used within a TradingAccountProvider")
  }
  return context
}
