"use server";

import { getDBService } from "@/lib/services/db";

export async function loadTranslations() {
  const LANGUAGES: Record<string, any> = {
    english: await import("../translations/english.json"),
  };

  const result: Record<string, any> = {};
  Object.keys(LANGUAGES).forEach((key) => {
    result[key] = LANGUAGES[key].default;
  });

  return result;
}

export async function getAdminFollowers(adminId: string) {
  const dbService = await getDBService();
  const result = await dbService.getAdminFollowers(adminId);
  return result;
}

export async function approveInvitee(userId: string, adminId: string) {
  const dbService = await getDBService();
  await dbService.approveUser(userId);
  const result = await dbService.getAdminFollowers(adminId);
  return result;
}

export async function rejectInvitee(userId: string, adminId: string) {
  const dbService = await getDBService();
  await dbService.rejectUser(userId);
  const result = await dbService.getAdminFollowers(adminId);
  return result;
}

// creeate a new follower, if email is already in the database, update the admin Field to myself,else create a new user with admin field set to myself and approved to true also set role to user.
export async function createCircleMember(
  member: { email: string; name: string; id?: string },
  adminId: string
) {
  let user = null;
  const { email, name } = member;
  const dbService = await getDBService();
  if (member?.id) {
    user = await dbService.updateUserRecord({
      userId: member.id,
      updates: { admin: adminId },
    });
  } else {
    user = await dbService.createUserRecord({
      email,
      name,
      admin: adminId,
      approved: true,
      role: "user",
    });
  }
  // get the updated list of the followers after adding or updating
  const result = await dbService.getAdminFollowers(adminId);
  return result;
}

// remove followers should remove the admin field from the user
export async function dropCircleMember(userId: string, adminId: string) {
  const dbService = await getDBService();
  await dbService.deleteUser(userId);
  // get the updated list of the followers after adding or updating
  const result = await dbService.getAdminFollowers(adminId);
  return result;
}
