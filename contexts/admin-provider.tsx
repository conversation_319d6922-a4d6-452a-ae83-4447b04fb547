"use client";

import { WalletFormDialog } from "@/app/components/WalletFormDialog";
import { AppDialogRef } from "@/app/components/dialog";
import { useToast } from "@/hooks/use-toast";
import { createContext, ReactNode, useContext, useRef, useState } from "react";
import {
  approveInvitee,
  createCircleMember,
  dropCircleMember,
  rejectInvitee,
} from "./actions";
import { useUser } from "./user-provider";

interface AdminContextType {
  followers: UserProps[];
  setFollowers: React.Dispatch<React.SetStateAction<UserProps[]>>;
  wallet: WalletType;
  setWallet: React.Dispatch<React.SetStateAction<WalletType>>;
  dialogRef: React.RefObject<AppDialogRef>;
}

export const AdminContext = createContext<AdminContextType | undefined>(
  undefined
);

interface AdminProviderProps {
  children: ReactNode;
  followers: UserProps[];
}

export type WalletType = {
  id?: string;
  address: string;
  network: string;
  blacklisted?: boolean;
};

export function AdminProvider({
  children,
  followers: defaultFollowers,
}: AdminProviderProps) {
  const { wallet: defaultWallet } = useUser();
  const dialogRef = useRef<AppDialogRef>(null) as React.RefObject<AppDialogRef>;
  const [followers, setFollowers] = useState<UserProps[]>(defaultFollowers);
  const [wallet, setWallet] = useState<WalletType>(
    defaultWallet || ({} as WalletType)
  );

  const value = {
    dialogRef,
    followers,
    setFollowers,
    wallet,
    setWallet,
  };

  return (
    <AdminContext.Provider value={value}>
      {children}
      <WalletFormDialog dialogRef={dialogRef} />
    </AdminContext.Provider>
  );
}

export function useAdmin() {
  const { user: adminUser, isAdmin } = useUser();
  const { toast } = useToast();
  const context = useContext(AdminContext);
  if (context === undefined) {
    throw new Error("useUser must be used within a UserProvider");
  }
  const { followers, setFollowers, wallet, dialogRef, setWallet } = context;

  const total = followers.length;
  const approved = followers.filter((user) => user.approved).length;
  const pending = followers.filter((user) => !user.approved).length;
  const rejected = 0; // not tracked yet

  const stats = { total, approved, pending, rejected };

  function onOpenWalletDialog() {
    const url = new URL(window.location.href);
    url.searchParams.set("withdraw", "true");
    window.history.pushState({}, "", url);
    dialogRef.current?.open();
  }

  function onCloseWalletDialog(callClose?: boolean) {
    const url = new URL(window.location.href);
    url.searchParams.delete("withdraw");
    window.history.pushState({}, "", url);
    if (callClose) {
      dialogRef.current?.close();
    }
  }

  async function onApprove(userId: string) {
    try {
      const approvedFollowers = await approveInvitee(userId, adminUser?.id!);
      if (!approvedFollowers) throw new Error("Approval failed");
      setFollowers(approvedFollowers);
      toast({
        title: "User approved successfully",
        description: "User approved successfully",
        variant: "default",
      });
    } catch (error) {
      toast({
        title: "Failed to approve user",
        description: "Something went wrong",
        variant: "destructive",
      });
    }
  }

  async function onReject(userId: string) {
    try {
      const updatedFollowers = await rejectInvitee(userId, adminUser?.id!);
      if (!updatedFollowers) throw new Error("Rejection failed");

      setFollowers(updatedFollowers);
      toast({
        title: "Member Removed",
        description: "The member has been removed from your circle.",
        variant: "default",
      });
    } catch (error) {
      toast({
        title: "Failed to remove member",
        description: "Something went wrong. Please try again.",
        variant: "destructive",
      });
    }
  }

  function getCircleMembers() {
    return followers
      .filter((follower) => follower.approved)
      .map((follower) => {
        return {
          id: follower.id,
          name: follower.name,
          email: follower.email,
        };
      });
  }
  const circleMembers = getCircleMembers();

  async function addCircleMember(member: { email: string; name: string }) {
    try {
      const updatedFollowers = await createCircleMember(member, adminUser?.id!);
      setFollowers(updatedFollowers);
      toast({
        title: "Member Added",
        description: `A new member has been added to your circle.`,
      });
    } catch (error) {
      toast({
        title: "Failed to add member",
        description: "Failed to add member",
        variant: "destructive",
      });
    }
  }

  async function deleteCircleMember(memberId: string) {
    try {
      const updatedFollowers = await dropCircleMember(memberId, adminUser?.id!);
      setFollowers(updatedFollowers);
      toast({
        title: "Member Deleted",
        description: ` The member has been deleted.`,
      });
    } catch (error: any) {
      toast({
        title: "Failed to remove member",
        description: error.message || "Failed to remove member",
        variant: "destructive",
      });
    }
  }

  return {
    onRemoveCircleMember: onReject,
    deleteCircleMember,
    addCircleMember,
    onApprove,
    onReject,
    stats,
    followers,
    user: adminUser,
    isAdmin,
    circleMembers,
    wallet,
    setWallet,
    dialogRef,
    onOpenWalletDialog,
    onCloseWalletDialog,
  };
}
