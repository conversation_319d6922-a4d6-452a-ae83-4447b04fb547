"use client";

import { fetchUser } from "@/app/auth/actions";
import { createContext, ReactNode, useContext, useState } from "react";

interface UserContextType {
  user: UserProps | null;
  isAuthenticated: boolean;
  isAdmin: boolean;
  approved: boolean;
  loading: boolean;
  error: string | null;
  refetch: () => void;
  authenticateUser: () => void;
}

const UserContext = createContext<UserContextType | undefined>(undefined);

interface UserProviderProps {
  children: ReactNode;
  user: UserProps | null;
}

export function UserProvider({
  children,
  user: initialUser,
}: UserProviderProps) {
  const [user, setUser] = useState<UserProps | null>(initialUser);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isAuthenticated, setIsAuthenticated] = useState(Boolean(user));

  const refetch = async () => {
    if (!user) return; // no user to refetch

    try {
      setLoading(true);
      setError(null);

      const result = await fetchUser(user.id); // call server action

      if (result.success) {
        setUser(result.user ?? null);
      } else {
        setError(result.error ?? "Unknown error");
      }
    } catch (err: any) {
      setError(err.message || "Unexpected error");
    } finally {
      setLoading(false);
    }
  };

  const authenticateUser = () => {
    setIsAuthenticated(true);
  };

  const value = {
    user,
    isAuthenticated,
    isAdmin: user?.role === "admin" || false,
    approved: user?.approved ?? false,
    loading,
    error,
    refetch,
    authenticateUser,
  };

  return <UserContext.Provider value={value}>{children}</UserContext.Provider>;
}

export function useUser() {
  const context = useContext(UserContext);
  if (context === undefined) {
    throw new Error("useUser must be used within a UserProvider");
  }
  const { user } = context;

  const wallet = {
    id: user?.wallet,
    address: user?.expand?.wallet?.address || "",
    network: user?.expand?.wallet?.network || "",
    blacklisted: user?.expand?.wallet?.blacklisted || false,
  };

  return { ...context, wallet };
}
