"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Loader2, CheckCircle, XCircle, Database, User, Wifi } from "lucide-react";
import { testConnection, initializeUser } from "@/lib/actions/onboarding-actions";

export default function TestConnectionPage() {
  const [isTestingConnection, setIsTestingConnection] = useState(false);
  const [isInitializingUser, setIsInitializingUser] = useState(false);
  const [connectionResult, setConnectionResult] = useState<any>(null);
  const [userResult, setUserResult] = useState<any>(null);

  const handleTestConnection = async () => {
    setIsTestingConnection(true);
    setConnectionResult(null);
    
    try {
      const result = await testConnection();
      setConnectionResult(result);
    } catch (error) {
      setConnectionResult({
        success: false,
        message: error instanceof Error ? error.message : "Unknown error",
      });
    } finally {
      setIsTestingConnection(false);
    }
  };

  const handleInitializeUser = async () => {
    setIsInitializingUser(true);
    setUserResult(null);
    
    try {
      const result = await initializeUser();
      setUserResult(result);
    } catch (error) {
      setUserResult({
        success: false,
        message: error instanceof Error ? error.message : "Unknown error",
      });
    } finally {
      setIsInitializingUser(false);
    }
  };

  return (
    <div className="container mx-auto py-8 max-w-4xl">
      <div className="mb-8">
        <h1 className="text-3xl font-bold mb-2">Connection Test</h1>
        <p className="text-muted-foreground">
          Test the connection to PocketBase and Ultimate package integration
        </p>
      </div>

      <div className="grid gap-6">
        {/* PocketBase Connection Test */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Database className="h-5 w-5" />
              PocketBase Connection
            </CardTitle>
            <CardDescription>
              Test the connection to PocketBase database using the Ultimate package
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <Button 
              onClick={handleTestConnection}
              disabled={isTestingConnection}
              className="w-full"
            >
              {isTestingConnection ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Testing Connection...
                </>
              ) : (
                <>
                  <Wifi className="mr-2 h-4 w-4" />
                  Test PocketBase Connection
                </>
              )}
            </Button>

            {connectionResult && (
              <div className="p-4 rounded-lg border">
                <div className="flex items-center gap-2 mb-2">
                  {connectionResult.success ? (
                    <CheckCircle className="h-5 w-5 text-green-500" />
                  ) : (
                    <XCircle className="h-5 w-5 text-red-500" />
                  )}
                  <Badge variant={connectionResult.success ? "default" : "destructive"}>
                    {connectionResult.success ? "Success" : "Failed"}
                  </Badge>
                </div>
                <p className="text-sm text-muted-foreground">
                  {connectionResult.message}
                </p>
              </div>
            )}
          </CardContent>
        </Card>

        {/* User Initialization Test */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <User className="h-5 w-5" />
              User Initialization
            </CardTitle>
            <CardDescription>
              Initialize the current user in the Ultimate app system
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <Button 
              onClick={handleInitializeUser}
              disabled={isInitializingUser}
              className="w-full"
            >
              {isInitializingUser ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Initializing User...
                </>
              ) : (
                <>
                  <User className="mr-2 h-4 w-4" />
                  Initialize Current User
                </>
              )}
            </Button>

            {userResult && (
              <div className="p-4 rounded-lg border">
                <div className="flex items-center gap-2 mb-2">
                  {userResult.success ? (
                    <CheckCircle className="h-5 w-5 text-green-500" />
                  ) : (
                    <XCircle className="h-5 w-5 text-red-500" />
                  )}
                  <Badge variant={userResult.success ? "default" : "destructive"}>
                    {userResult.success ? "Success" : "Failed"}
                  </Badge>
                </div>
                <p className="text-sm text-muted-foreground mb-2">
                  {userResult.message}
                </p>
                {userResult.user && (
                  <div className="text-xs bg-muted p-2 rounded">
                    <pre>{JSON.stringify(userResult.user, null, 2)}</pre>
                  </div>
                )}
              </div>
            )}
          </CardContent>
        </Card>

        {/* Environment Variables Check */}
        <Card>
          <CardHeader>
            <CardTitle>Environment Variables</CardTitle>
            <CardDescription>
              Check if required environment variables are configured (client-side check)
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid gap-2">
              {[
                'NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY',
                'CLERK_SECRET_KEY'
              ].map((envVar) => (
                <div key={envVar} className="flex items-center justify-between">
                  <span className="text-sm font-mono">{envVar}</span>
                  <Badge variant={envVar.startsWith('NEXT_PUBLIC_') ? "default" : "secondary"}>
                    {envVar.startsWith('NEXT_PUBLIC_') ? "Public" : "Server-only"}
                  </Badge>
                </div>
              ))}
              <div className="text-xs text-muted-foreground mt-2">
                Server-side environment variables (POCKETBASE_*, SALT) are checked during connection test
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
