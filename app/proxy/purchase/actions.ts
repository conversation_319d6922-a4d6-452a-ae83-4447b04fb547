"use server";

import { revalidate<PERSON>ath } from "next/cache";
import { createUltimateService } from "@/lib/services/ultimate-service";
import { ProxyPurchasePayload, ProxyPurchaseResponse } from "./types";
import { getLoggedInUser } from "@/app/auth/actions";
import { getDBService } from "@/lib/services/db";

/**
 * Server action to handle proxy purchase
 */
export async function purchaseProxyAction(
  payload: ProxyPurchasePayload
): Promise<ProxyPurchaseResponse> {
  const dbService = await getDBService();

  try {
    // Get current user
    const user = await getLoggedInUser();
    if (!user) {
      return {
        success: false,
        error: "User not authenticated",
      };
    }

    const email = user.email;

    // Initialize Ultimate service
    const service = createUltimateService();
    const userResult = await dbService.getUserByEmail(email);

    if (!userResult) {
      return {
        success: false,
        error: "User not found in database",
      };
    }

    // Simulate payment processing
    await new Promise((resolve) => setTimeout(resolve, 2000));

    // Generate proxy credentials based on purchased plan
    const proxyCredentials = generateProxyCredentials(payload.planId, email);

    // Create proxy in database
    const proxyResult = await service.createProxy({
      ip_address: `${proxyCredentials.username}:${proxyCredentials.password}@${proxyCredentials.host}:${proxyCredentials.port}`,
      type: proxyCredentials.type,
      userId: userResult.id,
    });

    if (!proxyResult.success) {
      return {
        success: false,
        error: "Failed to create proxy in database",
      };
    }

    // Calculate expiration date based on billing cycle
    const expiresAt = calculateExpirationDate(payload.billingCycle);

    revalidatePath("/onboarding");
    revalidatePath("/proxy/purchase");

    return {
      success: true,
      message: "Proxy purchased successfully!",
      data: {
        proxyId: proxyResult.data?.id ?? "",
        proxyCredentials,
        subscriptionId: `sub_${Date.now()}_${Math.random()
          .toString(36)
          .substr(2, 9)}`,
        expiresAt,
      },
    };
  } catch (error) {
    console.error("Error purchasing proxy:", error);
    return {
      success: false,
      error: "Failed to purchase proxy. Please try again.",
    };
  }
}

/**
 * Generate proxy credentials based on plan and user
 */
function generateProxyCredentials(planId: string, email: string) {
  const username = email.split("@")[0].toLowerCase();
  const password = Math.random().toString(36).substring(2, 15);

  // Different proxy servers based on plan
  const proxyServers = {
    basic: { host: "proxy-basic.tradesmart.com", port: 8080 },
    premium: { host: "proxy-premium.tradesmart.com", port: 8080 },
    enterprise: { host: "proxy-enterprise.tradesmart.com", port: 8080 },
  };

  const server =
    proxyServers[planId as keyof typeof proxyServers] || proxyServers.basic;

  return {
    username,
    password,
    host: server.host,
    port: server.port,
    type: "http" as const,
  };
}

/**
 * Calculate expiration date based on billing cycle
 */
function calculateExpirationDate(billingCycle: string): string {
  const now = new Date();

  switch (billingCycle) {
    case "monthly":
      now.setMonth(now.getMonth() + 1);
      break;
    case "quarterly":
      now.setMonth(now.getMonth() + 3);
      break;
    case "yearly":
      now.setFullYear(now.getFullYear() + 1);
      break;
    default:
      now.setMonth(now.getMonth() + 1);
  }

  return now.toISOString();
}
