/**
 * TypeScript interfaces for proxy purchase flow
 */

export interface ProxyPlan {
  id: string;
  name: string;
  price: number;
  description: string;
  features: string[];
  popular?: boolean;
  bandwidth: string;
  locations: number;
  concurrent: number;
}

export interface ProxyPurchasePayload {
  planId: string;
  billingCycle: "monthly" | "quarterly" | "yearly";
  paymentMethod: "card" | "paypal";
  cardDetails?: {
    cardNumber: string;
    expiryDate: string;
    cvv: string;
    cardholderName: string;
  };
}

export interface ProxyPurchaseResponse {
  success: boolean;
  message?: string;
  data?: {
    proxyId: string;
    proxyCredentials: {
      username: string;
      password: string;
      host: string;
      port: number;
      type: "http" | "socks5";
    };
    subscriptionId: string;
    expiresAt: string;
  };
  error?: string;
}

export interface ActionResult {
  success: boolean;
  message?: string;
  data?: any;
  error?: string;
}
