"use client";

import { Suspense } from "react";
import { useState, useTransition } from "react";
import { use<PERSON><PERSON>er, useSearchParams } from "next/navigation";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Separator } from "@/components/ui/separator";
import { Badge } from "@/components/ui/badge";
import { Container } from "@/components/container";
import { MobileNav } from "@/components/mobile-nav";
import { BottomNav } from "@/components/bottom-nav";
import { 
  ArrowLeft, 
  Check, 
  CreditCard, 
  Shield, 
  Globe, 
  Zap, 
  Users,
  BarChart2 
} from "lucide-react";
import { toast } from "sonner";
import { purchaseProxyAction } from "./actions";
import { ProxyPlan, ProxyPurchasePayload } from "./types";

const proxyPlans: ProxyPlan[] = [
  {
    id: "basic",
    name: "Basic Proxy",
    price: 15,
    description: "Perfect for individual traders getting started",
    bandwidth: "50GB",
    locations: 5,
    concurrent: 2,
    features: [
      "50GB monthly bandwidth",
      "5 proxy locations",
      "2 concurrent connections",
      "HTTP/HTTPS support",
      "24/7 uptime monitoring",
      "Email support"
    ]
  },
  {
    id: "premium",
    name: "Premium Proxy",
    price: 35,
    description: "For serious traders with higher volume needs",
    bandwidth: "200GB",
    locations: 15,
    concurrent: 10,
    popular: true,
    features: [
      "200GB monthly bandwidth",
      "15 proxy locations worldwide",
      "10 concurrent connections",
      "HTTP/HTTPS & SOCKS5 support",
      "99.9% uptime guarantee",
      "Priority support",
      "IP rotation every 10 minutes"
    ]
  },
  {
    id: "enterprise",
    name: "Enterprise Proxy",
    price: 75,
    description: "For professional trading operations",
    bandwidth: "Unlimited",
    locations: 50,
    concurrent: 50,
    features: [
      "Unlimited bandwidth",
      "50+ proxy locations globally",
      "50 concurrent connections",
      "All protocols supported",
      "99.99% uptime SLA",
      "Dedicated account manager",
      "Custom IP rotation settings",
      "API access for automation"
    ]
  }
];

function ProxyPurchaseContent() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const returnTo = searchParams.get('returnTo') || '/onboarding';
  
  const [selectedPlan, setSelectedPlan] = useState("premium");
  const [billingCycle, setBillingCycle] = useState<"monthly" | "quarterly" | "yearly">("monthly");
  const [paymentMethod, setPaymentMethod] = useState<"card" | "paypal">("card");
  const [isPending, startTransition] = useTransition();

  // Card form state
  const [cardNumber, setCardNumber] = useState("");
  const [expiryDate, setExpiryDate] = useState("");
  const [cvv, setCvv] = useState("");
  const [cardholderName, setCardholderName] = useState("");

  const selectedPlanData = proxyPlans.find(plan => plan.id === selectedPlan);
  
  const getBillingMultiplier = () => {
    switch (billingCycle) {
      case "quarterly": return 3 * 0.9; // 10% discount
      case "yearly": return 12 * 0.8; // 20% discount
      default: return 1;
    }
  };

  const getTotalPrice = () => {
    if (!selectedPlanData) return 0;
    return Math.round(selectedPlanData.price * getBillingMultiplier());
  };

  const handlePurchase = () => {
    if (!selectedPlanData) {
      toast.error("Please select a plan");
      return;
    }

    if (paymentMethod === "card") {
      if (!cardNumber || !expiryDate || !cvv || !cardholderName) {
        toast.error("Please fill in all card details");
        return;
      }
    }

    startTransition(async () => {
      const payload: ProxyPurchasePayload = {
        planId: selectedPlan,
        billingCycle,
        paymentMethod,
        ...(paymentMethod === "card" && {
          cardDetails: {
            cardNumber,
            expiryDate,
            cvv,
            cardholderName
          }
        })
      };

      const result = await purchaseProxyAction(payload);

      if (result.success) {
        toast.success(result.message || "Proxy purchased successfully!");
        // Navigate back to onboarding or specified return URL
        router.push(returnTo);
      } else {
        toast.error(result.error || "Failed to purchase proxy");
      }
    });
  };

  return (
    <div className="flex min-h-screen flex-col">
      <header className="h-16 flex items-center border-b">
        <Container className="flex w-full items-center justify-between">
          <div className="flex items-center gap-4">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => router.push(returnTo)}
              className="flex items-center gap-2"
            >
              <ArrowLeft className="h-4 w-4" />
              Back to Onboarding
            </Button>
            <div className="flex items-center gap-2">
              <BarChart2 className="h-6 w-6 text-[#245c1a]" />
              <span className="text-xl font-bold">TradeSmart</span>
            </div>
          </div>
          <div>
            <MobileNav />
          </div>
        </Container>
      </header>

      <main className="flex-1 py-8 pb-20 md:pb-8">
        <Container className="max-w-6xl">
          <div className="mb-8 text-center">
            <h1 className="text-3xl font-bold">Get Your Trading Proxy</h1>
            <p className="text-gray-500 mt-2">
              Secure, fast, and reliable proxy services for your trading operations
            </p>
          </div>

          <div className="grid gap-8 lg:grid-cols-3">
            {/* Plan Selection */}
            <div className="lg:col-span-2">
              <div className="mb-6">
                <h2 className="text-xl font-semibold mb-4">Choose Your Plan</h2>
                <div className="grid gap-4 md:grid-cols-3">
                  {proxyPlans.map((plan) => (
                    <Card 
                      key={plan.id} 
                      className={`relative cursor-pointer transition-all ${
                        selectedPlan === plan.id ? "border-[#245c1a] border-2 shadow-md" : "hover:shadow-sm"
                      }`}
                      onClick={() => setSelectedPlan(plan.id)}
                    >
                      {plan.popular && (
                        <Badge className="absolute -top-2 left-1/2 transform -translate-x-1/2 bg-[#245c1a]">
                          Most Popular
                        </Badge>
                      )}
                      <CardHeader className="pb-3">
                        <CardTitle className="text-lg">{plan.name}</CardTitle>
                        <CardDescription className="text-sm">{plan.description}</CardDescription>
                        <div className="mt-2">
                          <span className="text-2xl font-bold">${plan.price}</span>
                          <span className="text-sm text-gray-500">/month</span>
                        </div>
                      </CardHeader>
                      <CardContent className="pt-0">
                        <div className="grid grid-cols-3 gap-2 text-xs mb-3">
                          <div className="text-center">
                            <Globe className="h-4 w-4 mx-auto mb-1 text-[#245c1a]" />
                            <div className="font-medium">{plan.locations}</div>
                            <div className="text-gray-500">Locations</div>
                          </div>
                          <div className="text-center">
                            <Zap className="h-4 w-4 mx-auto mb-1 text-[#245c1a]" />
                            <div className="font-medium">{plan.bandwidth}</div>
                            <div className="text-gray-500">Bandwidth</div>
                          </div>
                          <div className="text-center">
                            <Users className="h-4 w-4 mx-auto mb-1 text-[#245c1a]" />
                            <div className="font-medium">{plan.concurrent}</div>
                            <div className="text-gray-500">Concurrent</div>
                          </div>
                        </div>
                        <ul className="space-y-1">
                          {plan.features.slice(0, 3).map((feature, index) => (
                            <li key={index} className="flex items-center text-xs">
                              <Check className="mr-2 h-3 w-3 text-[#245c1a] flex-shrink-0" />
                              <span>{feature}</span>
                            </li>
                          ))}
                        </ul>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </div>

              {/* Billing Cycle */}
              <div className="mb-6">
                <h3 className="text-lg font-semibold mb-3">Billing Cycle</h3>
                <RadioGroup value={billingCycle} onValueChange={(value) => setBillingCycle(value as any)}>
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="monthly" id="monthly" />
                    <Label htmlFor="monthly">Monthly</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="quarterly" id="quarterly" />
                    <Label htmlFor="quarterly">Quarterly (10% discount)</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="yearly" id="yearly" />
                    <Label htmlFor="yearly">Yearly (20% discount)</Label>
                  </div>
                </RadioGroup>
              </div>
            </div>

            {/* Payment Section */}
            <div>
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <CreditCard className="h-5 w-5" />
                    Payment Details
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  {/* Order Summary */}
                  <div className="bg-gray-50 p-3 rounded-lg">
                    <div className="flex justify-between items-center mb-2">
                      <span className="font-medium">{selectedPlanData?.name}</span>
                      <span>${selectedPlanData?.price}/mo</span>
                    </div>
                    <div className="flex justify-between items-center text-sm text-gray-600">
                      <span>Billing: {billingCycle}</span>
                      <span>×{getBillingMultiplier()}</span>
                    </div>
                    <Separator className="my-2" />
                    <div className="flex justify-between items-center font-semibold">
                      <span>Total</span>
                      <span>${getTotalPrice()}</span>
                    </div>
                  </div>

                  {/* Payment Method */}
                  <div>
                    <Label className="text-sm font-medium">Payment Method</Label>
                    <RadioGroup value={paymentMethod} onValueChange={(value) => setPaymentMethod(value as any)} className="mt-2">
                      <div className="flex items-center space-x-2">
                        <RadioGroupItem value="card" id="card" />
                        <Label htmlFor="card">Credit/Debit Card</Label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <RadioGroupItem value="paypal" id="paypal" />
                        <Label htmlFor="paypal">PayPal</Label>
                      </div>
                    </RadioGroup>
                  </div>

                  {/* Card Details */}
                  {paymentMethod === "card" && (
                    <div className="space-y-3">
                      <div>
                        <Label htmlFor="cardholderName" className="text-sm">Cardholder Name</Label>
                        <Input
                          id="cardholderName"
                          value={cardholderName}
                          onChange={(e) => setCardholderName(e.target.value)}
                          placeholder="John Doe"
                        />
                      </div>
                      <div>
                        <Label htmlFor="cardNumber" className="text-sm">Card Number</Label>
                        <Input
                          id="cardNumber"
                          value={cardNumber}
                          onChange={(e) => setCardNumber(e.target.value)}
                          placeholder="1234 5678 9012 3456"
                        />
                      </div>
                      <div className="grid grid-cols-2 gap-3">
                        <div>
                          <Label htmlFor="expiryDate" className="text-sm">Expiry Date</Label>
                          <Input
                            id="expiryDate"
                            value={expiryDate}
                            onChange={(e) => setExpiryDate(e.target.value)}
                            placeholder="MM/YY"
                          />
                        </div>
                        <div>
                          <Label htmlFor="cvv" className="text-sm">CVV</Label>
                          <Input
                            id="cvv"
                            value={cvv}
                            onChange={(e) => setCvv(e.target.value)}
                            placeholder="123"
                          />
                        </div>
                      </div>
                    </div>
                  )}

                  <Button
                    onClick={handlePurchase}
                    className="w-full bg-[#245c1a] hover:bg-[#1a4513]"
                    disabled={isPending}
                  >
                    {isPending ? "Processing..." : `Purchase for $${getTotalPrice()}`}
                  </Button>

                  <div className="text-xs text-gray-500 text-center">
                    <Shield className="h-3 w-3 inline mr-1" />
                    Secure payment processing
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </Container>
      </main>

      <BottomNav />
    </div>
  );
}

export default function ProxyPurchasePage() {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <ProxyPurchaseContent />
    </Suspense>
  );
}
