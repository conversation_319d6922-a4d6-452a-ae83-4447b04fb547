"use client";

import { BottomNav } from "@/components/bottom-nav";
import { Container } from "@/components/container";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Calendar } from "@/components/ui/calendar";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { useUser } from "@/contexts/user-provider";
import {
  format,
  isAfter,
  parseISO,
  subDays,
  subMonths,
  subWeeks,
} from "date-fns";
import { ArrowLeft, CalendarIcon } from "lucide-react";
import { useRouter } from "next/navigation";
import { useState } from "react";

// Mock profit transfer data
const mockProfitTransfers = [
  {
    id: "pt-001",
    amount: 245.67,
    date: "2025-05-15T14:32:21",
    tradingPair: "BTC/USDT",
    status: "completed",
    txId: "0x8a7b9c6d5e4f3g2h1i",
  },
  {
    id: "pt-002",
    amount: 178.32,
    date: "2025-05-10T09:15:43",
    tradingPair: "BTC/USDT",
    status: "completed",
    txId: "0x7b6c5d4e3f2g1h0i9j",
  },
  {
    id: "pt-003",
    amount: 312.45,
    date: "2025-05-05T16:45:12",
    tradingPair: "BTC/USDT",
    status: "completed",
    txId: "0x6c5d4e3f2g1h0i9j8k",
  },
  {
    id: "pt-004",
    amount: 89.21,
    date: "2025-04-28T11:22:05",
    tradingPair: "BTC/USDT",
    status: "completed",
    txId: "0x5d4e3f2g1h0i9j8k7l",
  },
  {
    id: "pt-005",
    amount: 156.78,
    date: "2025-04-20T08:34:19",
    tradingPair: "BTC/USDT",
    status: "completed",
    txId: "0x4e3f2g1h0i9j8k7l6m",
  },
  {
    id: "pt-006",
    amount: 267.89,
    date: "2025-04-15T15:56:32",
    tradingPair: "BTC/USDT",
    status: "completed",
    txId: "0x3f2g1h0i9j8k7l6m5n",
  },
  {
    id: "pt-007",
    amount: 198.45,
    date: "2025-04-05T10:12:45",
    tradingPair: "BTC/USDT",
    status: "completed",
    txId: "0x2g1h0i9j8k7l6m5n4o",
  },
  {
    id: "pt-008",
    amount: 423.12,
    date: "2025-03-25T13:45:22",
    tradingPair: "BTC/USDT",
    status: "completed",
    txId: "0x1h0i9j8k7l6m5n4o3p",
  },
  {
    id: "pt-009",
    amount: 176.34,
    date: "2025-03-15T09:23:11",
    tradingPair: "BTC/USDT",
    status: "completed",
    txId: "0x0i9j8k7l6m5n4o3p2q",
  },
  {
    id: "pt-010",
    amount: 289.67,
    date: "2025-03-05T14:18:37",
    tradingPair: "BTC/USDT",
    status: "completed",
    txId: "0x9j8k7l6m5n4o3p2q1r",
  },
  {
    id: "pt-011",
    amount: 345.21,
    date: "2025-02-20T11:05:48",
    tradingPair: "BTC/USDT",
    status: "completed",
    txId: "0x8k7l6m5n4o3p2q1r0s",
  },
  {
    id: "pt-012",
    amount: 132.56,
    date: "2025-02-10T16:37:29",
    tradingPair: "BTC/USDT",
    status: "completed",
    txId: "0x7l6m5n4o3p2q1r0s9t",
  },
];

export default function ProfitsPage() {
  const router = useRouter();
  const { isAuthenticated } = useUser();

  const [timeframe, setTimeframe] = useState("all");
  const [date, setDate] = useState<Date>();
  const [month, setMonth] = useState<Date>();
  const [year, setYear] = useState<number>(new Date().getFullYear());
  const [customDateRange, setCustomDateRange] = useState<{
    from: Date | undefined;
    to: Date | undefined;
  }>({
    from: undefined,
    to: undefined,
  });

  const [dayPopoverOpen, setDayPopoverOpen] = useState(false);
  const [weekPopoverOpen, setWeekPopoverOpen] = useState(false);
  const [monthPopoverOpen, setMonthPopoverOpen] = useState(false);
  const [customPopoverOpen, setCustomPopoverOpen] = useState(false);

  // Redirect to login if not authenticated
  if (!isAuthenticated) {
    router.push("/auth/login");
    return null;
  }

  // Filter profits based on selected timeframe
  const filteredProfits = mockProfitTransfers.filter((profit) => {
    const profitDate = parseISO(profit.date);
    const now = new Date();

    switch (timeframe) {
      case "day":
        return date
          ? format(profitDate, "yyyy-MM-dd") === format(date, "yyyy-MM-dd")
          : isAfter(profitDate, subDays(now, 1));
      case "week":
        return customDateRange.from && customDateRange.to
          ? profitDate >= customDateRange.from &&
              profitDate <= customDateRange.to
          : isAfter(profitDate, subWeeks(now, 1));
      case "month":
        return month
          ? format(profitDate, "yyyy-MM") === format(month, "yyyy-MM")
          : isAfter(profitDate, subMonths(now, 1));
      case "year":
        return profitDate.getFullYear() === year;
      case "custom":
        if (customDateRange.from && customDateRange.to) {
          return (
            profitDate >= customDateRange.from &&
            profitDate <= customDateRange.to
          );
        }
        return true;
      default:
        return true;
    }
  });

  // Calculate total profits for the selected timeframe
  const totalProfits = filteredProfits.reduce(
    (sum, profit) => sum + profit.amount,
    0
  );

  return (
    <div className="flex min-h-screen flex-col">
      <main className="flex-1 py-6 pb-20 md:pb-6">
        <Container className="max-w-6xl">
          <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6 gap-4">
            <div>
              <div className="flex items-center gap-4 mb-4">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => router.back()}
                  className="flex items-center gap-2"
                >
                  <ArrowLeft className="h-4 w-4" />
                  Back
                </Button>
              </div>
              <h1 className="text-2xl font-bold">Profit Transfers</h1>
              <p className="text-gray-500">
                Track profits automatically sent to your Binance account
              </p>
            </div>
            <div className="flex flex-wrap items-center gap-2 w-full md:w-auto">
              <Tabs
                defaultValue="all"
                value={timeframe}
                onValueChange={setTimeframe}
                className="w-full md:w-auto"
              >
                <TabsList className="grid grid-cols-5 w-full">
                  <TabsTrigger value="all">All Time</TabsTrigger>
                  <TabsTrigger value="day">Day</TabsTrigger>
                  <TabsTrigger value="week">Week</TabsTrigger>
                  <TabsTrigger value="month">Month</TabsTrigger>
                  <TabsTrigger value="year">Year</TabsTrigger>
                </TabsList>
              </Tabs>

              {timeframe === "day" && (
                <Popover open={dayPopoverOpen} onOpenChange={setDayPopoverOpen}>
                  <PopoverTrigger asChild>
                    <Button
                      variant="outline"
                      className="w-full md:w-auto justify-start text-left font-normal"
                    >
                      <CalendarIcon className="mr-2 h-4 w-4" />
                      {date ? format(date, "PPP") : "Pick a date"}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0">
                    <Calendar
                      mode="single"
                      selected={date}
                      onSelect={(selectedDate) => {
                        setDate(selectedDate);
                        setDayPopoverOpen(false);
                      }}
                      initialFocus
                    />
                  </PopoverContent>
                </Popover>
              )}

              {timeframe === "week" && (
                <Popover
                  open={weekPopoverOpen}
                  onOpenChange={setWeekPopoverOpen}
                >
                  <PopoverTrigger asChild>
                    <Button
                      variant="outline"
                      className="w-full md:w-auto justify-start text-left font-normal"
                    >
                      <CalendarIcon className="mr-2 h-4 w-4" />
                      {customDateRange.from && customDateRange.to
                        ? `${format(customDateRange.from, "MMM dd")} - ${format(
                            customDateRange.to,
                            "MMM dd, yyyy"
                          )}`
                        : "Select a week"}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0" align="start">
                    <div className="p-3">
                      <h4 className="mb-2 text-sm font-medium">
                        Select start date
                      </h4>
                      <Calendar
                        initialFocus
                        mode="single"
                        selected={customDateRange.from}
                        onSelect={(selectedDate) => {
                          if (selectedDate) {
                            // Calculate end date (start date + 6 days = 7 day period)
                            const endDate = new Date(selectedDate);
                            endDate.setDate(endDate.getDate() + 6);

                            setCustomDateRange({
                              from: selectedDate,
                              to: endDate,
                            });
                            setWeekPopoverOpen(false);
                          }
                        }}
                      />
                    </div>
                  </PopoverContent>
                </Popover>
              )}

              {timeframe === "month" && (
                <div className="flex flex-wrap gap-2 w-full md:w-auto">
                  <Select
                    value={year.toString()}
                    onValueChange={(value) => setYear(Number.parseInt(value))}
                  >
                    <SelectTrigger className="w-full md:w-[120px]">
                      <SelectValue placeholder="Year" />
                    </SelectTrigger>
                    <SelectContent>
                      {[...Array(6)].map((_, i) => {
                        const yearValue = new Date().getFullYear() - i;
                        return (
                          <SelectItem
                            key={yearValue}
                            value={yearValue.toString()}
                          >
                            {yearValue}
                          </SelectItem>
                        );
                      })}
                    </SelectContent>
                  </Select>

                  <Popover
                    open={monthPopoverOpen}
                    onOpenChange={setMonthPopoverOpen}
                  >
                    <PopoverTrigger asChild>
                      <Button
                        variant="outline"
                        className="w-full md:w-auto justify-start text-left font-normal"
                      >
                        <CalendarIcon className="mr-2 h-4 w-4" />
                        {month ? format(month, "MMMM") : "Select month"}
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0" align="start">
                      <div className="grid grid-cols-3 gap-2 p-3">
                        {Array.from({ length: 12 }).map((_, i) => {
                          const monthDate = new Date(year, i, 1);
                          const monthName = format(monthDate, "MMM");
                          const isSelected =
                            month &&
                            month.getMonth() === i &&
                            month.getFullYear() === year;

                          return (
                            <Button
                              key={i}
                              variant={isSelected ? "default" : "outline"}
                              className={`h-9 ${
                                isSelected
                                  ? "bg-[#245c1a] hover:bg-[#1a4513]"
                                  : ""
                              }`}
                              onClick={() => {
                                setMonth(monthDate);
                                setMonthPopoverOpen(false);
                              }}
                            >
                              {monthName}
                            </Button>
                          );
                        })}
                      </div>
                    </PopoverContent>
                  </Popover>
                </div>
              )}

              {timeframe === "year" && (
                <Select
                  value={year.toString()}
                  onValueChange={(value) => setYear(Number.parseInt(value))}
                >
                  <SelectTrigger className="w-full md:w-[180px]">
                    <SelectValue placeholder="Select year" />
                  </SelectTrigger>
                  <SelectContent>
                    {[...Array(6)].map((_, i) => {
                      const yearValue = new Date().getFullYear() - i;
                      return (
                        <SelectItem
                          key={yearValue}
                          value={yearValue.toString()}
                        >
                          {yearValue}
                        </SelectItem>
                      );
                    })}
                  </SelectContent>
                </Select>
              )}

              {timeframe === "custom" && (
                <Popover
                  open={customPopoverOpen}
                  onOpenChange={setCustomPopoverOpen}
                >
                  <PopoverTrigger asChild>
                    <Button
                      variant="outline"
                      className="w-full md:w-auto justify-start text-left font-normal"
                    >
                      <CalendarIcon className="mr-2 h-4 w-4" />
                      {customDateRange.from ? (
                        customDateRange.to ? (
                          <>
                            {format(customDateRange.from, "LLL dd, y")} -{" "}
                            {format(customDateRange.to, "LLL dd, y")}
                          </>
                        ) : (
                          format(customDateRange.from, "LLL dd, y")
                        )
                      ) : (
                        "Pick a date range"
                      )}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0" align="start">
                    <Calendar
                      initialFocus
                      mode="range"
                      defaultMonth={customDateRange.from}
                      selected={customDateRange}
                      onSelect={(range: any) => {
                        setCustomDateRange(range);
                        if (range.from && range.to) {
                          setCustomPopoverOpen(false);
                        }
                      }}
                      numberOfMonths={2}
                    />
                  </PopoverContent>
                </Popover>
              )}
            </div>
          </div>

          <Card className="mb-6">
            <CardHeader className="pb-2">
              <CardTitle>Profit Summary</CardTitle>
              <CardDescription>
                Total profits sent to your Binance account
                {timeframe !== "all" && " in the selected timeframe"}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
                <div>
                  <p className="text-sm text-gray-500">
                    Total Profit Transfers
                  </p>
                  <p className="text-3xl font-bold text-[#245c1a]">
                    ${totalProfits.toFixed(2)}
                  </p>
                  <p className="text-sm text-gray-500">
                    {filteredProfits.length} transfers
                    {timeframe !== "all" &&
                      ` in ${
                        timeframe === "custom" ? "selected period" : timeframe
                      }`}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-2">
              <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
                <div>
                  <CardTitle>Transfer History</CardTitle>
                  <CardDescription>
                    Profits automatically sent to your Binance account
                  </CardDescription>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="rounded-md border overflow-x-auto">
                <div className="min-w-[700px]">
                  <div className="grid grid-cols-5 gap-4 p-4 border-b bg-gray-50 font-medium text-sm">
                    <div>Date & Time</div>
                    <div>Amount</div>
                    <div>Trading Pair</div>
                    <div>Status</div>
                    <div>Transaction ID</div>
                  </div>
                  {filteredProfits.length > 0 ? (
                    filteredProfits.map((profit) => (
                      <div
                        key={profit.id}
                        className="grid grid-cols-5 gap-4 p-4 border-b last:border-0 text-sm hover:bg-gray-50"
                      >
                        <div>
                          {format(parseISO(profit.date), "MMM dd, yyyy HH:mm")}
                        </div>
                        <div className="font-medium text-[#245c1a]">
                          +${profit.amount.toFixed(2)}
                        </div>
                        <div>{profit.tradingPair}</div>
                        <div>
                          <Badge
                            variant="outline"
                            className="bg-green-50 text-green-700 border-green-200"
                          >
                            {profit.status}
                          </Badge>
                        </div>
                        <div
                          className="font-mono text-xs truncate"
                          title={profit.txId}
                        >
                          {profit.txId}
                        </div>
                      </div>
                    ))
                  ) : (
                    <div className="p-8 text-center text-gray-500">
                      No profit transfers found for the selected timeframe.
                    </div>
                  )}
                </div>
              </div>

              {filteredProfits.length > 0 && (
                <div className="flex justify-between items-center mt-4">
                  <p className="text-sm text-gray-500">
                    Showing {filteredProfits.length} transfers
                  </p>
                  <div className="flex items-center gap-2">
                    <Button variant="outline" size="sm" disabled>
                      Previous
                    </Button>
                    <Button variant="outline" size="sm" disabled>
                      Next
                    </Button>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </Container>
      </main>
      <BottomNav />
    </div>
  );
}
