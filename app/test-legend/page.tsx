"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Container } from "@/components/container"
import { AlertCircle, Check, Plus, Wallet, ArrowLeft } from "lucide-react"
import { cn } from "@/lib/utils"
import { useLegendTradingAccount, useActiveAccount } from "@/lib/hooks/use-legend-trading-account"
import { observer } from "@legendapp/state/react"

const TestLegendPage = observer(function TestLegendPage() {
  const {
    accounts,
    activeAccountId,
    switchAccount,
    getActiveAccount,
    createAccount,
    resumeAccountSetup
  } = useLegendTradingAccount()

  const { activeAccount } = useActiveAccount()
  const [isLoading, setIsLoading] = useState<string | null>(null)

  const handleSwitchAccount = (accountId: string) => {
    setIsLoading(accountId)

    // Simulate a brief loading state
    setTimeout(() => {
      switchAccount(accountId)
      setIsLoading(null)
    }, 500)
  }

  const handleCreateAccount = () => {
    const newAccount = createAccount({
      name: `Test Account ${accounts.length + 1}`,
      exchange: "binance",
      apiKey: "",
      balance: "$0.00",
      tradingPair: "BTC/USDT",
      isActive: false,
      setupComplete: false,
    })
    
    console.log("Created account:", newAccount)
  }

  // Sort accounts: active first, then by creation date (newest first)
  const sortedAccounts = [...accounts].sort((a, b) => {
    if (a.isActive) return -1
    if (b.isActive) return 1
    return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
  })

  return (
    <div className="flex min-h-screen flex-col">
      <main className="flex-1 py-6">
        <Container className="max-w-md">
          <div className="mb-6">
            <h1 className="text-2xl font-bold">Legend State Test Page</h1>
            <p className="text-gray-500">Testing Legend State account switching functionality.</p>
          </div>
          
          <div className="mb-4">
            <h2 className="text-lg font-semibold mb-2">Current State:</h2>
            <div className="bg-gray-100 p-4 rounded-md">
              <p><strong>Total Accounts:</strong> {accounts.length}</p>
              <p><strong>Active Account (from hook):</strong> {activeAccount?.name || "None"}</p>
              <p><strong>Active Account ID (from hook):</strong> {activeAccount?.id || "None"}</p>
              <p><strong>Active Account ID (from store):</strong> {activeAccountId || "None"}</p>
              <p><strong>First Account isActive:</strong> {accounts[0]?.isActive ? "Yes" : "No"}</p>
            </div>
          </div>

          <Card className="mb-4">
            <CardHeader>
              <CardTitle>Actions</CardTitle>
              <CardDescription>Test Legend State functionality</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <Button onClick={handleCreateAccount} className="w-full">
                <Plus className="h-4 w-4 mr-2" />
                Create New Account
              </Button>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Switch Account</CardTitle>
              <CardDescription>Select an account to switch to</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex flex-col gap-2">
                {sortedAccounts.map((account) => (
                  <div key={account.id} className="relative">
                    <button
                      className={cn(
                        "flex items-center justify-between w-full px-4 py-3 rounded-md text-left transition-colors",
                        account.isActive ? "bg-[#e6f0e4] text-[#245c1a]" : "hover:bg-gray-100",
                        isLoading === account.id ? "opacity-70" : "",
                        !account.setupComplete ? "border border-dashed border-amber-300" : "",
                      )}
                      onClick={() => handleSwitchAccount(account.id)}
                      disabled={account.isActive || isLoading !== null}
                    >
                      <div className="flex items-center gap-3">
                        <Wallet className={cn("h-5 w-5", account.isActive ? "text-[#245c1a]" : "text-gray-500")} />
                        <div className="flex flex-col">
                          <span className="font-medium">{account.name}</span>
                          <span className="text-xs text-gray-500">
                            {account.setupComplete
                              ? `${account.exchange} • ${account.tradingPair}`
                              : "Setup incomplete"}
                          </span>
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        {!account.setupComplete && !account.isActive && (
                          <span className="text-xs text-amber-600 font-medium flex items-center">
                            <AlertCircle className="h-3 w-3 mr-1" />
                            Setup
                          </span>
                        )}
                        {account.isActive && (
                          <span className="text-xs text-[#245c1a] font-medium flex items-center">
                            <Check className="h-3 w-3 mr-1" />
                            Active
                          </span>
                        )}
                        {isLoading === account.id && (
                          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-gray-900"></div>
                        )}
                      </div>
                    </button>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </Container>
      </main>
    </div>
  )
})

export default TestLegendPage
