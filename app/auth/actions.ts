"use server";

import { getDBService, getIDBService } from "@/lib/services/db";
import {
  deleteCookie,
  deleteMultipleCookies,
  getCookie,
  getMultipleCookies,
  setCookie,
  setMultipleCookies,
  USER_KEY,
} from "@/lib/utils/cookie";

import { redirect } from "next/navigation";


export async function checkCooldown(
  cookieKey: string,
  cooldownSeconds: number = 60
) {
  const lastRequest = await getCookie(cookieKey);

  if (!lastRequest) {
    return { isInCooldown: false };
  }

  const cooldownMs = cooldownSeconds * 1000;
  const timeSinceLastRequest = Date.now() - parseInt(lastRequest, 10);

  if (timeSinceLastRequest < cooldownMs) {
    const secondsLeft = Math.ceil((cooldownMs - timeSinceLastRequest) / 1000);
    return {
      isInCooldown: true,
      secondsLeft,
      message: `Please wait ${secondsLeft}s before requesting another OTP`,
    };
  }
  return { isInCooldown: false };
}

export async function sendOtp(_email?: string) {
  let email = _email;
  if (!email) {
    email = await getCookie("userEmail");
  }
  if (!email || !email.includes("@")) {
    return {
      success: false,
      error: "Please enter a valid email address",
    };
  }

  try {
    const dbService = await getDBService();
    const result = await dbService.requestOtp(email);

    const cookiesToSet = [
      { key: "userEmail", value: email },
      { key: "otpId", value: result.otpId },
      { key: "otpTimestamp", value: Date.now().toString() },
    ];

    await setMultipleCookies(cookiesToSet);

    return {
      success: true,
      data: {
        otpId: result.otpId,
        email: email,
      },
      message: "OTP sent successfully to your email",
    };
  } catch (error) {
    console.error("Send OTP failed:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to send OTP",
    };
  }
}

export async function verifyOTP(otp: string) {
  const sessionData = await getMultipleCookies(["userEmail", "otpId"]);
  const email = sessionData.userEmail;
  const otpId = sessionData.otpId;

  if (!email || !otpId || !otp) {
    return {
      success: false,
      error: "Email, OTP ID, and OTP code are required",
    };
  }

  try {
    const dbService = await getDBService();
    const result = await dbService.validateOtp({ email, otpId, otp });

    if (result) {
      await setCookie({
        key: USER_KEY,
        value: result.authToken,
        maxAge: result.expiringTime,
      });

      // Clear the temporary cookies only on successful verification
      await deleteMultipleCookies(["userEmail", "otpId", "otpTimestamp"]);
      return {
        success: true,
        data: result,
        message: "Email verified successfully",
      };
    } else {
      return {
        success: false,
        error: "Email verification failed - email mismatch",
      };
    }
  } catch (error) {
    console.error("OTP verification failed:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to verify OTP",
    };
  }
}

export async function getLoggedInUser() {
  try {
    const token = await getCookie(USER_KEY);

    if (!token) {
      return null;
    }

    const dbService = await getIDBService();
    const user = await dbService.getUserFromToken(token);
    return user;
  } catch (error) {
    console.error("Error fetching logged-in user:", error);
    //  await deleteCookie(USER_KEY);
    return null;
  }
}

export async function logOut() {
  await deleteCookie(USER_KEY);
}

export async function loginOrSignup(email: string, shouldCreate?: boolean) {
  // create a userRecord
  const dbService = await getDBService();
  //get if the user exist
  const user = await dbService.getUserByEmail(email, shouldCreate);

  if (!user) {
    return {
      error: "Please check your email or create an account.",
      success: false,
    };
  }

  // send a verification code to the user
  const result = await sendOtp(email);

  // return the result
  return result;
}

export async function signupOrLogin(formData: {
  email: string;
  name?: string;
}) {
  const { email, name } = formData;
  const dbService = await getDBService();

  // Check if user already exists
  let user = await dbService.getUserByEmail(email);

  // Determine if this is signup or login based on presence of `name`
  const isSignup = !!name;

  if (isSignup) {
    // Signup flow
    if (user) {
      return {
        error: "This email is already registered. Please log in instead.",
        success: false,
      };
    }

    // Create new user
    user = await dbService.createUserRecord({ email, name });
  } else {
    // Login flow
    if (!user) {
      return {
        error: "Please check your email or create an account.",
        success: false,
      };
    }
  }

  // Send OTP for verification
  const result = await sendOtp(email.toLowerCase());
  return result;
}

export async function fetchUser(id: string) {
  try {
    const dbService = await getDBService();
    const user = await dbService.getUserById(id);

    if (!user) {
      return {
        success: false,
        error: "User not found",
      };
    }
    return {
      success: true,
      user,
    };
  } catch (err: any) {
    return {
      success: false,
      error: err.message || "Failed to fetch user",
    };
  }
}

export async function removeAdmin(id: string) {
  const dbService = await getDBService();
  await dbService.updateUserRecord({
    userId: id,
    updates: { admin: "" },
  });
}
