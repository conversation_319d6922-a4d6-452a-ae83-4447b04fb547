"use client";

import React from "react";

import { useAuth } from "../hooks/useAuth";
import { AuthLayout } from "./AuthLayout";
import { LoginSignupForm } from "./LoginSignupForm";
import VerifyForm from "./VerifyForm";

interface AuthFormProps {
  mode: "login" | "signup";
}

const AuthForm: React.FC<AuthFormProps> = ({ mode }) => {
  const isLogin = mode === "login";
  const isSignup = mode === "signup";
  const {
    showVerification,
    formData,
    handleResendCode,
    handleBackToEmail,
    cooldown,
    handleFormSubmit,
    reset,
  } = useAuth();

  const titleHeader = showVerification
    ? "  Check Your Email"
    : isLogin
    ? "Welcome Back"
    : "Create Account";
  const description = showVerification ? (
    <>
      We've sent the verification code to
      <br />
      <span className="font-medium text-gray-900">{formData.email}</span>
    </>
  ) : isLogin ? (
    "Enter your email to receive a verification code"
  ) : (
    "Enter your details to get started with TradeSmart"
  );
  return (
    <AuthLayout
      title={titleHeader}
      description={description}
      displayHeader={isSignup}
    >
      {showVerification ? (
        <VerifyForm
          key={reset.toString()}
          cooldown={cooldown}
          handleBackToEmail={handleBackToEmail}
          handleResendCode={handleResendCode}
        />
      ) : (
        <LoginSignupForm
          mode={mode}
          onSubmitAction={handleFormSubmit}
          timer={cooldown}
        />
      )}
    </AuthLayout>
  );
};

export default AuthForm;
