"use client";

import React, { useState } from "react";

import { Alert, AlertDescription } from "@/components/ui/alert";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Separator } from "@/components/ui/separator";
import { useToast } from "@/hooks/use-toast";
import { validateEmail, validateName } from "@/lib/utils/formValidation";
import { AlertCircle, CheckCircle, Loader2, Mail, User } from "lucide-react";
import { useRouter } from "next/navigation";

import Link from "next/link";
import { useCountdown } from "../hooks/use-countdown";
import { signupOrLogin } from "../actions";

interface FormErrors {
  name?: string;
  email?: string;
  general?: string;
}

interface LoginSignupFormProps {
  mode: "login" | "signup";
  onSubmitAction: (
    formData: {
      email: string;
      name?: string;
    },
    timer?: number
  ) => void;
  timer?: number;
}

export interface FormDataProps {
  name?: string;
  email: string;
}

export const LoginSignupForm: React.FC<LoginSignupFormProps> = ({
  mode,
  onSubmitAction,
  timer,
}) => {
  const router = useRouter();
  const { toast } = useToast();
  const isLogin = mode === "login";
  const isSignup = mode === "signup";

  const { onTrigger, cooldown: internalCooldown } = useCountdown({
    timer,
  });

  // Common state
  const [isLoading, setIsLoading] = useState(false);
  const [errors, setErrors] = useState<FormErrors>({});

  // Unified form data for both login and signup
  const [formData, setFormData] = useState<FormDataProps>({
    name: "",
    email: "",
  });

  // Unified touched state for all fields
  const [touched, setTouched] = useState<{
    name?: boolean;
    email?: boolean;
  }>({});

  // Signup handlers
  const validateSignupForm = (): boolean => {
    const newErrors: FormErrors = {};

    if (formData.name?.trim()) {
      const nameError = validateName(formData.name);
      if (nameError) newErrors.name = nameError;
    }
    const emailError = validateEmail(formData.email);
    if (emailError) newErrors.email = emailError;

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleOAuthSignup = async (provider: "google" | "apple") => {
    setIsLoading(true);
    setErrors({});

    try {
      // Simulate OAuth process
      await new Promise((resolve) => setTimeout(resolve, 1000));

      // Store OAuth signup data for verification step
      const oauthData = {
        name: provider === "google" ? "Google User" : "Apple User",
        email: `user@${provider}.com`,
        isOAuth: true,
        timestamp: Date.now(),
      };
      localStorage.setItem("pendingSignup", JSON.stringify(oauthData));

      toast({
        title: "Account created!",
        description: `We've sent a verification code to your ${provider} email`,
      });

      // Redirect to verification
      router.push("/auth/verify");
    } catch (error) {
      setErrors({ general: `${provider} signup failed. Please try again.` });
    } finally {
      setIsLoading(false);
    }
  };

  // validation map
  const validators: Record<string, (val: string) => string | undefined> = {
    name: validateName,
    email: validateEmail,
  };

  // unified input change
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));

    // clear error when user types
    if (errors[name as keyof FormErrors]) {
      setErrors((prev) => ({ ...prev, [name]: undefined }));
    }

    // real-time validation if already touched
    if (touched[name as keyof typeof touched]) {
      const validator = validators[name];
      if (validator) {
        const err = validator(value);
        setErrors((prev) => ({ ...prev, [name]: err }));
      }
    }
  };

  const handleBlur = (field: keyof typeof formData) => {
    setTouched((prev) => ({ ...prev, [field]: true }));

    const validator = validators[field];
    if (validator) {
      const value = formData[field] ?? "";
      const err = validator(value);
      setErrors((prev) => ({ ...prev, [field]: err }));
    }
  };

  const onFormSubmit = async (
    callback: (countdown?: number) => Promise<void>
  ) => {
    setTouched({ name: true, email: true });
    if (!validateSignupForm()) {
      setErrors((prev) => ({
        ...prev,
        general: "Please fix the errors above before continuing",
      }));
      return;
    }

    setIsLoading(true);
    setErrors({});

    try {
      const result = await signupOrLogin(formData);
      if (result.success) {
        setIsLoading(false);
        setErrors({});
        if (isLogin) {
          await callback(60);
        } else {
          await callback(60);
        }
      } else {
        throw new Error(result.error);
      }
    } catch (error: any) {
      setErrors({
        general: error?.message || "Something went wrong. Please try again.",
      });
      setIsLoading(false);
    }
  };

  const handleFormSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    await onFormSubmit(async (countdown?: number) => {
      onSubmitAction(formData, countdown);
      toast({
        title: "Code sent",
        description: "A verification code has been sent to your email",
      });
    });
  };

  const isSubmitDisabled = isLoading || internalCooldown > 0;

  const promptText = isLogin
    ? "Don't have an account? "
    : "Already have an account? ";
  const linkHref = isLogin
    ? "/auth/signup"
    : "/auth/login";
  const authSwitchText = isLogin ? "Sign up here" : "Sign in here";

  return (
    <>
      {/* general error */}
      {errors.general && (
        <Alert className="mb-4 border-red-200 bg-red-50 dark:border-red-700 dark:bg-red-900">
          <AlertCircle className="h-4 w-4 text-red-600 dark:text-red-400" />
          <AlertDescription className="text-red-800 dark:text-red-200">
            {errors.general}
          </AlertDescription>
        </Alert>
      )}

      <form onSubmit={handleFormSubmit} className="space-y-4">
        {isLogin ? (
          <>
            {/* LOGIN FORM */}
            <InputFormField
              label="email"
              value={formData.email}
              onChange={handleInputChange}
              onBlur={() => handleBlur("email")}
              error={errors.email}
              isValid={touched.email && !errors.email}
            // disabled={isLoading}
            />
            <div>
              {internalCooldown > 0 && (
                <p className="text-sm text-red-500">
                  Please wait {internalCooldown} seconds before sending another
                  code
                </p>
              )}
              <Button
                type="submit"
                className="w-full h-12 bg-[#245c1a] hover:bg-[#1a4513] text-white font-medium"
                disabled={isSubmitDisabled}
              >
                {isLoading ? (
                  <>
                    <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                    Sending Code...
                  </>
                ) : (
                  "Send Verification Code"
                )}
              </Button>
            </div>
          </>
        ) : (
          <>
            {(["name", "email"] as const).map((field) => (
              <InputFormField
                key={field}
                label={field}
                value={formData[field] ?? ""}
                onChange={handleInputChange}
                onBlur={() => handleBlur(field)}
                error={errors[field]}
                isValid={touched[field] && !errors[field]}
              // disabled={isLoading || internalCooldown > 0}
              />
            ))}
            <>
              {internalCooldown > 0 && (
                <p className="text-sm text-red-500">
                  Please wait {internalCooldown} seconds before signing up
                  again.
                </p>
              )}
              <Button
                type="submit"
                className="w-full h-12 bg-[#245c1a] hover:bg-[#1a4513] text-white font-medium"
                disabled={isSubmitDisabled}
              >
                {isLoading ? (
                  <>
                    <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                    Creating Account...
                  </>
                ) : (
                  "Create Account"
                )}
              </Button>
            </>
          </>
        )}
      </form>

      {/* signup only → OAuth buttons */}
      {isSignup && (
        <div className="mt-6">
          <Separator className="my-4" />
          <div className="grid grid-cols-2 gap-4">
            <Button
              variant="outline"
              onClick={() => handleOAuthSignup("google")}
              disabled={isLoading}
              className="w-full"
            >
              {/* google icon */}
              <svg className="w-4 h-4 mr-2" viewBox="0 0 24 24">
                <path
                  fill="#4285F4"
                  d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"
                />
                <path
                  fill="#34A853"
                  d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"
                />
                <path
                  fill="#FBBC05"
                  d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"
                />
                <path
                  fill="#EA4335"
                  d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"
                />
              </svg>
              Google
            </Button>
            <Button
              variant="outline"
              onClick={() => handleOAuthSignup("apple")}
              disabled={isLoading}
              className="w-full"
            >
              {/* apple icon */}
              <svg
                className="w-4 h-4 mr-2"
                fill="currentColor"
                viewBox="0 0 24 24"
              >
                <path d="M18.71 19.5c-.83 1.24-1.71 2.45-3.05 2.47-1.34.03-1.77-.79-3.29-.79-1.53 0-2 .77-3.27.82-1.31.05-2.3-1.32-3.14-2.53C4.25 17 2.94 12.45 4.7 9.39c.87-1.52 2.43-2.48 4.12-2.51 1.28-.02 2.5.87 3.29.87.78 0 2.26-1.07 3.81-.91.65.03 2.47.26 3.64 1.98-.09.06-2.17 1.28-2.15 3.81.03 3.02 2.65 4.03 2.68 4.04-.03.07-.42 1.44-1.38 2.83M13 3.5c.73-.83 1.94-1.46 2.94-1.5.13 1.17-.34 2.35-1.04 3.19-.69.85-1.83 1.51-2.95 1.42-.15-1.15.41-2.35 1.05-3.11z" />
              </svg>
              Apple
            </Button>
          </div>
        </div>
      )}
      <div className="mt-6 text-center text-sm">
        <span className="text-gray-600 dark:text-gray-400">{promptText}</span>
        <Link
          href={linkHref}
          className="font-medium text-[#245c1a] hover:text-[#1a4513] dark:text-green-400 dark:hover:text-green-300"
        >
          {authSwitchText}
        </Link>
      </div>
      <p className="mt-4 text-xs text-center text-gray-500 dark:text-gray-400">
        By creating an account, you agree to our Terms of Service and Privacy
        Policy
      </p>
    </>
  );
};

type InputFormFieldProps = {
  label: "name" | "email";
  value: string;
  onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  onBlur: () => void;
  error?: string;
  isValid?: boolean;
  disabled?: boolean;
};

const InputFormField = ({
  label,
  value,
  onChange,
  onBlur,
  error,
  isValid,
  disabled,
}: InputFormFieldProps) => {
  return (
    <div className="space-y-2">
      <label htmlFor={label} className="text-sm font-medium text-gray-700">
        {label === "name" ? "Full Name" : "Email Address"}
      </label>
      <div className="relative">
        {label === "name" ? (
          <User className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
        ) : (
          <Mail className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
        )}
        <Input
          id={label}
          name={label}
          type={label === "email" ? "email" : "text"}
          placeholder={
            label === "email"
              ? "Enter your email address"
              : "Enter your full name"
          }
          value={value}
          onChange={onChange}
          onBlur={onBlur}
          className={`pl-10 pr-10 ${error
            ? "border-red-500 focus:ring-red-500 focus:border-red-500"
            : isValid
              ? "border-green-500 focus:ring-green-500 focus:border-green-500"
              : ""
            }`}
          required
          disabled={disabled}
        />
        {isValid && (
          <CheckCircle className="absolute right-3 top-3 h-4 w-4 text-green-500" />
        )}
      </div>
      {error && (
        <p className="text-sm text-red-600 flex items-center gap-1">
          <AlertCircle className="w-3 h-3" />
          {error}
        </p>
      )}
    </div>
  );
};
