"use client";

import type React from "react";

import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { useUser } from "@/contexts/user-provider";
import { useToast } from "@/hooks/use-toast";
import { <PERSON>ert<PERSON>ircle, ArrowLeft, CheckCircle, Loader2 } from "lucide-react";
import { useState } from "react";
import { verifyOTP } from "../actions";


interface FormErrors {
  email?: string;
  code?: string;
  general?: string;
}

type VerifyFormProps = {
  cooldown: number;
  handleBackToEmail: () => void;
  handleResendCode: () => void;
};

export default function VerifyForm({
  cooldown,
  handleBackToEmail,
  handleResendCode,
}: VerifyFormProps) {
  const [verificationCode, setVerificationCode] = useState("");

  const [isVerifying, setIsVerifying] = useState(false);
  const [errors, setErrors] = useState<FormErrors>({});
  const [touched, setTouched] = useState<{ email: boolean; code: boolean }>({
    email: false,
    code: false,
  });
  const { toast } = useToast();
  const { authenticateUser } = useUser();

  const OTP_LENGTH = 8;
  const isDisabled =
    isVerifying || verificationCode.length !== OTP_LENGTH || !!errors.code;

  const shouldVerify = cooldown === 0;

  const handleVerifyCode = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsVerifying(true);

    if (verificationCode.length !== OTP_LENGTH) {
      toast({
        title: "Invalid code",
        description: `Please enter a valid ${OTP_LENGTH}-digit verification code`,
        variant: "destructive",
      });
      return;
    }

    setErrors({}); // Clear previous errors

    try {
      // Verify the OTP
      const result = await verifyOTP(verificationCode);

      if (result.success) {
        toast({
          title: "Success!",
          description: "Email verified successfully",
          variant: "default",
        });
        authenticateUser()
        // Redirect to onboarding on success
        window.location.href = "/dashboard";
      } else {
        setErrors((prev) => ({
          ...prev,
          code: "Invalid verification code. Please try again.",
        }));
        toast({
          title: "Verification failed",
          description: result.message || "Invalid verification code",
          variant: "destructive",
        });
        setIsVerifying(false);
      }
    } catch (error) {
      setErrors((prev) => ({
        ...prev,
        general: "Verification failed. Please try again.",
      }));
      console.error("Verification error:", error);
      toast({
        title: "Verification Error",
        description: "Something went wrong. Please try again.",
        variant: "destructive",
      });
      setIsVerifying(false);
    }
  };

  const handleCodeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value.replace(/\D/g, "").slice(0, OTP_LENGTH);
    setVerificationCode(value);

    // Clear code error when user starts typing
    if (errors.code) {
      setErrors((prev) => ({ ...prev, code: undefined }));
    }
  };

  const handleCodeBlur = () => {
    setTouched((prev) => ({ ...prev, code: true }));
  };

  return (
    <>
      <form onSubmit={handleVerifyCode} className="space-y-4">
        <div className="space-y-2">
          <label htmlFor="code" className="text-sm font-medium text-gray-700">
            Verification Code
          </label>
          <Input
            id="code"
            type="text"
            placeholder={`Enter ${OTP_LENGTH}-digit code`}
            value={verificationCode}
            onChange={handleCodeChange}
            onBlur={handleCodeBlur}
            className={`h-12 text-center font-mono tracking-widest text-xl sm:text-2xl placeholder:text-base sm:placeholder:text-xl ${errors.code && touched.code
                ? "border-red-500 focus:ring-red-500 focus:border-red-500"
                : touched.code &&
                  !errors.code &&
                  verificationCode.length === OTP_LENGTH
                  ? "border-green-500 focus:ring-green-500 focus:border-green-500"
                  : ""
              }`}
            maxLength={OTP_LENGTH}
            required
            disabled={isVerifying}
          />

          {errors.code && touched.code && (
            <p className="text-sm text-red-600 flex items-center gap-1">
              <AlertCircle className="w-3 h-3" />
              {errors.code}
            </p>
          )}
          <div className="flex justify-between text-xs text-gray-500">
            <span>{`Enter the ${OTP_LENGTH}-digit code from your email`}</span>
            <span
              className={
                verificationCode.length === OTP_LENGTH ? "text-green-600" : ""
              }
            >
              {verificationCode.length}/{OTP_LENGTH}
            </span>
          </div>
        </div>

        <Button
          type="submit"
          className="w-full h-12 bg-[#245c1a] hover:bg-[#1a4513] text-white font-medium"
          disabled={isDisabled && !shouldVerify}
        >
          {isVerifying ? (
            <>
              <Loader2 className="w-4 h-4 mr-2 animate-spin" />
              Verifying...
            </>
          ) : (
            <>
              <CheckCircle className="w-4 h-4 mr-2" />
              Verify & Continue
            </>
          )}
        </Button>
      </form>
      <div className="text-center space-y-4">
        <p className="text-sm text-gray-600">
          Didn't receive the code?{" "}
          <button
            type="button"
            onClick={handleResendCode}
            disabled={cooldown > 0}
            className="text-[#245c1a] hover:underline font-medium disabled:opacity-50"
          >
            {cooldown > 0 ? `Resend in ${cooldown}s` : "Resend code"}
          </button>
        </p>

        <div className="flex items-center justify-center text-sm gap-x-1">
          <ArrowLeft className="h-4 w-4" />
          <button
            onClick={handleBackToEmail}
            className="text-[#245c1a] hover:underline"
          >
            Back to login
          </button>
        </div>
      </div>
    </>
  );
}
