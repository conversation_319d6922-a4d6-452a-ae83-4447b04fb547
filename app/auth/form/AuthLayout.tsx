import React from "react";

import { Container } from "@/components/container";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { ArrowLeft, Mail } from "lucide-react";
import Link from "next/link";

interface AuthFormProps {
  children: React.ReactNode;
  title: string;
  description: React.ReactNode;
  displayHeader?: boolean;
  className?: string;
}

export const AuthLayout: React.FC<AuthFormProps> = ({
  children,
  title,
  description,
  displayHeader,
}) => {
  const className = displayHeader
    ? "py-12 px-4 sm:px-6 lg:px-8"
    : "min-h-screen bg-gradient-to-br from-gray-50 via-white to-gray-50 dark:from-gray-950 dark:via-gray-900 dark:to-gray-950 p-4";

  const header = displayHeader ? (
    <div className="text-center mb-6">
      <Link
        href="/"
        className="inline-flex items-center text-sm text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
      >
        <ArrowLeft className="w-4 h-4 mr-2" />
        Back to Home
      </Link>
    </div>
  ) : undefined;

  return (
    <div className={`flex items-center justify-center ${className}`}>
      <Container className="max-w-md">
        {header}
        <Card className="border-0 shadow-xl bg-white dark:bg-gray-900">
          <CardHeader className="text-center space-y-4 pb-8">
            {header ? null : (
              <div className="w-16 h-16 bg-gradient-to-br from-[#245c1a] to-[#1a4513] rounded-2xl flex items-center justify-center mx-auto">
                <Mail className="w-8 h-8 text-white" />
              </div>
            )}
            <div>
              <CardTitle className="text-2xl font-bold text-gray-900 dark:text-white">
                {title}
              </CardTitle>
              <CardDescription className="text-gray-600 dark:text-gray-300 mt-2">
                {description}
              </CardDescription>
            </div>
          </CardHeader>

          <CardContent>{children}</CardContent>
        </Card>
      </Container>
    </div>
  );
};
