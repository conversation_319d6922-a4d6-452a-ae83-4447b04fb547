"use client";

import { useToast } from "@/hooks/use-toast";
import { useEffect, useState } from "react";
import { checkCooldown } from "../actions";

const COUNTDOWN_TIMER = 60;
export function useCountdown(params?: { timer?: number; cookieName?: string }) {
  const { timer = 0, cookieName = "otpTimestamp" } = params || {};
  const { toast } = useToast();

  const [cooldown, setCooldown] = useState<any>(timer);

  useEffect(() => {
    if (cooldown <= 0) return;

    const timer = setInterval(() => {
      setCooldown((prev: number) => (prev > 0 ? prev - 1 : 0));
    }, 1000);

    return () => clearInterval(timer);
  }, [cooldown]);

  const onTrigger = async (callback: (countdown?: number) => Promise<void>) => {
    if (cooldown > 0) {
      toast({
        title: "Resend blocked",
        description: `Please wait ${cooldown}s before requesting another OTP`,
        variant: "destructive",
      });

      return;
    }

    const cooldownCheck = await checkCooldown(cookieName, COUNTDOWN_TIMER);
    if (cooldownCheck.isInCooldown) {
      setCooldown(cooldownCheck?.secondsLeft);

      toast({
        title: "Resend blocked",
        description: cooldownCheck.message,
        variant: "destructive",
      });
      return;
    }
    await callback(COUNTDOWN_TIMER);
    setCooldown(COUNTDOWN_TIMER);
  };

  return {
    cooldown,
    onTrigger,
    setCooldown,
  };
}
