"use client";

import { useToast } from "@/hooks/use-toast";
import { useState } from "react";
import { sendOtp } from "../actions";
import { useCountdown } from "./use-countdown";

export function useLogin() {
  const { toast } = useToast();
  const { cooldown, onTrigger, setCooldown } = useCountdown();

  // Login state
  const [email, setEmail] = useState("");
  const [reset, setReset] = useState(false);

  // Verification state
  const [showVerification, setShowVerification] = useState(false);

  const handleResendCode = async () => {
    setReset(false);
    const result = await sendOtp();

    if (result.success) {
      setReset(true);

      toast({
        title: "Code resent",
        description: "A new verification code has been sent to your email",
      });
    } else {
    }
  };

  const handleBackToEmail = () => {
    setShowVerification(false);
    setReset(true);
  };

  const handleEmailSubmit = (_email: string, timer?: number) => {
    if (timer) {
      setCooldown(timer);
    }
    setShowVerification(true);
    setEmail(_email);
  };

  return {
    cooldown,
    reset,
    showVerification,
    email,
    handleResendCode: async () => await onTrigger(handleResendCode),
    handleBackToEmail,
    handleEmailSubmit,
  };
}
