"use client";

import { useToast } from "@/hooks/use-toast";
import { useState } from "react";
import { sendOtp } from "../actions";
import { useCountdown } from "../hooks/use-countdown";
import { FormDataProps } from "../form/LoginSignupForm";

export function useAuth() {
  const { toast } = useToast();
  const { cooldown, onTrigger, setCooldown } = useCountdown();


  // Login state
  const [formData, setFormData] = useState<FormDataProps>({
    name: "",
    email: "",
  });

  const [reset, setReset] = useState(false);

  // Verification state
  const [showVerification, setShowVerification] = useState(false);

  const handleResendCode = async () => {
    setReset(false);
    const result = await sendOtp();

    if (result.success) {
      setReset(true);

      toast({
        title: "Code resent",
        description: "A new verification code has been sent to your email",
      });
    } else {
    }
  };

  const handleBackToEmail = () => {
    setShowVerification(false);
    setReset(true);
  };

  const handleFormSubmit = (
    formData: {
      email: string;
      name?: string;
    },
    timer?: number
  ) => {
    if (timer) {
      setCooldown(timer);
    }
    setShowVerification(true);
    setFormData(formData);
  };

  return {
    cooldown,
    reset,
    showVerification,
    formData,
    handleResendCode: async () => await onTrigger(handleResendCode),
    handleBackToEmail,
    handleFormSubmit,
  };
}
