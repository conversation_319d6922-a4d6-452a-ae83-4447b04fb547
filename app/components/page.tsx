"use client";

import { AccountSwitcher } from "@/components/account-switcher";
import { BottomNav } from "@/components/bottom-nav";
import { Container } from "@/components/container";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { useToast } from "@/components/ui/use-toast";
import { useAuth } from "@/contexts/auth-context";
import {
  ArrowDown,
  ArrowUp,
  BarChart2,
  DollarSign,
  Percent,
  RefreshCw,
} from "lucide-react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import StatCard from "./statsCard";
import PerformanceCard from "./performanceCard";
import ActiveTradeCard from "./acitveTradeCard";
import ConfigCard from "./configCard";
import SubscriptionDetails from "./subscriptionDetails";
import TableCard from "./tableCard";

export default function DashboardPage() {
  const router = useRouter();
  const { user, isAuthenticated, getActiveAccount } = useAuth();
  const [btcPrice, setBtcPrice] = useState(65432.21);
  const [priceChange, setPriceChange] = useState(2.34);
  const [isPositive, setIsPositive] = useState(true);
  const [activeTab, setActiveTab] = useState("overview");
  const [isLoading, setIsLoading] = useState(false);
  const { toast } = useToast();

  const activeAccount = {
    id: "account-1",
    name: "Main BTC Account",
    exchange: "Binance",
    apiKey: "xxxx-xxxx-xxxx-xxxx",
    balance: "$10,245.67",
    tradingPair: "BTC/USDT",
    isActive: true,
    setupComplete: true,
    createdAt: "2025-08-20T14:15:22.123Z", // ISO timestamp
  };

  // Simulate price updates
  useEffect(() => {
    const interval = setInterval(() => {
      const change = (Math.random() * 100 - 50) / 10;
      const newPrice = btcPrice + change;
      setBtcPrice(Number.parseFloat(newPrice.toFixed(2)));

      const newPriceChange = Number.parseFloat(
        (priceChange + change / 100).toFixed(2)
      );
      setPriceChange(Math.abs(newPriceChange));
      setIsPositive(newPriceChange > 0);
    }, 5000);

    return () => clearInterval(interval);
  }, [btcPrice, priceChange]);

  const refreshData = () => {
    setIsLoading(true);
    setTimeout(() => {
      setIsLoading(false);
    }, 1000);
  };

  const tradingHistory = [
    {
      id: 1,
      type: "Buy",
      amount: "0.05 BTC",
      price: "$64,230.45",
      date: "2 hours ago",
      profit: null,
    },
    {
      id: 2,
      type: "Sell",
      amount: "0.03 BTC",
      price: "$65,120.78",
      date: "5 hours ago",
      profit: "+$267.89",
    },
    {
      id: 3,
      type: "Buy",
      amount: "0.02 BTC",
      price: "$63,890.12",
      date: "1 day ago",
      profit: null,
    },
    {
      id: 4,
      type: "Sell",
      amount: "0.04 BTC",
      price: "$64,567.34",
      date: "2 days ago",
      profit: "+$312.45",
    },
  ];

  const botSettings = {
    riskLevel: "Medium",
    takeProfit: "3.5%",
    stopLoss: "2.5%",
    maxTradeSize: "10% of available funds",
    tradingPair: activeAccount.tradingPair,
  };

  const accountStats = {
    totalBalance: activeAccount.balance,
    availableBalance: "$5,123.45",
    inTrade: "$5,122.22",
    monthlyVolume: "$15,678.90",
    volumeLimit: "$25,000.00",
    plan: "Enthusiast",
  };

  return (
    <div className="flex min-h-screen flex-col">
      <main className="flex-1 py-6 pb-20 md:pb-6">
        <Container className="max-w-6xl">
          <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6 gap-4">
            <div>
              <h1 className="text-2xl font-bold">Trading Dashboard</h1>
              <p className="text-gray-500">
                Manage your {activeAccount.tradingPair} trading bot
              </p>
            </div>
            <div className="flex flex-col sm:flex-row gap-2 w-full md:w-auto">
              <AccountSwitcher />
              <Button
                variant="outline"
                className="flex items-center gap-2 bg-transparent"
                onClick={refreshData}
                disabled={isLoading}
              >
                <RefreshCw
                  className={`h-4 w-4 ${isLoading ? "animate-spin" : ""}`}
                />
                Refresh Data
              </Button>
            </div>
          </div>

          <div className="grid gap-6 md:grid-cols-3 mb-6">
            <StatCard
              title={`${activeAccount.tradingPair} Price`}
              value={`$${btcPrice.toLocaleString()}`}
              subtitle="Last updated just now"
              isChange
              changeValue={priceChange}
              isPositive={isPositive}
            />

            <StatCard
              title="Account Balance"
              value={accountStats.totalBalance}
              extraContent={
                <div className="grid grid-cols-2 gap-2">
                  <div>
                    <p className="text-xs text-gray-500">Available</p>
                    <p className="text-sm font-medium">
                      {accountStats.availableBalance}
                    </p>
                  </div>
                  <div>
                    <p className="text-xs text-gray-500">In Trade</p>
                    <p className="text-sm font-medium">
                      {accountStats.inTrade}
                    </p>
                  </div>
                </div>
              }
            />

            <StatCard
              title="Monthly Volume"
              value={accountStats.monthlyVolume}
              progress={{
                current: Number.parseInt(
                  accountStats.monthlyVolume.replace(/[^0-9.-]+/g, "")
                ),
                total: Number.parseInt(
                  accountStats.volumeLimit.replace(/[^0-9.-]+/g, "")
                ),
                label: accountStats.volumeLimit,
              }}
            />
          </div>

          <Tabs
            defaultValue="overview"
            value={activeTab}
            onValueChange={setActiveTab}
            className="mb-6"
          >
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="overview">Overview</TabsTrigger>
              <TabsTrigger value="history">History</TabsTrigger>
              <TabsTrigger value="settings">Settings</TabsTrigger>
            </TabsList>
            <TabsContent value="overview" className="mt-6">
              <div className="grid gap-6 md:grid-cols-2">
                <PerformanceCard
                  title="Trading Performance"
                  description="Your bot's performance over time"
                  stats={[
                    { label: "Total Trades", value: "24" },
                    { label: "Win Rate", value: "68%" },
                    {
                      label: "Profit/Loss",
                      value: "+$876.45",
                      valueClassName: "text-green-500",
                    },
                    { label: "Avg. Trade Duration", value: "4.2 hours" },
                  ]}
                />

                <ActiveTradeCard
                  title="Active Trades"
                  description="Currently open positions"
                  trades={[
                    {
                      pair: activeAccount.tradingPair,
                      type: "Buy",
                      openedAgo: "3 hours ago",
                      amount: "0.05 BTC",
                      value: "$3,271.61",
                      entryPrice: "$65,432.21",
                      takeProfit: "$67,722.34",
                      stopLoss: "$63,796.41",
                      pnl: "+$124.32 (1.9%)",
                      pnlClassName: "text-green-500",
                      progress: 65,
                    },
                    {
                      pair: activeAccount.tradingPair,
                      type: "Sell",
                      openedAgo: "1 hour ago",
                      amount: "0.03 BTC",
                      value: "$1,850.61",
                      entryPrice: "$64,890.45",
                      takeProfit: "$62,768.19",
                      stopLoss: "$66,512.71",
                      pnl: "-$16.28 (-0.3%)",
                      pnlClassName: "text-red-500",
                      progress: 35,
                    },
                  ]}
                />
              </div>
            </TabsContent>
            <TabsContent value="history" className="mt-6">
              <TableCard
                title="Trading History"
                description="Recent trades executed by your bot"
                columns={[
                  {
                    label: "Type",
                    key: "type",
                    type: "badge",
                    badgeColors: {
                      Buy: "bg-green-100 text-green-800",
                      Sell: "bg-red-100 text-red-800",
                    },
                  },
                  { label: "Amount", key: "amount" },
                  { label: "Price", key: "price", type: "currency" },
                  { label: "Date", key: "date" },
                  {
                    label: "Profit/Loss",
                    key: "profit",
                    formtatter: (value: string) => (value ? `+${value}` : "-"),
                  },
                ]}
                data={tradingHistory}
                action={{
                  label: "View All Trades",
                  onClick: () => console.log("Go to all trades page"),
                }}
              />
            </TabsContent>
            <TabsContent value="settings" className="mt-6">
              <ConfigCard
                title="Bot Configuration"
                description="Current settings for your trading bot"
                items={[
                  {
                    icon: Percent,
                    label: "Risk Level",
                    value: botSettings.riskLevel,
                  },
                  {
                    icon: ArrowUp,
                    label: "Take Profit",
                    value: botSettings.takeProfit,
                  },
                  {
                    icon: ArrowDown,
                    label: "Stop Loss",
                    value: botSettings.stopLoss,
                  },
                  {
                    icon: DollarSign,
                    label: "Max Trade Size",
                    value: botSettings.maxTradeSize,
                  },
                  {
                    icon: BarChart2,
                    label: "Trading Pair",
                    value: botSettings.tradingPair,
                    hint: `Your subscription is currently limited to trading ${activeAccount.tradingPair} only.`,
                  },
                ]}
                columns={2}
                action={{ label: "Update Settings", href: "/settings" }}
              />
            </TabsContent>
          </Tabs>

          <Card>
            <CardHeader>
              <CardTitle>Subscription Plan</CardTitle>
              <CardDescription>Your current plan and usage</CardDescription>
            </CardHeader>

            <CardContent>
              <div className="flex flex-col md:flex-row gap-6">
                <div className="flex-1">
                  <SubscriptionDetails
                    plan={accountStats.plan}
                    monthlyFee="$500"
                    renewalDate="June 15, 2025"
                    monthlyVolume={accountStats.monthlyVolume}
                    volumeLimit={accountStats.volumeLimit}
                  />
                </div>

                <div className="md:w-[300px]">
                  <div className="rounded-lg border p-4 bg-gray-50">
                    <h3 className="font-medium mb-2">
                      Need More Trading Volume?
                    </h3>
                    <p className="text-sm text-gray-500 mb-4">
                      Upgrade to our Trader plan to increase your monthly
                      trading volume limit to $100,000.
                    </p>
                    <Link href="/pricing">
                      <Button className="w-full bg-[#245c1a] hover:bg-[#1a4513]">
                        Upgrade Plan
                      </Button>
                    </Link>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </Container>
      </main>
      <BottomNav />
    </div>
  );
}
