"use client";

import {
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>le,
  CardDescription,
  CardContent,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { BarChart3, Settings, Plus } from "lucide-react";


type AccountsCardProps = {
  title?: string;
  description?: string;
  accounts: any[];
  onViewDetails?: (id: string) => void;
  onSettingsClick?: (id: string) => void;
  onAddAccount?: () => void;
};

export function AccountsCard({
  title = "Trading Accounts",
  description = "Manage your active trading accounts",
  accounts,
  onViewDetails,
  onSettingsClick,
  onAddAccount,
}: AccountsCardProps) {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <BarChart3 className="w-5 h-5" />
          {title}
        </CardTitle>
        {description && <CardDescription>{description}</CardDescription>}
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {accounts.map((account) => (
            <div key={account.id} className="border rounded-lg p-4 space-y-3">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <div className="w-10 h-10 bg-orange-100 rounded-full flex items-center justify-center">
                    <span className="text-orange-600 font-semibold text-sm">
                      {account?.exchange?.charAt(0).toUpperCase()}
                    </span>
                  </div>
                  <div>
                    <h3 className="font-semibold">{account?.slug}</h3>
                    <p className="text-sm text-muted-foreground capitalize">
                      • ${account?.currentValue?.toFixed(2)}
                    </p>
                  </div>
                </div>
                <div className="text-right">
                  <Badge
                    variant={
                      account.status === "active" ? "default" : "secondary"
                    }
                  >
                    {account.status}
                  </Badge>
                </div>
              </div>
              <div className="flex gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => onViewDetails?.(account.id)}
                >
                  View Details
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => onSettingsClick?.(account.id)}
                >
                  <Settings className="w-4 h-4" />
                </Button>
              </div>
            </div>
          ))}
          <Button variant="outline" onClick={onAddAccount} className="w-full">
            <Plus className="w-4 h-4 mr-2" />
            Add New Account
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}
