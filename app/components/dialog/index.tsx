"use client";

import { Download, UserPlus, Wallet, Edit } from "lucide-react";
import { AppDialog } from "./AppDialog";
import { AppDrawer } from "./AppDrawer";
import { useControlledDialog } from "./hook";

type TitleIcons = Record<string, React.ElementType>;

const titleIcons: TitleIcons = {
  download: Download,
  user: UserPlus,
  wallet: Wallet,
  edit: Edit,
};

export type AppDialogRef = {
  open: () => void;
  close: () => void;
  isOpen: boolean;
  isLoading: boolean;
};

export type AppDrawerRef = {
  open: () => void;
  close: () => void;
  isLoading: boolean;
};

export type ApplicationDialogProps = {
  ref?: React.Ref<AppDialogRef | AppDrawerRef>;
  title: string;
  icon?: keyof TitleIcons;
  description: string;
  children: React.ReactNode;
  maxWidth?: "sm" | "md" | "lg" | "xl";
  onSubmit?: () => Promise<void>;
  submitButtonProps?: {
    content: string;
  };
  componentType?: "dialog" | "drawer";
  headerButton?: React.ReactNode;
  isDisabled?: boolean;
  canClose?: boolean;
  closeDialog?: () => void;
};

export const ApplicationDialog = (props: ApplicationDialogProps) => {
  const { isLoading, isOpen, setIsOpen, onHandleSubmit, disabled } =
    useControlledDialog(props);

  const Icon = titleIcons[props.icon || ""];
  const icon = Icon ? <Icon className="h-5 w-5 text-[#245c1a]" /> : null;
  const localProps = {
    isOpen,
    setIsOpen,
    onHandleSubmit,
    isLoading,
    disabled,
    icon,
    children: props.children,
    title: props.title,
    description: props.description,
    buttonContent: props.submitButtonProps?.content,
    maxWidth: props.maxWidth,
    canClose: props.canClose,
    closeDialog: props.closeDialog,
  };

  return (
    <>
      {props.componentType === "dialog" ? (
        <AppDialog {...localProps} />
      ) : (
        <AppDrawer headerButton={props.headerButton} {...localProps} />
      )}
    </>
  );
};
