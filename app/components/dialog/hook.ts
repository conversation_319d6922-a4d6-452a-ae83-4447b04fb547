"use client";

import { useImperativeHandle, useState } from "react";
import { AppDialogRef } from "./AppDialog";

export type UseDialogType = {
  ref?: React.Ref<AppDialogRef>;
  onSubmit?: () => Promise<void>;
  isDisabled?: boolean;
};

export const useControlledDialog = ({
  ref,
  onSubmit,
  isDisabled,
}: UseDialogType) => {
  const [isLoading, setIsLoading] = useState(false);
  const [isOpen, setIsOpen] = useState(false);

  useImperativeHandle(ref, () => ({
    open: () => setIsOpen(true),
    close: () => setIsOpen(false),
    isOpen,
    isLoading,
  }));

  const disabled = isLoading || Boolean(isDisabled);

  const onHandleSubmit = async () => {
    setIsLoading(true);
    try {
      if (onSubmit) {
        await onSubmit();
      }
    } catch (error) {
      console.error(error);
    } finally {
      setIsLoading(false);
    }
  };

  return { isLoading, isOpen, setIsOpen, onHandleSubmit, disabled };
};
