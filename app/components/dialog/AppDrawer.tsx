"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Drawer,
  DrawerClose,
  DrawerContent,
  DrawerDes<PERSON>,
  DrawerFooter,
  DrawerHeader,
  DrawerTitle,
} from "@/components/ui/drawer";
import { Loader2 } from "lucide-react";

export type AppDrawerProps = {
  isOpen: boolean;
  setIsOpen: (open: boolean) => void;
  isLoading: boolean;
  onHandleSubmit?: () => void;
  icon?: React.ReactNode;
  disabled: boolean;
  children: React.ReactNode;
  title: string;
  description: string;
  buttonContent?: string;
  headerButton?: React.ReactNode;
};

export const AppDrawer = ({
  isOpen,
  setIsOpen,
  isLoading,
  onHandleSubmit,
  icon,
  disabled,
  children,
  title,
  description,
  buttonContent,
  headerButton,
}: AppDrawerProps) => {
  return (
    <Drawer open={isOpen} onOpenChange={setIsOpen}>
      <DrawerContent className="h-[90vh]">
        <div className="mx-auto w-full max-w-2xl h-full flex flex-col">
          <DrawerHeader className="flex-shrink-0">
            <DrawerTitle className="flex items-center gap-2 text-lg">
              {headerButton}
              {icon}
              {title}
            </DrawerTitle>
            <DrawerDescription className="text-sm">
              {description}
            </DrawerDescription>
          </DrawerHeader>

          <div className="flex-1 overflow-y-auto px-4">{children}</div>

          <DrawerFooter className="flex-shrink-0">
            <div className="flex flex-col md:flex-row gap-2">
              <DrawerClose asChild>
                <Button
                  variant="outline"
                  disabled={isLoading}
                  className="w-full h-11 bg-transparent"
                >
                  Cancel
                </Button>
              </DrawerClose>
              {onHandleSubmit && (
                <Button
                  onClick={onHandleSubmit}
                  className="bg-[#245c1a] hover:bg-[#1a4513] w-full h-11"
                  disabled={disabled}
                >
                  {isLoading ? (
                    <>
                      <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                      Submitting...
                    </>
                  ) : (
                    buttonContent
                  )}
                </Button>
              )}
            </div>
          </DrawerFooter>
        </div>
      </DrawerContent>
    </Drawer>
  );
};
