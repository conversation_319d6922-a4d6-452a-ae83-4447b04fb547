"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Loader2 } from "lucide-react";

export type AppDialogRef = {
  open: () => void;
  close: () => void;
  isLoading: boolean;
};

export type AppDialogProps = {
  isOpen: boolean;
  setIsOpen: (open: boolean) => void;
  isLoading: boolean;
  onHandleSubmit?: () => void;
  icon?: React.ReactNode;
  disabled: boolean;
  children: React.ReactNode;
  title: string;
  description: string;
  buttonContent?: string;
  maxWidth?: "sm" | "md" | "lg" | "xl";
  canClose?: boolean;
  closeDialog?: () => void;
};

export const AppDialog = ({
  isOpen,
  setIsOpen,
  isLoading,
  onHandleSubmit,
  icon,
  disabled,
  children,
  title,
  description,
  buttonContent,
  maxWidth,
  canClose = true,
  closeDialog,
}: AppDialogProps) => {
  const getWidth = () => {
    if (maxWidth) {
      const widthMap = {
        sm: "max-w-[400px]",
        md: "max-w-[500px]",
        lg: "max-w-[600px]",
        xl: "max-w-[700px]",
      };
      return widthMap[maxWidth];
    }
    return "max-w-[600px]";
  };

  const handleClose = () => {
    if (!canClose) return;
     setIsOpen(false);
     if (closeDialog) closeDialog();
  };

  return (
    <Dialog
      open={isOpen}
      onOpenChange={(open) => {
        setIsOpen(open);
        if (!open && closeDialog) closeDialog();
      }}
    >
      <DialogContent
        className={`${getWidth()} mx-4 max-h-[90vh] overflow-y-auto`}
        canClose={canClose}
        // Prevent closing on outside click
        onInteractOutside={(event) => {
          if (!canClose) {
            event.preventDefault();
          }
        }}
        // Prevent closing on ESC key
        onEscapeKeyDown={(event) => {
          if (!canClose) {
            event.preventDefault();
          }
        }}
      >
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2 text-lg md:text-xl">
            {icon}
            {title}
          </DialogTitle>
          <DialogDescription className="text-sm md:text-base">
            {description}
          </DialogDescription>
        </DialogHeader>
        {children}
        <DialogFooter className="flex flex-col md:flex-row gap-2">
          <Button
            variant="outline"
            disabled={isLoading}
            className="w-full md:w-auto"
            onClick={handleClose}
          >
            Cancel
          </Button>
          {buttonContent && (
            <Button
              onClick={onHandleSubmit}
              className="bg-[#245c1a] hover:bg-[#1a4513] w-full md:w-auto"
              disabled={disabled}
            >
              {isLoading ? (
                <>
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                  Submitting...
                </>
              ) : (
                buttonContent
              )}
            </Button>
          )}
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};
