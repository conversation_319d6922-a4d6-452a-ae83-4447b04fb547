"use client";

import { useAdmin } from "@/contexts/admin-provider";
import { useToast } from "@/hooks/use-toast";
import { useRef, useState } from "react";
import {
  WithdrawalForm,
  WithdrawalFormRefProps,
} from "./investments/InvestmentCard/WithdrawalForm";
import { ApplicationDialog } from "./dialog";
import { AppDialogRef } from "./dialog";
import { useUser } from "@/contexts/user-provider";

type WalletFormDialogType = {
  dialogRef: React.RefObject<AppDialogRef>;
};

export const WalletFormDialog = ({ dialogRef }: WalletFormDialogType) => {
  const { wallet, setWallet, onCloseWalletDialog } = useAdmin();
  const { wallet: defaultWallet } = useUser();
  const [isDisabled, setIsDisabled] = useState(true);
  const withdrawalFormRef = useRef<WithdrawalFormRefProps | null>(null);
  const { toast } = useToast();
  const canClose = Boolean(wallet.address && wallet.network);

  const handleSaveWalletAddress = async () => {
    const { onSubmit } = withdrawalFormRef.current!;
    const result = await onSubmit();
    if (result) {
      toast({
        title: "Wallet Saved",
        description:
          "Your wallet address has been verified and saved successfully.",
      });
      setWallet(result);
      onCloseWalletDialog(true);
      if (defaultWallet?.blacklisted) {
        window.location.reload();
      }
    }
  };

  return (
    <>
      <ApplicationDialog
        ref={dialogRef}
        componentType="dialog"
        title="Edit Wallet Address"
        description="Edit your wallet address"
        icon="edit"
        onSubmit={handleSaveWalletAddress}
        submitButtonProps={{ content: "Save" }}
        isDisabled={isDisabled}
        maxWidth="md"
        canClose={canClose}
        closeDialog={onCloseWalletDialog}
      >
        <WithdrawalForm
          ref={withdrawalFormRef}
          onChange={(valid) => setIsDisabled(!valid)}
          wallet={wallet}
        />
      </ApplicationDialog>
    </>
  );
};
