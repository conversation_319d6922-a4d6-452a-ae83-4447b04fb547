// components/StatCard.tsx
"use client";

import { <PERSON>, <PERSON><PERSON><PERSON>er, Card<PERSON><PERSON>le, CardContent } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { ArrowUp, ArrowDown } from "lucide-react";

type StatCardProps = {
  title: string;
  value: string | number;
  subtitle?: string;
  isChange?: boolean;
  changeValue?: number;
  isPositive?: boolean;
  extraContent?: React.ReactNode;
  progress?: {
    current: number;
    total: number;
    label: string;
  };
};

export default function StatCard({
  title,
  value,
  subtitle,
  isChange,
  changeValue,
  isPositive,
  extraContent,
  progress,
}: StatCardProps) {
  return (
    <Card>
      <CardHeader className="pb-2">
        <CardTitle className="text-sm font-medium text-gray-500">
          {title}
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="flex items-baseline">
          <span className="text-2xl font-bold">{value}</span>
          {isChange && changeValue !== undefined && (
            <span
              className={`ml-2 text-sm font-medium ${
                isPositive ? "text-green-500" : "text-red-500"
              }`}
            >
              {isPositive ? (
                <ArrowUp className="inline h-3 w-3 mr-1" />
              ) : (
                <ArrowDown className="inline h-3 w-3 mr-1" />
              )}
              {changeValue}%
            </span>
          )}
        </div>
        {subtitle && (
          <p className="text-xs text-gray-500 mt-1">{subtitle}</p>
        )}

        {extraContent && <div className="mt-2">{extraContent}</div>}

        {progress && (
          <div className="mt-2">
            <div className="flex justify-between text-xs mb-1">
              <span>Used</span>
              <span>
                {Math.round((progress.current / progress.total) * 100)}% of{" "}
                {progress.label}
              </span>
            </div>
            <Progress
              value={(progress.current / progress.total) * 100}
              className="h-2"
            />
          </div>
        )}
      </CardContent>
    </Card>
  );
}
