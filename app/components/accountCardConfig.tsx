// AccountCardConfig.ts
import { Settings } from "lucide-react"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"

export const accountCardConfig = {
  avatar: (exchange: string) => ({
    className: "w-10 h-10 bg-orange-100 rounded-full flex items-center justify-center",
    label: exchange.charAt(0).toUpperCase(),
    labelClass: "text-orange-600 font-semibold text-sm",
  }),

  status: (status: string) => ({
    element: (
      <Badge variant={status === "active" ? "default" : "secondary"}>
        {status}
      </Badge>
    ),
  }),

  actions: (
    accountId: string,
    setSelectedAccountId: (id: string) => void,
    setViewMode: (mode: string) => void
  ) => [
    <Button
      key="view"
      variant="outline"
      size="sm"
      onClick={() => {
        setSelectedAccountId(accountId)
        setViewMode("account")
      }}
    >
      View Details
    </Button>,
    <Button key="settings" variant="outline" size="sm">
      <Settings className="w-4 h-4" />
    </Button>,
  ],
}
