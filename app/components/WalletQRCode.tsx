"use client";
import React, { useRef } from "react";
import { QRCodeCanvas, QRCodeSVG } from "qrcode.react";

type Props = {
  address: string;
  size?: number;
};

export default function WalletQRCode({ address, size = 220 }: Props) {
  const canvasRef = useRef<HTMLCanvasElement | null>(null);

  const downloadPng = () => {
    if (!canvasRef.current) return;
    const url = canvasRef.current.toDataURL("image/png");
    const a = document.createElement("a");
    a.href = url;
    a.download = `${address.slice(0, 8)}.png`;
    a.click();
  };

  return (
    <div className="flex items-center justify-center">
      <div className="p-4 bg-white dark:bg-gray-800 rounded-lg border text-center">
        <div className="relative inline-block">
          {/* SVG for display */}
          <QRCodeSVG
            value={address.trim()}
            size={size}
            level="H"
            bgColor="#ffffff"
            fgColor="#000000"
          />

          {/*Hidden Canvas (for download only) */}
          <QRCodeCanvas
            value={address.trim()}
            size={size}
            level="H"
            bgColor="#ffffff"
            fgColor="#000000"
            ref={canvasRef}
            style={{ display: "none" }}
          />
        </div>

        <p className="text-xs mt-2 text-gray-500 dark:text-gray-300"></p>

        <button
          onClick={downloadPng}
          className="mt-3 px-3 py-1 text-xs rounded-md border hover:bg-gray-50 dark:hover:bg-gray-700"
        >
          Download PNG
        </button>
      </div>
    </div>
  );
}
