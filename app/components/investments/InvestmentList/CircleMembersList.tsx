"use client";

import { Alert, AlertDescription } from "@/components/ui/alert";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useMobile } from "@/hooks/use-mobile";
import { useToast } from "@/hooks/use-toast";
import { validateEmail, validateName } from "@/lib/utils/formValidation";
import {
  AlertCircle,
  CheckCircle,
  Loader2,
  Trash2,
  User,
  UserPlus,
  Users,
} from "lucide-react";
import { useImperativeHandle, useRef, useState } from "react";
import { ApplicationDialog } from "../../dialog";
import { useAdmin } from "@/contexts/admin-provider";
import { useInvestment } from "@/contexts/investment-context";

type AppDrawerRef = {
  open: () => void;
  close: () => void;
  isLoading: boolean;
};

export default function CircleMembersList() {
  const { circleMembers, addCircleMember } = useAdmin();
  const { removeOrDeleteMember } = useInvestment();
  const { toast } = useToast();
  const addCircelMemberDrawerRef = useRef<AppDrawerRef>(null);
  const addCircleMemberFormRef = useRef<AddCircleMemberFormRefProps>(null);
  const [isDisabled, setIsDisabled] = useState(true);
  const isMobile = useMobile();
  const [isLoading, setIsLoading] = useState<string | null>(null);

  const handleAddMember = async () => {
    const { member } = addCircleMemberFormRef.current!;
    const result = member(); // { name, email }

    if (!result) {
      setIsDisabled(true);
      return;
    }
    try {
      await addCircleMember(result);
      addCircelMemberDrawerRef.current?.close();
    } catch (error) {
      console.error(error);
      toast({
        title: "Error",
        description: "Something went wrong while adding the member.",
        variant: "destructive",
      });
    }
  };

  const handleRemoveMember = async (id: string) => {
    setIsLoading(id);
    try {
      await removeOrDeleteMember(id);
    } catch (error: any) {
      if (error instanceof Error) {
        toast({
          title: "Error",
          description: error.message,
          variant: "destructive",
        });
        return;
      }
      toast({
        title: error.title,
        description: error.description,
        variant: "destructive",
      });
    } finally {
      setIsLoading(null);
    }
  };

  const isTrulyEmpty = circleMembers.length === 0;

  return (
    <>
      <div className="py-4">
        <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-3 mb-4">
          <h4 className="font-medium text-base md:text-lg">
            Circle Members ({circleMembers.length})
          </h4>
          <Button
            onClick={() => addCircelMemberDrawerRef.current?.open()}
            size="sm"
            className="bg-[#245c1a] hover:bg-[#1a4513] h-10 w-full md:w-auto"
          >
            <UserPlus className="w-4 h-4 mr-2" />
            Add Member
          </Button>
        </div>
        <div className="space-y-3 max-h-60 overflow-y-auto">
          {isTrulyEmpty ? (
            <div className="text-center py-8 text-gray-500">
              <Users className="w-12 h-12 mx-auto mb-2 opacity-50" />
              <p className="text-sm md:text-base">
                No members in your circle yet.
              </p>
              <p className="text-xs md:text-sm">
                Add people to start creating investments for them.
              </p>
            </div>
          ) : (
            circleMembers.map((member) => (
              <div
                key={member.id}
                className="flex items-center justify-between p-3 border rounded-lg"
              >
                <div className="flex items-center gap-3 min-w-0 flex-1">
                  <div className="w-10 h-10 bg-[#245c1a]/10 rounded-full flex items-center justify-center flex-shrink-0">
                    <User className="w-5 h-5 text-[#245c1a]" />
                  </div>
                  <div className="min-w-0 flex-1">
                    <p className="font-medium text-sm md:text-base truncate">
                      {member.name}
                    </p>
                    <p className="text-xs md:text-sm text-gray-500 truncate">
                      {member.email}
                    </p>
                  </div>
                </div>
                <Button
                  onClick={() => handleRemoveMember(member.id)}
                  variant="ghost"
                  size="sm"
                  disabled={isLoading === member.id}
                  className="text-red-600 hover:text-red-700 hover:bg-red-50 flex-shrink-0 h-9 w-9 p-0"
                >
                  {isLoading === member.id ? (
                    <Loader2 className="w-4 h-4 animate-spin" />
                  ) : (
                    <Trash2 className="w-4 h-4" />
                  )}
                </Button>
              </div>
            ))
          )}
        </div>
      </div>
      <ApplicationDialog
        componentType={isMobile ? "drawer" : "dialog"}
        title="Add Circle Member"
        description="Add someone to your trading circle to create investments for them."
        icon="user"
        onSubmit={handleAddMember}
        submitButtonProps={{ content: "Add Member" }}
        ref={addCircelMemberDrawerRef}
        isDisabled={isDisabled}
        maxWidth="sm"
      >
        <AddCircleMemberForm
          ref={addCircleMemberFormRef}
          onChange={(valid) => {
            setIsDisabled(!valid);
          }}
        />
      </ApplicationDialog>
    </>
  );
}

export interface FormErrors {
  name?: string;
  email?: string;
  investmentName?: string;
  amount?: string;
  beneficiaryRate?: string;
  walletAddress?: string;
  verificationCode?: string;
  cardNumber?: string;
  expiryDate?: string;
  cvv?: string;
  cardName?: string;
  general?: string;
}

export interface CircleMember {
  id: string;
  name: string;
  email: string;
}

export type CircleMemberForm = {
  name: string;
  email: string;
};

export type AddCircleMemberFormRefProps = {
  member: () => undefined | { name: string; email: string };
  reset: () => void;
};

export type AddCircleMemberFormProps = {
  isLoading?: boolean;
  onChange?: (valid: boolean) => void;
  ref?: React.Ref<AddCircleMemberFormRefProps>;
};

export const AddCircleMemberForm = ({
  ref,
  onChange,
}: AddCircleMemberFormProps) => {
  const [touched, setTouched] = useState<{ name?: boolean; email?: boolean }>(
    {}
  );
  const [errors, setErrors] = useState<FormErrors>({});
  const [newMember, setNewMember] = useState<CircleMemberForm>({
    name: "",
    email: "",
  });
  const [isLoading, setIsLoading] = useState(false);

  const isValid = (newErrors: FormErrors, _newMember: CircleMemberForm) => {
    return (
      Object.keys(newErrors).length === 0 &&
      validateName(_newMember.name) === undefined &&
      validateEmail(_newMember.email) === undefined
    );
  };

  const reset = () => {
    setNewMember({ name: "", email: "" });
    setErrors({});
  };

  useImperativeHandle(ref, () => ({
    member: () => {
      if (isValid(errors, newMember)) {
        return newMember;
      }
      return undefined;
    },
    reset,
  }));

  const handleMemberInputChange = (field: "name" | "email", value: string) => {
    const _newMember = { ...newMember, [field]: value };
    setNewMember(_newMember);

    // Clear errors when user starts typing
    const errorKey = field;
    if (errors[errorKey as keyof FormErrors]) {
      setErrors((prev) => ({ ...prev, [errorKey]: undefined }));
    }

    // Real-time validation for touched fields
    const newErrors = { ...errors };
    if (touched[errorKey]) {
      if (field === "name") {
        const nameError = validateName(value);
        if (nameError) {
          newErrors[errorKey as keyof FormErrors] = nameError;
        } else {
          delete newErrors[errorKey as keyof FormErrors];
        }
      } else if (field === "email") {
        const emailError = validateEmail(value);
        if (emailError) {
          newErrors[errorKey as keyof FormErrors] = emailError;
        } else {
          delete newErrors[errorKey as keyof FormErrors];
        }
      }
      setErrors(newErrors);
    }
    if (onChange) {
      onChange(isValid(newErrors, _newMember));
    }
  };

  const handleBlur = (field: string) => {
    setTouched((prev) => ({ ...prev, [field]: true }));

    // Validate field on blur
    const newErrors = { ...errors };

    if (field === "name") {
      const value = newMember.name;
      const nameError = validateName(value);
      if (nameError) {
        newErrors[field as keyof FormErrors] = nameError;
      } else {
        delete newErrors[field as keyof FormErrors];
      }
    } else if (field === "email") {
      const value = newMember.email;
      const emailError = validateEmail(value);
      if (emailError) {
        newErrors[field as keyof FormErrors] = emailError;
      } else {
        delete newErrors[field as keyof FormErrors];
      }
    }

    setErrors(newErrors);
  };

  return (
    <div className="flex-1 overflow-y-auto px-4 pb-4">
      <div className="grid gap-4 py-4">
        {errors.general && (
          <Alert className="border-red-200 bg-red-50">
            <AlertCircle className="h-4 w-4 text-red-600" />
            <AlertDescription className="text-red-800 text-sm">
              {errors.general}
            </AlertDescription>
          </Alert>
        )}

        <div className="space-y-2">
          <Label htmlFor="member-name" className="text-sm md:text-base ">
            Full Name
          </Label>
          <div className="relative">
            <Input
              id="member-name"
              placeholder="Enter their full name"
              value={newMember.name}
              onChange={(e) => handleMemberInputChange("name", e.target.value)}
              onBlur={() => handleBlur("name")}
              className={`pr-10 h-11 md:h-10 ${
                errors.name && touched.name
                  ? "border-red-500 focus:ring-red-500 focus:border-red-500"
                  : touched.name && !errors.name && newMember.name.trim()
                  ? "border-green-500 focus:ring-green-500 focus:border-green-500"
                  : ""
              }`}
              disabled={isLoading}
            />
            {touched.name && !errors.name && newMember.name.trim() && (
              <CheckCircle className="absolute right-3 top-3 h-4 w-4 text-green-500" />
            )}
          </div>
          {errors.name && touched.name && (
            <p className="text-sm text-red-600 flex items-center gap-1">
              <AlertCircle className="w-3 h-3" />
              {errors.name}
            </p>
          )}
        </div>

        <div className="space-y-2">
          <Label htmlFor="member-email" className="text-sm md:text-base">
            Email Address
          </Label>
          <div className="relative">
            <Input
              id="member-email"
              type="email"
              placeholder="Enter their email address"
              value={newMember.email}
              onChange={(e) => handleMemberInputChange("email", e.target.value)}
              onBlur={() => handleBlur("email")}
              className={`pr-10 h-11 md:h-10 ${
                errors.email && touched.email
                  ? "border-red-500 focus:ring-red-500 focus:border-red-500"
                  : touched.email && !errors.email && newMember.email.trim()
                  ? "border-green-500 focus:ring-green-500 focus:border-green-500"
                  : ""
              }`}
              disabled={isLoading}
            />
            {touched.email && !errors.email && newMember.email.trim() && (
              <CheckCircle className="absolute right-3 top-3 h-4 w-4 text-green-500" />
            )}
          </div>
          {errors.email && touched.email && (
            <p className="text-sm text-red-600 flex items-center gap-1">
              <AlertCircle className="w-3 h-3" />
              {errors.email}
            </p>
          )}
        </div>
      </div>
    </div>
  );
};
