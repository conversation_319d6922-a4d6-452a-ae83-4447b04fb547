"use client";


import { Card, CardContent } from "@/components/ui/card";
import { Activity, DollarSign, TrendingUp, Users } from "lucide-react";

import { Investment, InvestmentStatus } from "@/app/data/investmentModel";
import { useInvestmentAdmin } from "@/contexts/investment-context";
import { ExtendedInvestmentStatusType } from ".";

type InvestmentSummaryProps = {
  investments: Investment[];
  tabStatus: ExtendedInvestmentStatusType;
  isAdmin?: boolean;
};

export const InvestmentSummary = ({
  investments,
  tabStatus,
  isAdmin = false,
}: InvestmentSummaryProps) => {


  if (isAdmin) {
    return <AdminInvestmentSummary {...{ investments, tabStatus, isAdmin }} />;
  }

  return <UserInvestmentSummary {...{ investments, tabStatus }} />;
};


const AdminInvestmentSummary = ({
  investments,
  tabStatus,
}: InvestmentSummaryProps) => {
  const { getInitialAmount, circleMembers } = useInvestmentAdmin();
  const noOfUsers = circleMembers.length;

  return (
    <BaseSummary
      investments={investments}
      tabStatus={tabStatus}
      getInitialAmount={getInitialAmount}
      noOfUsers={noOfUsers}
      isAdmin
    />
  );
};


const UserInvestmentSummary = ({
  investments,
  tabStatus,
}: InvestmentSummaryProps) => {
  const getInitialAmount = (inv: Investment) => {
    return { initialAmount: inv.currentAmount || 0 };
  };

  // user has no circleMembers
  const noOfUsers = 0;

  return (
    <BaseSummary
      investments={investments}
      tabStatus={tabStatus}
      getInitialAmount={getInitialAmount}
      noOfUsers={noOfUsers}
    />
  );
};


const BaseSummary = ({
  investments,
  tabStatus,
  getInitialAmount,
  noOfUsers,
  isAdmin=false,
}: {
  investments: Investment[];
  tabStatus: ExtendedInvestmentStatusType;
  getInitialAmount: (inv: Investment) => { initialAmount: number };
  noOfUsers: number;
  isAdmin?: boolean;
}) => {
  const formatCurrency = (amount: number) =>
    new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
    }).format(amount);

  let filteredInvestments: Investment[] = [];

  if (tabStatus === InvestmentStatus.ACTIVE) {
    filteredInvestments = investments.filter(
      (inv) => inv.status === InvestmentStatus.ACTIVE
    );
  } else if (tabStatus === InvestmentStatus.MATURED) {
    filteredInvestments = investments.filter(
      (inv) => inv.status === InvestmentStatus.MATURED
    );
  } else if (tabStatus === InvestmentStatus.CLOSED) {
    filteredInvestments = investments.filter(
      (inv) =>
        inv.status === InvestmentStatus.CLOSED ||
        inv.status === InvestmentStatus.MATURED
    );
  }

  const totalInvested = filteredInvestments.reduce((sum, inv) => {
    const { initialAmount } = getInitialAmount(inv);
    return sum + initialAmount;
  }, 0);

  const totalCurrentValue = filteredInvestments.reduce(
    (sum, inv) => sum + (inv.currentAmount || 0),
    0
  );

  const totalForClosed =
    tabStatus === InvestmentStatus.CLOSED ? totalCurrentValue : totalInvested;

  const totalProfit = totalCurrentValue - totalInvested;

  const activeInvestments = investments.filter(
    (inv) => inv.status === InvestmentStatus.ACTIVE
  ).length;
  const maturedInvestments = investments.filter(
    (inv) => inv.status === InvestmentStatus.MATURED
  ).length;

  const summaryCards = [
    {
      value: formatCurrency(
        tabStatus === InvestmentStatus.CLOSED ? totalForClosed : totalInvested
      ),
      title:
        tabStatus === InvestmentStatus.CLOSED
          ? "Total Value"
          : "Total Invested",
      description:
        tabStatus === InvestmentStatus.CLOSED
          ? "Matured & closed"
          : "Active only",
      Icon: DollarSign,
      iconClass: "text-blue-600 dark:text-blue-400",
      bgClass: "bg-blue-100 dark:bg-blue-900",
    },
    {
      value: formatCurrency(totalProfit),
      title: "Profit",
      description: `${formatCurrency(totalInvested)} invested`,
      Icon: TrendingUp,
      iconClass: "text-green-600 dark:text-green-400",
      bgClass: "bg-green-100 dark:bg-green-900",
      trend: true,
    },
    {
      value: activeInvestments,
      title: "Active Investments",
      description: `${maturedInvestments} ready for withdrawal`,
      Icon: Activity,
      iconClass: "text-[#245c1a] dark:text-green-400",
      bgClass: "bg-[#245c1a]/10 dark:bg-green-600/20",
    },

    ...(isAdmin
      ? [
        {
          value: noOfUsers,
          title: "Circle Members",
          description: "People in your circle",
          Icon: Users,
          iconClass: "text-purple-600 dark:text-purple-400",
          bgClass: "bg-purple-100 dark:bg-purple-900",
        },
      ]
      : []),
  ];

  return (
    <div
      className={`grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 ${isAdmin ? "lg:grid-cols-4" : "lg:grid-cols-3"
        } gap-4 md:gap-6 mb-8`}
    >
      {summaryCards.map(
        ({ title, value, description, Icon, iconClass, bgClass, trend }, idx) => (
          <Card
            key={idx}
            className="transition-all duration-300 hover:shadow-md dark:hover:shadow-gray-800 border border-gray-100 dark:border-gray-800 rounded-2xl"
          >
            <CardContent className="p-5 md:p-6">
              <div className="flex items-start justify-between">
                {/* Text Section */}
                <div className="flex-1 min-w-0">
                  <p className="text-xs md:text-sm font-medium text-gray-500 dark:text-gray-400 truncate">
                    {title}
                  </p>
                  <p
                    className={`text-lg md:text-2xl font-semibold mt-1 ${trend
                      ? "text-green-600 dark:text-green-400"
                      : "text-gray-900 dark:text-white"
                      } truncate`}
                  >
                    {value}
                  </p>
                </div>

                {/* Icon Section */}
                <div
                  className={`p-2.5 md:p-3 rounded-xl flex-shrink-0 ml-3 ${bgClass}`}
                >
                  <Icon className={`w-4 h-4 md:w-6 md:h-6 ${iconClass}`} />
                </div>
              </div>

              {/* Description */}
              {description && (
                <div className="mt-2">
                  <span className="text-xs md:text-sm text-gray-500 dark:text-gray-400">
                    {description}
                  </span>
                </div>
              )}
            </CardContent>
          </Card>
        )
      )}
    </div>
  );
};
