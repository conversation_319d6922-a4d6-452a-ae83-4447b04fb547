"use client";

import { Card, CardContent } from "@/components/ui/card";
import { Target } from "lucide-react";

type TotalInvestmentSummaryProps = {
  summary: {
    startingValue: number;
    currentValue: number;
    daysElapsed: number;
    totalDays: number;
    totalProfit: number;
    startDate: string;
  };
};

export default function TotalInvestmentSummary({
  summary,
}: TotalInvestmentSummaryProps) {
  const {
    totalDays,
    daysElapsed,
    startDate,
    startingValue,
    currentValue,
    totalProfit,
  } = summary;
  const daysRemaining = totalDays - daysElapsed;
  const progressPercentage = (daysElapsed / totalDays) * 100;

  const initialValue = startingValue
    ?.toFixed(2)
    .replace(/\B(?=(\d{3})+(?!\d))/g, ",");
  const targetValue = currentValue
    ?.toFixed(2)
    .replace(/\B(?=(\d{3})+(?!\d))/g, ",");

  return (
    <Card className="bg-gradient-to-r from-[#245c1a] to-[#1a4513] text-white overflow-hidden relative">
      {/* Background Accent */}
      <div className="absolute top-0 right-0 w-32 h-32 bg-white/10 rounded-full -translate-y-16 translate-x-16"></div>

      <CardContent className="p-6 relative z-10">
        {/* Header */}
        <div className="flex justify-between items-center mb-4">
          <div>
            <h2 className="text-2xl font-bold flex items-center gap-2">
              <Target className="w-6 h-6" />
              {"Investment Progress"}
            </h2>
            <p className="opacity-90">
              Day {daysElapsed} of {totalDays} • {daysRemaining} days remaining
            </p>
          </div>
          <div className="text-right">
            <div className="text-3xl font-bold">${initialValue}</div>
            <div className="text-sm opacity-90">Target: ${targetValue}</div>
          </div>
        </div>

        {/* Progress Bar */}
        <div className="w-full bg-white/20 rounded-full h-4 mb-3 overflow-hidden">
          <div
            className="bg-gradient-to-r from-white to-green-200 rounded-full h-4 transition-all duration-500 ease-out relative"
            style={{ width: `${progressPercentage}%` }}
          >
            <div className="absolute inset-0 bg-white/30 animate-pulse rounded-full"></div>
          </div>
        </div>

        {/* Stats */}
        <div className="grid grid-cols-3 gap-4 text-sm">
          {[
            { label: "Total Profit", value: `$${totalProfit.toFixed(2)}` },
            { label: "Progress", value: `${progressPercentage.toFixed(1)}%` },
            {
              label: "Start Date",
              value: startDate,
            },
          ].map((item, index) => (
            <div key={index} className="text-center">
              <div className="font-bold">{item.value}</div>
              <div className="opacity-75">{item.label}</div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
}
