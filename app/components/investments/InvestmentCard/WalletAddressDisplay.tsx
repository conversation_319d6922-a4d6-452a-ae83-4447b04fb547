import { Alert, AlertDescription } from "@/components/ui/alert";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { WalletType } from "@/contexts/admin-provider";
import { AlertCircle } from "lucide-react";
import WalletQRCode from "../../WalletQRCode";

type WalletAddressDisplayProps = {
  wallet: WalletType;
  onClick: () => void;
  buttonText: React.ReactNode;
  alertMessage: React.ReactNode;
};

export function WalletAddressDisplay({
  wallet,
  onClick,
  buttonText,
  alertMessage,
}: WalletAddressDisplayProps) {
  return (
    <div className="grid gap-4 md:gap-6 py-2 md:py-4">
      <div className="space-y-4">
        <div className="p-4 border rounded-lg bg-white dark:bg-gray-800">
          <div className="flex items-center justify-between mb-2">
            <Label className="text-sm font-medium">Network</Label>
            <Badge variant="outline">{wallet.network}</Badge>
          </div>
          <div className="space-y-2">
            <Label className="text-sm font-medium">Wallet Address</Label>
            <div className="flex items-center gap-2">
              <Input
                value={wallet.address}
                readOnly
                className="font-mono text-xs md:text-sm h-11 md:h-10"
              />
              <Button
                variant="outline"
                size="sm"
                onClick={onClick}
                className="flex items-center gap-1 h-11 md:h-10 px-3"
              >
                {buttonText}
              </Button>
            </div>
          </div>
        </div>

        <Alert className="border-yellow-200 bg-yellow-50">
          <AlertCircle className="h-4 w-4 text-yellow-600" />
          <AlertDescription className="text-yellow-800 text-sm">
            {alertMessage}
          </AlertDescription>
        </Alert>

        <div className="p-6 flex flex-col items-center gap-6">
          <div className="p-6 flex flex-col items-center gap-6">
            <WalletQRCode address={wallet.address} size={240} />
          </div>
        </div>
      </div>
    </div>
  );
}
