"use client";

import { processWithdrawalPayment } from "@/app/admin/actions";
import { Investment, InvestmentStatus, InvestmentStatusType } from "@/app/data/investmentModel";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { useInvestment, useInvestmentBase } from "@/contexts/investment-context";
import { useToast } from "@/hooks/use-toast";
import {
  CheckCircle,
  ChevronDown,
  ChevronUp,
  Clock,
  Download,
  Edit,
  Hourglass,
  XCircle,
} from "lucide-react";
import { useRef, useState } from "react";
import { AppDialogRef } from "../../dialog";
import { WithdrawDialog } from "./WithdrawalDialog";
import { ExtendedInvestmentStatusType } from "../InvestmentList";

type InvestmentCardProps = {
  investment: Investment;
  onClick?: () => void;
  onEdit?: () => void;
  onTabStatus: (status: ExtendedInvestmentStatusType) => void;
  transformedInvestment: any;
  updateInvestment: any;
  wallet: any;
};

export const InvestmentCard = ({
  investment: defaultInvestment,
  onClick,
  onEdit,
  onTabStatus,
  transformedInvestment,
  updateInvestment,
  wallet,


}: InvestmentCardProps) => {

  const { toast } = useToast();
  const investment = transformedInvestment(defaultInvestment);
  const [selectedInvestment, setSelectedInvestment] =
    useState<Investment>(defaultInvestment);

    console.log("investment", investment)

  const getStatusIcon = (investment: Investment) => {
    if (
      (investment.status === "initialize" && investment.verifyingPayment) ||
      (investment.status === "matured" && investment.verifyingPayment)
    ) {
      return <Hourglass className="w-4 h-4" />;
    }

    switch (investment.status) {
      case "active":
        return <Clock className="w-4 h-4" />;
      case "matured":
        return <CheckCircle className="w-4 h-4" />;
      case "closed":
        return <CheckCircle className="w-4 h-4" />;
      default:
        return <Clock className="w-4 h-4" />;
    }
  };

  const apprawalFormRef = useRef<AppDialogRef>(null);

  const withdrawalAmount = investment.currentAmount;

  const processWithdrawal = async () => {
    try {
      const result = await processWithdrawalPayment(defaultInvestment, wallet);
      if (result.success) {
        updateInvestment(investment.id, { verifyingPayment: true });
        apprawalFormRef?.current?.close();
        onTabStatus("pending");
        toast({
          title: "Withdrawal Initiated",
          description: result.message,
        });
      } else {
        throw new Error(result.error || "Something went wrong.");
      }
    } catch (error: any) {
      toast({
        title: "Withdrawal Failed",
        description: error.message || "Something went wrong.",
        variant: "destructive",
      });
      throw error;
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      month: "short",
      day: "numeric",
      year: "numeric",
    });
  };

  const getStatusColor = (investment: Investment) => {
    if (
      (investment.status === "initialize" && investment.verifyingPayment) ||
      (investment.status === "matured" && investment.verifyingPayment)
    ) {
      return "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200";
    }
    switch (investment.status) {
      case "active":
        return "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200";
      case "matured":
        return "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200";
      case "closed":
        return "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200";
      default:
        return "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200";
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
    }).format(amount);
  };

  const calculateCompoundAmount = (
    principal: number,
    rate: number,
    months: number
  ) => {
    return principal * Math.pow(1 + rate / 100, months);
  };

  const isMatured = investment.status === InvestmentStatus.MATURED;
  const isWithdrawn = investment.status === InvestmentStatus.CLOSED
  const isPending =
    (investment.status === InvestmentStatus.INITIALIZE && investment.verifyingPayment) ||
    (investment.status === InvestmentStatus.MATURED && investment.verifyingPayment);

  const handleWithdraw = () => {
    apprawalFormRef.current?.open();
    if (!investment.canWithdraw) {
      toast({
        title: "Cannot Withdraw",
        description:
          "Only the investment creator can withdraw funds. You can nudge them to withdraw.",
        variant: "destructive",
      });
      setSelectedInvestment(defaultInvestment);
      return;
    }
  };

  return (
    <>
      <Card
        key={investment.id}
        onClick={onClick}
        className={`overflow-hidden ${isWithdrawn ? "opacity-75" : ""} 
              transition-colors hover:bg-green-50 dark:hover:bg-gray-900 cursor-pointer`}
      >
        <CardHeader className="pb-3 md:pb-4">
          <div
            className={`flex ${isMatured
                ? "flex-col md:flex-row md:items-center md:justify-between gap-3"
                : "items-start justify-between"
              }`}
          >
            <div className="flex items-start gap-2 md:gap-3 min-w-0 flex-1">
              <div className="min-w-0 flex-1">
                <CardTitle className="text-base md:text-lg flex items-center gap-2 flex-wrap">
                  <span className="truncate">{investment.name}</span>
                  {getStatusIcon(defaultInvestment)}
                  {!investment.isForSelf && (
                    <Badge
                      variant="secondary"
                      className="text-xs flex-shrink-0"
                    >
                      {investment.canWithdraw ? "For" : "From"}{" "}
                      {investment.canWithdraw
                        ? investment.beneficiaryName
                        : investment.createdByName}
                    </Badge>
                  )}
                </CardTitle>
                <CardDescription className="flex flex-col md:flex-row md:items-center gap-1 md:gap-2 mt-1 text-xs md:text-sm">
                  {investment.startDate ? (
                    <span className="whitespace-nowrap">
                      {formatDate(investment.startDate)} -{" "}
                      {formatDate(investment.endDate)}
                    </span>
                  ) : investment.status === InvestmentStatus.CLOSED &&
                    investment.withdrawn_at ? (
                    <span>Closed on {formatDate(investment.withdrawn_at)}</span>
                  ) : (
                    <span>N/A</span>
                  )}

                  <Badge
                    variant="outline"
                    className={`${getStatusColor(defaultInvestment)} w-fit`}
                  >
                    {(investment.status === InvestmentStatus.INITIALIZE &&
                      investment.verifyingPayment) ||
                      (investment.status === InvestmentStatus.MATURED &&
                        investment.verifyingPayment)
                      ? "pending"
                      : investment.status}
                  </Badge>
                </CardDescription>
              </div>
            </div>
            {/* Header right side */}
            {isMatured && !isWithdrawn ? (
              <div className="flex items-center gap-2 w-full md:w-auto">
                {investment.verifyingPayment ? (
                  <Button
                    disabled
                    className="bg-yellow-500 hover:bg-yellow-600 text-white flex items-center gap-2 w-full md:w-auto h-11 md:h-10"
                  >
                    <Hourglass className="w-4 h-4" />
                    Verifying Withdrawal Payment
                  </Button>
                ) : investment.canWithdraw ? (
                  <Button
                    onClick={(e) => {
                      e.stopPropagation();
                      handleWithdraw();
                    }}
                    className="bg-green-600 hover:bg-green-700 text-white flex items-center gap-2 w-full md:w-auto h-11 md:h-10"
                  >
                    <Download className="w-4 h-4" />
                    Withdraw
                  </Button>
                ) : null}
              </div>
            ) : isPending || investment.status === InvestmentStatus.INITIALIZE ? (
              <div className="flex items-center gap-2 w-full md:w-auto">
                {investment.verifyingPayment ? (
                  <Button
                    disabled
                    className="bg-yellow-500 hover:bg-yellow-600 text-white flex items-center gap-2 w-full md:w-auto h-11 md:h-10"
                  >
                    <Hourglass className="w-4 h-4" />
                    Verifying Payment
                  </Button>
                ) : (
                  <Button
                    onClick={(e) => {
                      e.stopPropagation();
                      onEdit?.();
                    }}
                    variant="default"
                    className="bg-gray-100 hover:bg-gray-300 text-gray-900 flex items-center gap-2 w-full md:w-auto h-11 md:h-10"
                  >
                    <Edit className="w-4 h-4" />
                    Edit
                  </Button>
                )}
              </div>
            ) : null}

            {isWithdrawn && (
              <Button
                disabled
                variant="default"
                className="bg-green-600 hover:bg-green-700 text-white flex items-center gap-2 w-full md:w-auto h-11 md:h-10"
              >
                <CheckCircle className="w-4 h-4" />
                Withdrawn
              </Button>
            )}
          </div>
        </CardHeader>

        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 md:gap-6">
            {/* Initial Amount */}
            <div>
              <p className="text-xs md:text-sm font-medium text-gray-600 dark:text-gray-400 mb-1">
                Initial Amount
              </p>
              <p className="text-lg md:text-xl font-bold text-gray-900 dark:text-white">
                {formatCurrency(investment.initialAmount)}
              </p>
            </div>

            {/* Value section */}
            <div>
              <p className="text-xs md:text-sm font-medium text-gray-600 dark:text-gray-400 mb-1">
                {isWithdrawn
                  ? "Withdrawn"
                  : isMatured
                    ? "Final Value"
                    : "Current Value"}
              </p>
              {!investment.isForSelf && investment.canWithdraw ? (
                <div className="space-y-1">
                  <p className="text-lg md:text-xl font-bold text-green-600 dark:text-green-400">
                    {formatCurrency(investment.currentAmount)}
                  </p>
                  <p className="text-xs md:text-sm text-green-600 dark:text-green-400">
                    +
                    {formatCurrency(
                      investment.currentAmount - investment.initialAmount
                    )}{" "}
                    profit (10%)
                  </p>
                  <p className="text-xs md:text-sm text-gray-500 dark:text-gray-400">
                    Friend sees:{" "}
                    {formatCurrency(
                      calculateCompoundAmount(
                        investment.initialAmount,
                        investment.beneficiaryRate,
                        investment.monthsElapsed
                      )
                    )}
                  </p>
                </div>
              ) : (
                <>
                  <p className="text-lg md:text-xl font-bold text-green-600 dark:text-green-400">
                    {formatCurrency(investment.currentAmount)}
                  </p>
                  <p className="text-xs md:text-sm text-green-600 dark:text-green-400">
                    +
                    {formatCurrency(
                      investment.currentAmount - investment.initialAmount
                    )}{" "}
                    profit
                  </p>
                </>
              )}
            </div>

            {/* Middle blocks differ by status */}
            {isWithdrawn ? (
              <>
                {/* Withdrawal Info */}
                <div>
                  <p className="text-xs md:text-sm font-medium text-gray-600 dark:text-gray-400 mb-1">
                    Withdrawal Info
                  </p>
                  <div className="space-y-1 md:space-y-2">
                    <div className="flex justify-between text-xs md:text-sm">
                      <span>Network</span>
                      <span className="font-medium">{investment.network}</span>
                    </div>
                    <div className="flex justify-between text-xs md:text-sm">
                      <span>Wallet</span>
                      <span className="font-medium font-mono text-xs truncate max-w-[100px]">
                        {investment.walletAddress}
                      </span>
                    </div>
                  </div>
                </div>

                {/* Duration + ROI */}
                <div>
                  <p className="text-xs md:text-sm font-medium text-gray-600 dark:text-gray-400 mb-1">
                    Duration
                  </p>
                  <div className="space-y-1 md:space-y-2">
                    <div className="flex justify-between text-xs md:text-sm">
                      <span>{investment.duration} months</span>
                      <span className="font-medium text-gray-500 dark:text-gray-400">
                        Completed
                      </span>
                    </div>
                    <div className="flex justify-between text-xs md:text-sm">
                      <span>ROI</span>
                      <span className="font-medium text-green-600 dark:text-green-400">
                        {(
                          ((investment.currentAmount -
                            investment.initialAmount) /
                            investment.initialAmount) *
                          100
                        ).toFixed(1)}
                        %
                      </span>
                    </div>
                  </div>
                </div>
              </>
            ) : (
              <>
                {/* Rate & Duration */}
                <div>
                  <p className="text-xs md:text-sm font-medium text-gray-600 dark:text-gray-400 mb-1">
                    Rate & Duration
                  </p>
                  <div className="space-y-1 md:space-y-2">
                    <div className="flex justify-between text-xs md:text-sm">
                      {!investment.isForSelf && investment.canWithdraw ? (
                        <div className="flex flex-col">
                          <span className="text-[#245c1a] font-medium">
                            Your rate: 10%
                          </span>
                          <span className="text-gray-500">
                            Friend sees: {investment.beneficiaryRate}%
                          </span>
                        </div>
                      ) : (
                        `${investment.beneficiaryRate}% monthly`
                      )}

                      <span
                        className={`font-medium ${isMatured || isWithdrawn
                            ? "text-green-600 dark:text-green-400"
                            : ""
                          }`}
                      >
                        {isMatured || isWithdrawn
                          ? "Completed"
                          : `${investment.profitPaymentsCount}/${investment.duration} months`}
                      </span>
                    </div>
                    <div className="flex justify-between text-xs md:text-sm">
                      <span>Payment</span>
                      <span className="font-medium capitalize">
                        {investment.paymentMethod}
                        {investment.network && ` (${investment.network})`}
                      </span>
                    </div>
                  </div>
                </div>

                {/* Progress / Status */}
                <ProgressSection investment={investment} />
              </>
            )}
          </div>
        </CardContent>

        {/* Shared beneficiary breakdown */}
        {investment.multiSetupData && investment.multiSetupData.length > 0 && (
          <div className="mt-4 pt-4 border-t">
            <h5 className="text-sm font-medium text-gray-600 dark:text-gray-400 mb-3">
              Beneficiary Breakdown ({investment.multiSetupData.length} people)
            </h5>
            <div className="grid gap-2">
              <CollapsibleBeneficiaries
                beneficiaries={investment.multiSetupData}
              />
            </div>
          </div>
        )}
      </Card>

      {selectedInvestment && (
        <WithdrawDialog
          ref={apprawalFormRef}
          description={`Withdraw your ${formatCurrency(
            withdrawalAmount
          )} to your wallet address.`}
          onSubmit={processWithdrawal}
        />
      )}
    </>
  );
};

type ProgressSectionProps = {
  investment: {
    status: string;
    progressPercentage: number;
    endDate: string;
    canWithdraw: boolean;
    verifyingPayment: boolean;
  };
};

const ProgressSection = ({ investment }: ProgressSectionProps) => {
  // figure out effective status (normalize "pending")
  const isPending =
    (investment.status === InvestmentStatus.INITIALIZE && investment.verifyingPayment) ||
    (investment.status === InvestmentStatus.MATURED && investment.verifyingPayment);

  const effectiveStatus = isPending ? "pending" : investment.status;

  // status → icon map
  const iconMap: Record<
    "matured" | "active" | "pending" | "closed" | "initialize",
    React.ElementType
  > = {
    matured: CheckCircle,
    active: Clock,
    pending: Hourglass,
    closed: CheckCircle,
    initialize: Clock,
  };

  const Icon = iconMap[effectiveStatus as keyof typeof iconMap];

  // helper for days left
  function getDaysRemaining(endDate: string): number {
    const end = new Date(endDate);
    const now = new Date();
    const diffTime = end.getTime() - now.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return Math.max(0, diffDays); // avoid negatives
  }

  const isMatured = investment.status === InvestmentStatus.MATURED;

  const progress = isMatured ? 100 : investment.progressPercentage;
  const formattedProgress = Number.isInteger(progress)
    ? progress.toString()
    : progress.toFixed(1);

  const title = isMatured
    ? investment.verifyingPayment
      ? "Awaiting verification"
      : investment.canWithdraw
        ? "Ready to withdraw"
        : "Awaiting creator"
    : `${formattedProgress}% complete`;

  const description =
    investment.status === InvestmentStatus.INITIALIZE
      ? "Not started"
      : investment.status === InvestmentStatus.ACTIVE
        ? `${getDaysRemaining(investment.endDate)} days left`
        : "Matured";

  return (
    <div>
      <p className="text-xs md:text-sm font-medium text-gray-600 dark:text-gray-400 mb-1">
        {isMatured ? "Status" : "Progress"}
      </p>
      <div className="space-y-1 md:space-y-2">
        <Progress
          value={progress}
          className="h-2 bg-gray-200 dark:bg-gray-700"
        />

        <div className="flex justify-between text-xs md:text-sm">
          <span className="text-green-600 dark:text-green-400 font-medium">
            {title}
          </span>
          <span className="text-gray-500 dark:text-gray-400 flex items-center gap-1">
            <Icon className="w-3 h-3" />
            {description}
          </span>
        </div>
      </div>
    </div>
  );
};

type BeneficiaryType = {
  id: string;
  type: string;
  beneficiaryId: string;
  beneficiaryName: string;
  amount: number;
  percentage: number;
  currentAmount: number;
};

function CollapsibleBeneficiaries({
  beneficiaries,
}: {
  beneficiaries: BeneficiaryType[];
}) {
  const [expanded, setExpanded] = useState(false);

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
    }).format(amount);
  };

  // Show 1 item by default if not expanded
  const visibleBeneficiaries = expanded
    ? beneficiaries
    : beneficiaries.slice(0, 1);

  return (
    <div
      className="grid gap-2"
      onClick={(e) => {
        e.stopPropagation();
        setExpanded(!expanded);
      }}
    >
      {visibleBeneficiaries.map((beneficiary: any, i) => (
        <div
          key={i}
          className="flex items-center justify-between p-2 bg-gray-50 dark:bg-gray-800 rounded text-xs"
        >
          <div className="flex items-center gap-2">
            <span className="font-medium">{beneficiary.beneficiaryName}</span>
            <Badge variant="outline" className="text-xs">
              {beneficiary.type === "self" ? "You" : "Friend"}
            </Badge>
          </div>
          <div className="text-right">
            <div className="font-medium text-green-600">
              {formatCurrency(beneficiary.currentAmount)}
            </div>
            <div className="text-gray-500">
              {beneficiary.type === "self" ? (
                `10% • ${formatCurrency(beneficiary.amount)} initial`
              ) : (
                <div className="flex flex-col text-right">
                  <span>You earn: 10%</span>
                  <span>They see: {beneficiary.percentage}%</span>
                  <span>{formatCurrency(beneficiary.amount)} initial</span>
                </div>
              )}
            </div>
          </div>
        </div>
      ))}

      {beneficiaries.length > 1 && (
        <button
          onClick={() => setExpanded(!expanded)}
          className="flex items-center justify-center gap-1 text-green-600 hover:text-green-800 transition-colors"
        >
          {expanded ? (
            <ChevronUp className="h-5 w-5" />
          ) : (
            <ChevronDown className="h-5 w-5" />
          )}
        </button>
      )}
    </div>
  );
}
