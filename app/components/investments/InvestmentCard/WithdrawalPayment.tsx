"use client";

import { useAdmin } from "@/contexts/admin-provider";
import { useEffect } from "react";
import { WalletAddressDisplay } from "./WalletAddressDisplay";

type WithdrawPaymentProps = {};

export const WithdrawPayment = ({}: WithdrawPaymentProps) => {
  const { wallet,onOpenWalletDialog } = useAdmin();
  const canClose = Boolean(wallet.address && wallet.network);

  useEffect(() => {
    if (!canClose) {
      onOpenWalletDialog();
    }
  }, [canClose]);

  const buttonText = "Edit";
  const alertMessage = (
    <>
      <strong>Important:</strong> Ensure the wallet address is correct. Funds
      sent to the wrong address cannot be recovered.
    </>
  );

  return (
    <>
      {canClose ? (
        <WalletAddressDisplay
          wallet={wallet}
          onClick={() => onOpenWalletDialog()}
          buttonText={buttonText}
          alertMessage={alertMessage}
        />
      ) : null}
    </>
  );
};
