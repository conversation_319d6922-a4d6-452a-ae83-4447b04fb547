"use client";

import { Alert, AlertDescription } from "@/components/ui/alert";
import { AlertCircle } from "lucide-react";
import { RefObject, useState } from "react";
import { AppDialogRef, ApplicationDialog } from "../../dialog";
import { FormErrors } from "../InvestmentList/CircleMembersList";
import { WithdrawPayment } from "./WithdrawalPayment";

type WithdrawalDialogProps = {
  ref?: RefObject<AppDialogRef | null>;
  description: string;
  onSubmit: () => Promise<void>;
};

export const WithdrawDialog = ({
  ref,
  description,
  onSubmit,
}: WithdrawalDialogProps) => {
  const [errors, setErrors] = useState<FormErrors>({});
  const handleWitdrawal = async () => {
    try {
      await onSubmit();
      setErrors({});
    } catch (error: any) {
      setErrors({ general: error.message });
    }
  };

  return (
    <ApplicationDialog
      ref={ref}
      componentType="drawer"
      title="Withdraw Investment"
      description={description}
      icon="download"
      onSubmit={handleWitdrawal}
      submitButtonProps={{
        content: "Confirm Withdrawal",
      }}
      isDisabled={false}
      maxWidth="md"
    >
      <>
        {errors.general && (
          <Alert className="border-red-200 bg-red-50">
            <AlertCircle className="h-4 w-4 text-red-600" />
            <AlertDescription className="text-red-800 text-sm">
              {errors.general}
            </AlertDescription>
          </Alert>
        )}
        <WithdrawPayment />
      </>
    </ApplicationDialog>
  );
};
