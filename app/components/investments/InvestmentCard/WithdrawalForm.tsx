import {
  validateAndSaveAddress,
  withdrawalAddressChangeVerification,
} from "@/app/admin/actions";
import { useCountdown } from "@/app/auth/hooks/use-countdown";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { WalletType } from "@/contexts/admin-provider";
import { useToast } from "@/hooks/use-toast";
import {
  validateVerificationCode,
  validateWalletAddress,
} from "@/lib/utils/formValidation";
import { AlertCircle, CheckCircle } from "lucide-react";
import { useEffect, useImperativeHandle, useState } from "react";
import { FormErrors } from "../InvestmentList/CircleMembersList";

export type WithdrawalFormResultProps = {
  walletAddress: string;
  network: string;
  verificationCode: string;
};

export type WithdrawalFormRefProps = {
  onSubmit: () => Promise<WalletType | null>;
};

export type WithdrawalFormProps = {
  ref?: React.Ref<WithdrawalFormRefProps>;
  onChange?: (valid: boolean) => void;
  wallet: WalletType;
};

type WithdrawalFormErrors = {
  general?: string;
  walletAddress?: string;
  network?: string;
  verificationCode?: string;
};

export const WithdrawalForm = ({
  ref,
  onChange,
  wallet,
}: WithdrawalFormProps) => {
  const [touched, setTouched] = useState<{ [key: string]: boolean }>({});
  const [errors, setErrors] = useState<WithdrawalFormErrors>({});
  const [walletAddress, setWalletAddress] = useState(wallet.address);
  const [network, setNetwork] = useState(wallet.network);
  const [verificationCode, setVerificationCode] = useState("");
  const { cooldown, onTrigger } = useCountdown();

  // run the onChange function to check if the form is valid because the wallet address might be the same
  useEffect(() => {
    if (onChange) {
      onChange(isValid({ walletAddress, network, verificationCode }));
    }
  }, []);

  function isDuplicateWallet() {
    if (!wallet?.address) {
      return false;
    }
    return (
      wallet.address.toLowerCase() === walletAddress.toLowerCase() &&
      wallet.network.toLowerCase() === network.toLowerCase()
    );
  }

  async function onSubmit() {
    try {
      const _wallet = { address: walletAddress, network };
      // if the user wallet did not change just return the wallet
      if (isDuplicateWallet()) {
        return _wallet;
      }
      const result = await validateAndSaveAddress(_wallet, verificationCode);
      if (!result.success) {
        throw result;
      }
      if ("data" in result && result.data) {
        return result.data as WalletType;
      }
      return null;
    } catch (error: any) {
      if (error.field) {
        setErrors((prev) => ({
          ...prev,
          [error.field as keyof FormErrors]: error.error,
        }));
      }
      console.error("Submit error:", error);
      return null;
    }
  }

  async function sendVerificationCode() {
    try {
      await onTrigger(async () => {
        await withdrawalAddressChangeVerification();
      });
      return { success: true };
    } catch (error) {
      console.log(error);
      return { success: false, error: true };
    }
  }

  useImperativeHandle(ref, () => ({
    onSubmit,
  }));

  function validateForm(
    values: WithdrawalFormResultProps
  ): WithdrawalFormErrors {
    const isDuplicate = isDuplicateWallet();
    return {
      walletAddress:
        validateWalletAddress(values.walletAddress, values.network) ||
        undefined,
      network: undefined,
      verificationCode: !isDuplicate
        ? validateVerificationCode(values.verificationCode) || undefined
        : undefined,
    };
  }

  function isValid(values: WithdrawalFormResultProps): boolean {
    const validation = validateForm(values);
    return Object.values(validation).every((err) => err === undefined);
  }

  const handleWithdrawInputChange = (field: string, value: string) => {
    const formData = { walletAddress, network, verificationCode };

    if (field === "walletAddress") {
      setWalletAddress(value);
      formData.walletAddress = value;
    } else if (field === "network") {
      setNetwork(value);
      formData.network = value;
    } else if (field === "verificationCode") {
      setVerificationCode(value);
      formData.verificationCode = value;
    }

    // Run validation every time input changes
    const newErrors = validateForm(formData);

    setErrors(newErrors);

    if (onChange) {
      onChange(isValid(formData));
    }
  };
  const handleWithdrawBlur = (field: string) => {
    setTouched((prev) => ({ ...prev, [field]: true }));

    const formData = { walletAddress, network, verificationCode };

    setErrors(validateForm(formData));
  };

  const onNetworkSelect = (value: string) => {
    setNetwork(value);

    const formData = { walletAddress, network: value, verificationCode };
    const newErrors = validateForm(formData);

    setErrors(newErrors);

    if (onChange) {
      onChange(isValid(formData));
    }
  };

  const networks = [
    { value: "TRC20", label: "TRC20 (Tron) - Low fees" },
    { value: "ERC20", label: "ERC20 (Ethereum)" },
    { value: "BEP20", label: "BEP20 (BSC)" },
  ];

  return (
    <div className="grid gap-4 py-4">
      {errors.general && (
        <Alert className="border-red-200 bg-red-50">
          <AlertCircle className="h-4 w-4 text-red-600" />
          <AlertDescription className="text-red-800 text-sm">
            {errors.general}
          </AlertDescription>
        </Alert>
      )}

      <div className="space-y-2 w-full max-w-sm mx-auto">
        <Label
          htmlFor="wallet-address"
          className="text-sm sm:text-base font-medium"
        >
          New Wallet Address
        </Label>

        <div className="relative">
          <Input
            id="wallet-address"
            placeholder="Enter your USDT wallet address"
            value={walletAddress}
            onChange={(e) =>
              handleWithdrawInputChange("walletAddress", e.target.value)
            }
            onBlur={() => handleWithdrawBlur("walletAddress")}
            className={`
        pr-10 text-sm sm:text-base
        h-10 sm:h-11
        placeholder:text-xs sm:placeholder:text-sm
        rounded-lg sm:rounded-xl
        ${errors.walletAddress && touched.walletAddress
                ? "border-red-500 focus:ring-red-500 focus:border-red-500"
                : touched.walletAddress &&
                  !errors.walletAddress &&
                  walletAddress.trim()
                  ? "border-green-500 focus:ring-green-500 focus:border-green-500"
                  : ""}
      `}
            inputMode="text"
            autoComplete="off"
            spellCheck="false"
          />

          {touched.walletAddress &&
            !errors.walletAddress &&
            walletAddress.trim() && (
              <CheckCircle className="absolute right-3 top-1/2 -translate-y-1/2 h-4 w-4 text-green-500" />
            )}
        </div>

        {errors.walletAddress && touched.walletAddress && (
          <p className="text-xs sm:text-sm text-red-600 flex items-center gap-1">
            <AlertCircle className="w-3 h-3 shrink-0" />
            {errors.walletAddress}
          </p>
        )}
      </div>


      <div className="space-y-2">
        <Label htmlFor="network-select" className="text-sm md:text-base">
          Network
        </Label>
        <Select value={network} onValueChange={onNetworkSelect}>
          <SelectTrigger className="h-11 md:h-10">
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            {networks.map((network) => (
              <SelectItem key={network.value} value={network.value}>
                {network.label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      <EmailVerificationField
        onChange={(value) =>
          handleWithdrawInputChange("verificationCode", value)
        }
        defaultError={errors.verificationCode}
        cooldown={cooldown}
        onSendCode={sendVerificationCode}
        isDisabled={isDuplicateWallet()}
      />

      <div className="p-4 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg">
        <p className="text-sm text-yellow-800 dark:text-yellow-200">
          <strong>Important:</strong> Please ensure your wallet address and
          network are correct. Transactions cannot be reversed.
        </p>
      </div>
    </div>
  );
};

type EmailVerificationFieldProps = {
  onChange: (value: string) => void;
  defaultError?: string;
  cooldown?: number;
  onSendCode: () => Promise<{
    success?: boolean;
    error?: boolean;
  }>;
  isDisabled?: boolean;
};

const EmailVerificationField = ({
  onChange,
  defaultError = "",
  cooldown = 0,
  onSendCode,
  isDisabled: _isDisabled,
}: EmailVerificationFieldProps) => {
  const { toast } = useToast();

  const [touched, setTouched] = useState<boolean>(false);
  const [isSendingVerification, setIsSendingVerification] = useState(false);
  const [errors, setErrors] = useState(defaultError);
  const [verificationCode, setVerificationCode] = useState("");

  const isDisabled = cooldown > 0;

  const handleWithdrawInputChange = (value: string) => {
    setVerificationCode(value);
    onChange(value);

    // Clear errors when user starts typing
    setErrors("");

    // Real-time validation for touched fields
    if (touched) {
      let newErrors = "";

      const codeError = validateVerificationCode(value);
      if (codeError) {
        newErrors = codeError;
      }

      setErrors(newErrors);
    }
  };

  const handleWithdrawBlur = () => {
    setTouched(true);

    let newErrors = "";

    const codeError = validateVerificationCode(verificationCode);
    if (codeError) {
      newErrors = codeError;
    }
    setErrors(newErrors);
  };

  const sendVerificationCode = async () => {
    setIsSendingVerification(true);
    try {
      const result = await onSendCode();

      if (result.success) {
        toast({
          title: "Verification Code Sent",
          description:
            "A verification code has been sent to your email address.",
        });
      } else {
        toast({
          title: "Failed to send code",
          description: result.error || "Please try again in a moment",
          variant: "destructive",
        });
      }
    } catch (error) {
      toast({
        title: "Failed to send code",
        description: "Please try again in a moment",
        variant: "destructive",
      });
    } finally {
      setIsSendingVerification(false);
    }
  };

  const OTP_LENGTH = 8;

  return (
    <div className="space-y-2">
      <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-2">
        <Label htmlFor="verification-code" className="text-sm md:text-base">
          Email Verification Code
        </Label>
        <Button
          variant="outline"
          size="sm"
          onClick={sendVerificationCode}
          disabled={_isDisabled || isSendingVerification || isDisabled}
          className="h-9 w-full md:w-auto bg-transparent"
        >
          {isSendingVerification ? "Sending..." : "Send Code"}
        </Button>
      </div>
      <div className="relative">
        <Input
          id="verification-code"
          placeholder={`Enter ${OTP_LENGTH}-digit code`}
          value={verificationCode}
          onChange={(e) => {
            const value = e.target.value
              .replace(/\D/g, "")
              .slice(0, OTP_LENGTH);
            handleWithdrawInputChange(value);
          }}
          onBlur={() => handleWithdrawBlur()}
          className={`text-center font-mono tracking-widest pr-10 h-11 md:h-10 ${errors && touched
              ? "border-red-500 focus:ring-red-500 focus:border-red-500"
              : touched && !errors && verificationCode.length === OTP_LENGTH
                ? "border-green-500 focus:ring-green-500 focus:border-green-500"
                : ""
            }`}
          maxLength={OTP_LENGTH}
          disabled={_isDisabled || isSendingVerification}
        />
        {touched && !errors && verificationCode.length === OTP_LENGTH && (
          <CheckCircle className="absolute right-3 top-3 h-4 w-4 text-green-500" />
        )}
      </div>
      {defaultError && touched && (
        <p className="text-xs text-red-600 flex items-center gap-1">
          <AlertCircle className="w-3 h-3" />
          {defaultError}
        </p>
      )}
      <div className="flex justify-between text-xs text-gray-500">
        <span>A verification code will be sent to your email</span>
        <span
          className={
            verificationCode.length === OTP_LENGTH ? "text-green-600" : ""
          }
        >
          {verificationCode.length}/{OTP_LENGTH}
        </span>
      </div>
      {cooldown > 0 && (
        <p className="text-xs text-red-500">
          Please wait {cooldown} seconds before sending another code
        </p>
      )}
    </div>
  );
};
