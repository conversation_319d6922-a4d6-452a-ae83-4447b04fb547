"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
    Activity,
    Hourglass,
    Plus,
    Wallet,
    XCircle
} from "lucide-react";

import { Investment, InvestmentStatusType } from "@/app/data/investmentModel";
import { useRouter } from "next/navigation";
import { InvestmentCard } from "../InvestmentCard";
import { ExtendedInvestmentStatusType } from "../InvestmentList";
import { WalletType } from "@/contexts/investment-context";

export type InvestmentTabsProps = {
    investments: Investment[];
    transformedInvestment: (investment: Investment) => any;
    updateInvestment: (id: string, updates: Partial<Investment>) => void;
    wallet: WalletType;
    tabStatus: ExtendedInvestmentStatusType;
    setTabStatus: (status: ExtendedInvestmentStatusType) => void;
    clickable?: boolean;
    onEditInvestment?: (investment: Investment) => void;
    onCreateInvestment?: () => void;
}


export const InvestmentTabs = ({
    investments,
    transformedInvestment,
    updateInvestment,
    wallet,
    tabStatus,
    setTabStatus,
    clickable,
    onEditInvestment,
    onCreateInvestment,
}: InvestmentTabsProps) => {
    const router = useRouter();
    const statuses: string[] = ["active", "closed", "pending", "all"];

    return (
        <Tabs
            value={tabStatus}
            onValueChange={(value) => setTabStatus(value as InvestmentStatusType)}
            className="mb-6"
        >
            <TabsList className="grid w-full grid-cols-4 h-12 md:h-10">
                {statuses.map((status) => (
                    <TabsTrigger
                        key={status}
                        value={status}
                        onClick={() => setTabStatus(status as ExtendedInvestmentStatusType)}
                        className="text-xs md:text-sm"
                    >
                        {status.charAt(0).toUpperCase() + status.slice(1)}
                    </TabsTrigger>
                ))}
            </TabsList>

            {statuses.map((status) => {
                const filteredInvestments = investments.filter((investment) => {
                    const displayStatus =
                        (investment.status === "initialize" && investment.verifyingPayment) ||
                            (investment.status === "matured" && investment.verifyingPayment)
                            ? "pending"
                            : investment.status;

                    if (status === "all") return true;
                    if (status === "closed")
                        return displayStatus === "matured" || displayStatus === "closed";
                    return displayStatus === status;
                });

                return (
                    <TabsContent key={status} value={status} className="mt-4 md:mt-6">
                        <InvestmentTabContent
                            status={
                                status as "active" | "matured" | "closed" | "pending" | "all"
                            }
                            onClick={onCreateInvestment}
                            isEmpty={filteredInvestments.length === 0}
                        >
                            {filteredInvestments.map((investment) => (
                                <InvestmentCard
                                    key={investment.id}
                                    investment={investment}
                                    {...(onEditInvestment && {
                                        onEdit: () => onEditInvestment(investment),
                                    })}
                                    {...(clickable && {
                                        onClick: () =>
                                            router.push(`/admin/investments/${investment.id}`),
                                    })}
                                    onTabStatus={setTabStatus}
                                    transformedInvestment={transformedInvestment}
                                    updateInvestment={updateInvestment}
                                    wallet={wallet}
                                />
                            ))}
                        </InvestmentTabContent>
                    </TabsContent>
                );
            })}
        </Tabs>
    );
};


type InvestmentTabContentProps = {
    children: React.ReactNode;
    isEmpty?: boolean;
    onClick?: () => void;
    status: "active" | "matured" | "closed" | "pending" | "all";
};

const InvestmentTabContent = ({
    children,
    isEmpty,
    onClick,
    status,
}: InvestmentTabContentProps) => {
    const content = {
        active: {
            title: "No Active Investments",
            description: `You don't have any active investments yet. Start your first investment
          to begin earning compound returns.`,
            icon: {
                component: Activity,
                className: "text-blue-600 dark:text-blue-400",
                bgClass: "bg-blue-100 dark:bg-blue-900/20",
            },
        },
        matured: {
            title: "No Matured Investments",
            description: `You don't have any matured investments ready for withdrawal yet.
          Your active investments will appear here when they complete.`,
            icon: {
                component: Wallet,
                className: "text-green-600 dark:text-green-400",
                bgClass: "bg-green-100 dark:bg-green-900/20",
            },
        },
        pending: {
            title: "No Pending Investments",
            description: `You don't have any pending investments yet.Create an investment to get started.`,
            icon: {
                component: Hourglass,
                className: "text-yellow-500 dark:text-yellow-400",
                bgClass: "bg-yellow-100 dark:bg-yellow-900/20",
            },
        },
        closed: {
            title: "No Closed Investments",
            description: `You don't have any withdrawn investments yet. Completed investments
          that have been withdrawn will appear here.`,
            icon: {
                component: XCircle,
                className: "text-gray-600 dark:text-gray-400",
                bgClass: "bg-gray-100 dark:bg-gray-800",
            },
        },
        all: {
            title: "No Investments Yet",
            description: `Start your investment journey today. Create your first USDT
          investment and watch your money grow with compound interest.`,
            icon: {
                component: Wallet,
                className: "text-blue-600 dark:text-blue-400",
                bgClass: "bg-blue-100 dark:bg-blue-900/20",
            },
        },
    };

    const item = content[status];
    const Icon = item.icon.component;

    const onCreateInvestment = status === "closed" || status === "all";

    return (
        <div className="grid gap-4 md:gap-6">
            {isEmpty ? (
                <div className="text-center py-8 md:py-12">
                    <div
                        className={`mx-auto w-16 h-16 md:w-24 md:h-24 ${item.icon.bgClass} rounded-full flex items-center justify-center mb-4`}
                    >
                        <Icon
                            className={`w-8 h-8 md:w-12 md:h-12 ${item.icon.className}`}
                        />
                    </div>
                    <h3 className="text-base md:text-lg font-semibold text-gray-900 dark:text-white mb-2">
                        {item.title}
                    </h3>
                    <p className="text-sm md:text-base text-gray-500 dark:text-gray-400 mb-4 md:mb-6 max-w-md mx-auto px-4">
                        {item.description}
                    </p>
                    {onCreateInvestment && (
                        <>
                            <Button
                                onClick={onClick}
                                className="bg-[#245c1a] hover:bg-[#1a4513] dark:bg-green-600 dark:hover:bg-green-700 h-11 md:h-10"
                            >
                                <Plus className="w-4 h-4 mr-2" />
                                Create Your First Investment
                            </Button>
                        </>
                    )}
                </div>
            ) : (
                <>{children}</>
            )}
        </div>
    );
};


