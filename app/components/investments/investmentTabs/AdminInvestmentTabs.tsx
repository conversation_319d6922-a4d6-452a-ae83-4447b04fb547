// components/investments/InvestmentTabs/AdminInvestmentTabs.tsx
"use client";

import { useInvestmentAdmin } from "@/contexts/investment-context";
import { InvestmentTabs } from "../investmentTabs/index";
import { Investment } from "@/app/data/investmentModel";
import { ExtendedInvestmentStatusType } from "../InvestmentList";

export type AdminInvestmentTabsProps = {
  investments: any[];
  tabStatus: ExtendedInvestmentStatusType;
  setTabStatus: (status: ExtendedInvestmentStatusType) => void;
  clickable: boolean;
  onEditInvestment: (investment: Investment) => void;
  onCreateInvestment: () => void;
  
};

export const AdminInvestmentTabs = ({
  investments,
  tabStatus,
  setTabStatus,
  clickable,
  onEditInvestment,
  onCreateInvestment,
}: AdminInvestmentTabsProps) => {
  const { transformedInvestment, updateInvestment, wallet } =
    useInvestmentAdmin();


  return (
    <InvestmentTabs
      investments={investments}
      transformedInvestment={transformedInvestment}
      updateInvestment={updateInvestment}
      wallet={wallet}
      tabStatus={tabStatus}
      setTabStatus={setTabStatus}
      clickable={clickable}
      onEditInvestment={onEditInvestment}
      onCreateInvestment={onCreateInvestment}
    />
  );
};
