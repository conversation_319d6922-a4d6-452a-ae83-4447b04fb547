"use client";

import { useInvestmentBase } from "@/contexts/investment-context";
import { InvestmentTabs } from "../investmentTabs/index";
import { ExtendedInvestmentStatusType } from "../InvestmentList";


export type UserInvestmentTabsProps = {
    investments: any[];
    tabStatus: ExtendedInvestmentStatusType;
    setTabStatus: (status: ExtendedInvestmentStatusType) => void;

}


export const UserInvestmentTabs = ({ investments, tabStatus, setTabStatus }: UserInvestmentTabsProps) => {
    const { transformedInvestment, updateInvestment, wallet } =
        useInvestmentBase();

    return (
        <InvestmentTabs
            investments={investments}
            transformedInvestment={transformedInvestment}
            updateInvestment={updateInvestment}
            wallet={wallet}
            tabStatus={tabStatus}
            setTabStatus={setTabStatus}
        />
    );
};
