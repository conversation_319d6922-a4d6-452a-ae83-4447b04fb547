"use client";

import { InvestmentFormType } from "./InvestmentForm";



type InvestmentSummaryProps = {
  investment: InvestmentFormType;
  detailed?: boolean;
};

export function InvestmentFormSummary({
  investment,
  detailed,
}: InvestmentSummaryProps) {
  const { name, duration, beneficiaries } = investment;

  const formatCurrency = (amount: number) =>
    new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
    }).format(amount);

  const getTotal = () =>
    beneficiaries?.reduce(
      (total, b) => total + Number.parseFloat(b.amount || "0"),
      0
    );

  // Common summary fields
  const fields = [
    { label: "Investment Group", value: name },
    { label: "Total Amount", value: formatCurrency(getTotal()) },
    { label: "Beneficiaries", value: `${beneficiaries?.length} people` },
    { label: "Duration", value: `${duration} months` },
  ];

  return (
    <div className="p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
      <h4 className="font-medium mb-2 text-sm md:text-base">
        Investment Summary
      </h4>

      <div className="space-y-1 text-xs md:text-sm">
        {fields.map((field) => (
          <div key={field.label} className="flex justify-between">
            <span>{field.label}:</span>
            <span className="font-medium">{field.value}</span>
          </div>
        ))}

        {/* Extra details if detailed = true */}
        {detailed && beneficiaries.length > 0 && (
          <div className="mt-2 pt-2 border-t">
            <div className="text-xs space-y-1">
              {beneficiaries.map((beneficiary) => (
                <div key={beneficiary.id} className="flex justify-between">
                  <span>{beneficiary.beneficiaryName}:</span>
                  <span>
                    {formatCurrency(Number.parseFloat(beneficiary.amount))} @{" "}
                    {beneficiary.percentage}%
                  </span>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  );
}