import { Alert, AlertDescription } from "@/components/ui/alert";
import { useToast } from "@/hooks/use-toast";
import { AlertCircle, Check, Copy } from "lucide-react";
import { useState } from "react";
import { WalletAddressDisplay } from "../InvestmentCard/WalletAddressDisplay";
import { InvestmentFormType } from "./InvestmentForm";
import { InvestmentFormSummary } from "./InvestmentFormSummary";

interface FormErrors {
  name?: string;
  email?: string;
  investmentName?: string;
  amount?: string;
  beneficiaryRate?: string;
  walletAddress?: string;
  verificationCode?: string;
  cardNumber?: string;
  expiryDate?: string;
  cvv?: string;
  cardName?: string;
  general?: string;
}

// Wallet addresses for different networks
const WALLET_ADDRESSES = {
  TRC20: "TQn9Y2khEsLJW1ChVWFMSMeRDow5KcbLSE",
  ERC20: "******************************************",
  BEP20: "******************************************",
};

type InvestmentPaymentSummaryProps = {
  newInvestment: InvestmentFormType;
};

export function InvestmentPaymentSummary({
  newInvestment,
}: InvestmentPaymentSummaryProps) {
  const { toast } = useToast();
  const network = "TRC20";

  const [errors, setErrors] = useState<FormErrors>({});

  const [copiedAddress, setCopiedAddress] = useState(false);

  const multiSetup = newInvestment.beneficiaries;

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
    }).format(amount);
  };

  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text);
      setCopiedAddress(true);
      toast({
        title: "Address Copied",
        description: "Wallet address has been copied to clipboard",
      });
      setTimeout(() => setCopiedAddress(false), 2000);
    } catch (err) {
      toast({
        title: "Copy Failed",
        description: "Failed to copy address to clipboard",
        variant: "destructive",
      });
    }
  };

  const getMultiSetupTotal = () => {
    return multiSetup?.reduce(
      (total, beneficiary) => total + Number(beneficiary.amount || 0),
      0
    );
  };

  const buttonText = (
    <>
      {copiedAddress ? (
        <Check className="w-4 h-4" />
      ) : (
        <Copy className="w-4 h-4" />
      )}
      <span className="hidden md:inline">
        {copiedAddress ? "Copied" : "Copy"}
      </span>
    </>
  );
  const alertMessage = (
    <>
      <strong>Important:</strong> Send exactly{" "}
      {formatCurrency(getMultiSetupTotal())} USDT to the address above. Sending
      a different amount may delay processing.
    </>
  );

  const wallet = {
    address: WALLET_ADDRESSES[network as keyof typeof WALLET_ADDRESSES],
    network,
  };

  return (
    <div className="grid gap-4 md:gap-6 py-2 md:py-4">
      {errors.general && (
        <Alert className="border-red-200 bg-red-50">
          <AlertCircle className="h-4 w-4 text-red-600" />
          <AlertDescription className="text-red-800 text-sm">
            {errors.general}
          </AlertDescription>
        </Alert>
      )}

      {/* Investment Summary */}
      <InvestmentFormSummary investment={newInvestment} />

      {/* Payment Interface */}
      <div className="space-y-4">
        <div className="text-center">
          <h3 className="text-base md:text-lg font-semibold mb-2">
            Send USDT to Complete Payment
          </h3>
          <p className="text-sm md:text-base text-gray-600 dark:text-gray-400 mb-4">
            Send exactly {formatCurrency(getMultiSetupTotal())} USDT to the
            address below
          </p>
        </div>

        <WalletAddressDisplay
          wallet={wallet}
          onClick={() =>
            copyToClipboard(
              WALLET_ADDRESSES[network as keyof typeof WALLET_ADDRESSES]
            )
          }
          buttonText={buttonText}
          alertMessage={alertMessage}
        />
      </div>
    </div>
  );
}
