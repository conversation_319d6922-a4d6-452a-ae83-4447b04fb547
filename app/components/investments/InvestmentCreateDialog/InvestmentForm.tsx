"use client";

import { Alert, AlertDescription } from "@/components/ui/alert";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

import {
  validateAmount,
  validateBeneficiaryRate,
  validateInvestmentName,
} from "@/lib/utils/formValidation";
import { AlertCircle, CheckCircle, Plus, X } from "lucide-react";
import { useImperativeHandle, useState } from "react";
import { InvestmentFormSummary } from "./InvestmentFormSummary";
import { useAdmin } from "@/contexts/admin-provider";

interface FormErrors {
  name?: string;
  email?: string;
  investmentName?: string;
  amount?: string;
  beneficiaryRate?: string;
  walletAddress?: string;
  verificationCode?: string;
  cardNumber?: string;
  expiryDate?: string;
  cvv?: string;
  cardName?: string;
  general?: string;
}

export interface MultiBeneficiary {
  id: string;
  type: "self" | "friend";
  beneficiaryId?: string;
  beneficiaryName: string;
  amount: string;
  percentage: string; // Only for friends
}

export interface newInvestmentProps {
  name: string;
  amount: string;
  duration: string;
  network: string;
  isForSelf: boolean;
  beneficiaryId: string;
  beneficiaryRate: string;
}

export type InvestmentFormType = {
  name: string;
  duration: string;
  beneficiaries: MultiBeneficiary[];
};

type InvestmentFormRefProps = {
  investmentData: () => InvestmentFormType | undefined;
  reset: () => void;
  validate: () => boolean;
};

export const defaultNewInvestment: InvestmentFormType = {
  name: "",
  duration: "",
  beneficiaries: [],
};

type InvestmentFormProps = {
  onChange?: (data: any) => void;
  ref: React.Ref<InvestmentFormRefProps>;
  initialInvestment?: InvestmentFormType;
};

export const InvestmentForm = ({
  onChange,
  ref,
  initialInvestment,
}: InvestmentFormProps) => {
  const [errors, setErrors] = useState<FormErrors>({});
  const [touched, setTouched] = useState<{ [key: string]: boolean }>({});
  const [newInvestment, setNewInvestment] = useState(
    initialInvestment || defaultNewInvestment
  );
  const [multiSetup, setMultiSetup] = useState<MultiBeneficiary[]>(
    initialInvestment?.beneficiaries || []
  );

  const isValid = (newErrors: FormErrors, multiSetup: MultiBeneficiary[]) => {
    return Object.keys(newErrors).length === 0 && multiSetup.length > 0;
  };

  const newInvestmentData = {
    beneficiaries: multiSetup,
    name: newInvestment.name,
    duration: newInvestment.duration,
  };

  const validateForm = () => {
    const newErrors: FormErrors = {};

    // Validate investment name
    if (!newInvestment.name.trim()) {
      newErrors.investmentName = "Investment name is required";
    } else {
      const nameError = validateInvestmentName(newInvestment.name);
      if (nameError) {
        newErrors.investmentName = nameError;
      }
    }

    // Validate duration
    if (!newInvestment.duration) {
      newErrors.general = "Duration is required";
    }

    // Validate beneficiaries
    if (multiSetup.length === 0) {
      newErrors.general = "At least one beneficiary is required";
    }

    setErrors(newErrors);
    setTouched({ investmentName: true, duration: true });

    return Object.keys(newErrors).length === 0;
  };

  const reset = () => {
    setNewInvestment({
      name: "",
      duration: "",
      beneficiaries: [],
    });
    setErrors({});
    setMultiSetup([]);
  };

  const handleInvestmentInputChange = (field: string, value: string) => {
    if (field === "name" || field === "amount" || field === "beneficiaryRate") {
      const _newInvestment = { ...newInvestment, [field]: value };
      setNewInvestment(_newInvestment);
    }

    // Clear errors when user starts typing
    const errorKey = field === "name" ? "investmentName" : field;
    if (errors[errorKey as keyof FormErrors]) {
      setErrors((prev) => ({ ...prev, [errorKey]: undefined }));
    }

    // Real-time validation for touched fields
    const newErrors = { ...errors };
    if (touched[errorKey]) {
      if (field === "name") {
        const nameError = validateInvestmentName(value);
        if (nameError) {
          newErrors.investmentName = nameError;
        } else {
          delete newErrors.investmentName;
        }
      } else if (field === "amount") {
        const amountError = validateAmount(value);
        if (amountError) {
          newErrors.amount = amountError;
        } else {
          delete newErrors.amount;
        }
      } else if (field === "beneficiaryRate") {
        const rateError = validateBeneficiaryRate(value);
        if (rateError) {
          newErrors.beneficiaryRate = rateError;
        } else {
          delete newErrors.beneficiaryRate;
        }
      }
      setErrors(newErrors);
    }
    if (onChange) {
      // onChange(Object.keys(newErrors).length === 0 && multiSetup.length > 0);
      onChange(isValid(newErrors, multiSetup));
    }
  };

  const handleBlur = (field: string) => {
    setTouched((prev) => ({ ...prev, [field]: true }));

    const beneficiaryRate = newInvestment.beneficiaries
      .filter((b) => {
        return b.type === "friend";
      })
      .map((b) => {
        return b.percentage;
      });

    // Validate field on blur
    const newErrors = { ...errors };

    if (field === "investmentName") {
      const nameError = validateInvestmentName(newInvestment.name);
      if (nameError) {
        newErrors.investmentName = nameError;
      } else {
        delete newErrors.investmentName;
      }
    }

    setErrors(newErrors);
  };

  useImperativeHandle(ref, () => ({
    investmentData: () => {
      if (isValid(errors, multiSetup)) {
        return newInvestmentData;
      }
      return undefined;
    },
    reset,
    validate: validateForm,
  }));

  return (
    <div className="flex-1 overflow-y-auto px-4 pb-4">
      <div className="grid gap-4 md:gap-6 py-2 md:py-4">
        {errors.general && (
          <Alert className="border-red-200 bg-red-50">
            <AlertCircle className="h-4 w-4 text-red-600" />
            <AlertDescription className="text-red-800 text-sm">
              {errors.general}
            </AlertDescription>
          </Alert>
        )}
        {/* Amount and Duration - Side by side for all single investment types */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {/* Investment Name - Always show */}
          <div className="space-y-2">
            <Label htmlFor="investment-name" className="text-sm md:text-base">
              Investment Name
            </Label>
            <div className="relative">
              <Input
                id="investment-name"
                placeholder="e.g., 6-Month USDT Investment"
                value={newInvestment.name}
                onChange={(e) =>
                  handleInvestmentInputChange("name", e.target.value)
                }
                onBlur={() => handleBlur("investmentName")}
                className={`pr-10 h-11 md:h-10 ${
                  errors.investmentName && touched.investmentName
                    ? "border-red-500 focus:ring-red-500 focus:border-red-500"
                    : touched.investmentName &&
                      !errors.investmentName &&
                      newInvestment.name.trim()
                    ? "border-green-500 focus:ring-green-500 focus:border-green-500"
                    : ""
                }`}
              />
              {touched.investmentName &&
                !errors.investmentName &&
                newInvestment.name.trim() && (
                  <CheckCircle className="absolute right-3 top-3 h-4 w-4 text-green-500" />
                )}
            </div>
            {errors.investmentName && touched.investmentName && (
              <p className="text-sm text-red-600 flex items-center gap-1">
                <AlertCircle className="w-3 h-3" />
                {errors.investmentName}
              </p>
            )}
          </div>
          <SelectFormField
            label="Duration"
            value={newInvestment.duration}
            placeholder="Duration"
            onChange={(value) => {
              setNewInvestment({ ...newInvestment, duration: value });
              // Clear duration error when user selects a value
              if (errors.general && errors.general.includes("Duration")) {
                setErrors((prev) => ({ ...prev, general: undefined }));
              }
            }}
            options={[
              { label: "3 Months", value: "3" },
              { label: "6 Months", value: "6" },
              { label: "9 Months", value: "9" },
              { label: "12 Months", value: "12" },
            ]}
            error={
              touched.duration && !newInvestment.duration
                ? "Duration is required"
                : undefined
            }
          />
        </div>

        {/* Multi-Setup Interface */}
        <AddBeneficiaryFormField
          initialMultiSetup={initialInvestment?.beneficiaries ?? []}
          onChange={(beneficiaries) => {
            setMultiSetup(beneficiaries);
            onChange?.(
              Object.keys(errors).length === 0 && beneficiaries.length > 0
            );
          }}
        />

        {/* Investment Summary */}
        {multiSetup.length > 0 && newInvestment.duration && (
          <InvestmentFormSummary investment={newInvestmentData} detailed />
        )}
      </div>
    </div>
  );
};

type AddBeneficiaryFormFieldProps = {
  onChange: (beneficiaries: MultiBeneficiary[]) => void;
  initialMultiSetup?: MultiBeneficiary[];
};

const AddBeneficiaryFormField = ({
  onChange,
  initialMultiSetup,
}: AddBeneficiaryFormFieldProps) => {
  const { circleMembers: initialCircleMembers, user } = useAdmin();
  const circleMembers = initialCircleMembers.map((member) => ({
    ...member,
    name: member.name || member.email,
  }));
  const [currentMultiBeneficiary, setCurrentMultiBeneficiary] =
    useState<MultiBeneficiary>({
      id: "",
      type: "self",
      beneficiaryId: "",
      beneficiaryName: "",
      amount: "",
      percentage: "10",
    });
  const [multiSetup, setMultiSetup] = useState<MultiBeneficiary[]>(
    initialMultiSetup || []
  );
  const [errors, setErrors] = useState<FormErrors>({});

  const addMultiBeneficiary = () => {
    const newBeneficiary: MultiBeneficiary = {
      id: `multi_${Date.now()}`,
      type: currentMultiBeneficiary.type,
      beneficiaryId:
        currentMultiBeneficiary.type === "friend"
          ? currentMultiBeneficiary.beneficiaryId
          : user?.id,
      beneficiaryName:
        currentMultiBeneficiary.type === "self"
          ? "You"
          : circleMembers.find(
              (m) => m.id === currentMultiBeneficiary.beneficiaryId
            )?.name || "",
      amount: currentMultiBeneficiary.amount,
      percentage:
        currentMultiBeneficiary.type === "self"
          ? "10"
          : currentMultiBeneficiary.percentage,
    };

    if (
      !newBeneficiary.amount ||
      Number.parseFloat(newBeneficiary.amount) < 100
    ) {
      setErrors({ general: "Amount must be at least $100 USDT" });
      return;
    }

    if (newBeneficiary.type === "friend" && !newBeneficiary.beneficiaryId) {
      setErrors({ general: "Please select a beneficiary" });
      return;
    }

    // compute the new array first
    let updated: MultiBeneficiary[];
    if (newBeneficiary.type === "self") {
      updated = [
        ...multiSetup.filter((b) => b.type !== "self"),
        newBeneficiary,
      ];
    } else {
      updated = [...multiSetup, newBeneficiary];
    }

    // update local state
    setMultiSetup(updated);

    // now safely notify parent
    onChange(updated);

    // reset form
    setCurrentMultiBeneficiary({
      id: "",
      type: "self",
      beneficiaryId: "",
      beneficiaryName: "",
      amount: "",
      percentage: "10",
    });

    setErrors({});
  };

  const removeMultiBeneficiary = (id: string) => {
    setMultiSetup((prev) => prev.filter((b) => b.id !== id));
    onChange(multiSetup.filter((b) => b.id !== id));
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
    }).format(amount);
  };

  const getMultiSetupTotal = () => {
    return multiSetup.reduce(
      (total, beneficiary) =>
        total + Number.parseFloat(beneficiary.amount || "0"),
      0
    );
  };

  return (
    <>
      <div className="space-y-4 p-4 border rounded-lg bg-gray-50 dark:bg-gray-800">
        <h4 className="font-medium text-sm md:text-base">Add Beneficiaries</h4>

        {/* Current Beneficiary Form */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label className="text-sm">Beneficiary Type</Label>
            <div className="grid grid-cols-2 gap-2">
              <div
                className={`p-2 border rounded cursor-pointer text-center text-sm ${
                  currentMultiBeneficiary.type === "self"
                    ? "border-[#245c1a] bg-[#245c1a]/5"
                    : "border-gray-200"
                }`}
                onClick={() =>
                  setCurrentMultiBeneficiary({
                    ...currentMultiBeneficiary,
                    type: "self",
                    beneficiaryId: user?.id!,
                    beneficiaryName: "You",
                    percentage: "10",
                  })
                }
              >
                Myself
              </div>
              <div
                className={`p-2 border rounded cursor-pointer text-center text-sm ${
                  currentMultiBeneficiary.type === "friend"
                    ? "border-[#245c1a] bg-[#245c1a]/5"
                    : "border-gray-200"
                }`}
                onClick={() =>
                  setCurrentMultiBeneficiary({
                    ...currentMultiBeneficiary,
                    type: "friend",
                    beneficiaryId: "",
                    beneficiaryName: "",
                    percentage: "5",
                  })
                }
              >
                Friend
              </div>
            </div>
          </div>

          <div className="space-y-2">
            <Label className="text-sm">Amount (USDT)</Label>
            <Input
              type="number"
              placeholder="100"
              min="100"
              value={currentMultiBeneficiary.amount}
              onChange={(e) =>
                setCurrentMultiBeneficiary({
                  ...currentMultiBeneficiary,
                  amount: e.target.value,
                })
              }
              className="h-10"
            />
          </div>
        </div>

        {currentMultiBeneficiary.type === "friend" && (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <SelectFormField
              label="Select Friend"
              options={circleMembers.map((member) => ({
                value: member.id,
                label: member.name,
              }))}
              value={currentMultiBeneficiary.beneficiaryId || ""}
              onChange={(value) => {
                const member = circleMembers.find((m) => m.id === value);
                setCurrentMultiBeneficiary({
                  ...currentMultiBeneficiary,
                  beneficiaryId: value,
                  beneficiaryName: member?.name || "",
                });
              }}
              placeholder="Choose friend"
            />

            <div className="space-y-2">
              <Label className="text-sm">Profit Rate (%)</Label>
              <Input
                type="number"
                min="1"
                max="10"
                step="0.1"
                placeholder="5"
                value={currentMultiBeneficiary.percentage}
                onChange={(e) =>
                  setCurrentMultiBeneficiary({
                    ...currentMultiBeneficiary,
                    percentage: e.target.value,
                  })
                }
                className="h-10"
              />
            </div>
          </div>
        )}

        <Button
          onClick={addMultiBeneficiary}
          variant="outline"
          className="w-full h-10 bg-transparent"
          disabled={
            !currentMultiBeneficiary.amount ||
            Number.parseFloat(currentMultiBeneficiary.amount) < 100 ||
            (currentMultiBeneficiary.type === "friend" &&
              !currentMultiBeneficiary.beneficiaryId)
          }
        >
          <Plus className="w-4 h-4 mr-2" />
          Add Beneficiary
        </Button>

        {/* Added Beneficiaries List */}
        {multiSetup.length > 0 && (
          <div className="space-y-2">
            <Label className="text-sm font-medium">
              Added Beneficiaries ({multiSetup.length})
            </Label>
            <div className="space-y-2 max-h-40 overflow-y-auto">
              {multiSetup.map((beneficiary) => (
                <div
                  key={beneficiary.id}
                  className="flex items-center justify-between p-3 bg-white dark:bg-gray-700 rounded border"
                >
                  <div className="flex-1">
                    <div className="flex items-center gap-2">
                      <span className="font-medium text-sm">
                        {beneficiary.beneficiaryName}
                      </span>
                      <Badge variant="outline" className="text-xs">
                        {beneficiary.type === "self" ? "You" : "Friend"}
                      </Badge>
                    </div>
                    <div className="text-xs text-gray-500 mt-1">
                      {formatCurrency(Number.parseFloat(beneficiary.amount))} •{" "}
                      {beneficiary.percentage}% rate
                    </div>
                  </div>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => removeMultiBeneficiary(beneficiary.id)}
                    className="text-red-600 hover:text-red-700 h-8 w-8 p-0"
                  >
                    <X className="w-4 h-4" />
                  </Button>
                </div>
              ))}
            </div>

            <div className="p-3 bg-[#245c1a]/5 rounded border border-[#245c1a]/20">
              <div className="flex justify-between items-center">
                <span className="font-medium text-sm">
                  Total Investment Amount:
                </span>
                <span className="font-bold text-lg text-[#245c1a]">
                  {formatCurrency(getMultiSetupTotal())}
                </span>
              </div>
            </div>
          </div>
        )}
      </div>
    </>
  );
};

type SelectFormFieldProps = {
  label: string;
  options: { label: string; value: string }[];
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  error?: string;
};

const SelectFormField = ({
  label,
  options,
  value,
  onChange,
  placeholder,
  error,
}: SelectFormFieldProps) => {
  return (
    <div className="space-y-2">
      <Label
        htmlFor={label.toLocaleLowerCase()}
        className="text-sm md:text-base"
      >
        {label.charAt(0).toUpperCase() + label.slice(1)}
      </Label>
      <Select value={value} onValueChange={onChange}>
        <SelectTrigger
          className={`h-11 md:h-10 ${error ? "border-red-500" : ""}`}
        >
          {placeholder ? (
            <SelectValue placeholder={placeholder} />
          ) : (
            <SelectValue />
          )}
        </SelectTrigger>
        <SelectContent>
          {options.map((option) => (
            <SelectItem key={option.value} value={option.value}>
              {option.label}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
      {error && (
        <p className="text-sm text-red-600 flex items-center gap-1">
          <AlertCircle className="w-3 h-3" />
          {error}
        </p>
      )}
    </div>
  );
};
