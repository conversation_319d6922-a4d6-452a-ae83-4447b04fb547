"use client";

import {
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>le,
  CardDescription,
  CardContent,
} from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { Clock } from "lucide-react";

type Trade = {
  pair: string;
  type: "Buy" | "Sell";
  openedAgo: string;
  amount: string;
  value: string;
  entryPrice: string;
  takeProfit: string;
  stopLoss: string;
  pnl: string;
  pnlClassName?: string;
  progress: number;
};

type ActiveTradeCardProps = {
  title: string;
  description: string;
  trades: Trade[];
};

export default function ActiveTradeCard({
  title,
  description,
  trades,
}: ActiveTradeCardProps) {
  return (
    <Card>
      <CardHeader>
        <CardTitle>{title}</CardTitle>
        <CardDescription>{description}</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="rounded-md border">
          {trades.map((trade, i) => {
            const details = [
              { label: "Entry Price", value: trade.entryPrice },
              { label: "Take Profit", value: trade.takeProfit },
              { label: "Stop Loss", value: trade.stopLoss },
            ];

            return (
              <div
                key={i}
                className={`p-4 ${i !== trades.length - 1 ? "border-b" : ""}`}
              >
                <div className="flex justify-between items-start">
                  <div>
                    <div className="flex items-center">
                      <span className="text-sm font-medium">{trade.pair}</span>
                      <span
                        className={`ml-2 px-2 py-0.5 rounded-full text-xs ${
                          trade.type === "Buy"
                            ? "bg-green-100 text-green-800"
                            : "bg-red-100 text-red-800"
                        }`}
                      >
                        {trade.type}
                      </span>
                    </div>
                    <div className="mt-1 flex items-center text-xs text-gray-500">
                      <Clock className="h-3 w-3 mr-1" />
                      <span>Opened {trade.openedAgo}</span>
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="text-sm font-medium">{trade.amount}</div>
                    <div className="text-xs text-gray-500">{trade.value}</div>
                  </div>
                </div>

                <div className="mt-3 grid grid-cols-3 gap-2 text-xs">
                  {details.map((item, idx) => (
                    <div key={idx}>
                      <p className="text-gray-500">{item.label}</p>
                      <p className="font-medium">{item.value}</p>
                    </div>
                  ))}
                </div>

                <div className="mt-3">
                  <div className="flex justify-between text-xs mb-1">
                    <span className="text-gray-500">Current P/L</span>
                    <span className={trade.pnlClassName}>{trade.pnl}</span>
                  </div>
                  <Progress
                    value={trade.progress}
                    className="h-1.5 bg-gray-100"
                  />
                </div>
              </div>
            );
          })}
        </div>
      </CardContent>
    </Card>
  );
}
