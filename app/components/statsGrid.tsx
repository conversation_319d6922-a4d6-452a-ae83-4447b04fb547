"use client";

import { Card, CardContent } from "@/components/ui/card";

type Stat = {
  label: string;
  value: string | number;
  color?: string;
};

type StatsGridProps = {
  stats: Stat[];
  columns?: number;
};

const colClasses: Record<number, string> = {
  1: "md:grid-cols-1",
  2: "md:grid-cols-2",
  3: "md:grid-cols-3",
  4: "md:grid-cols-4",
  5: "md:grid-cols-5",
  6: "md:grid-cols-6",
  7: "md:grid-cols-7",
  8: "md:grid-cols-8",
};

export default function StatsGrid({ stats, columns = 4 }: StatsGridProps) {
  return (
    // <div className={`grid grid-cols-1 md:grid-cols-${columns} gap-4`}>
    //   {stats.map((stat, i) => (
    //     <Card key={i}>
    //       <CardContent className="p-4">
    //         <div className="text-sm text-gray-600">{stat.label}</div>
    //         <div className={`text-2xl font-bold ${stat.color || "text-gray-900"}`}>
    //           {stat.value}
    //         </div>
    //       </CardContent>
    //     </Card>
    //   ))}
    // </div>
    <div
      className={`grid grid-cols-1 ${
        colClasses[columns] || "md:grid-cols-4"
      } gap-4`}
    >
      {stats.map((stat, i) => (
        <Card key={i}>
          <CardContent className="p-4">
            <div className="text-sm text-gray-600">{stat.label}</div>
            <div
              className={`text-2xl font-bold ${stat.color || "text-gray-900"}`}
            >
              {stat.value}
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
}
