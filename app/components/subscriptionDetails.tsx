"use client";

import { Progress } from "@/components/ui/progress";

type SubscriptionDetailsProps = {
  plan: string;
  monthlyFee: string;
  renewalDate: string;
  monthlyVolume: string;
  volumeLimit: string;
};

export default function SubscriptionDetails({
  plan,
  monthlyFee,
  renewalDate,
  monthlyVolume,
  volumeLimit,
}: SubscriptionDetailsProps) {
  const volumeUsedPercent =
    (Number.parseInt(monthlyVolume.replace(/[^0-9.-]+/g, "")) /
      Number.parseInt(volumeLimit.replace(/[^0-9.-]+/g, ""))) *
    100;

  const details = [
    { label: "Monthly Fee", value: monthlyFee },
    { label: "Renewal Date", value: renewalDate },
    { label: "Trading Volume Limit", value: volumeLimit },
  ];

  return (
    <div className="rounded-lg border p-4">
      <h3 className="font-medium mb-3">Current Plan: {plan}</h3>

      <div className="space-y-2 mb-4">
        {details.map((item, idx) => (
          <div key={idx} className="flex justify-between text-sm">
            <span className="text-gray-500">{item.label}:</span>
            <span className="font-medium">{item.value}</span>
          </div>
        ))}
      </div>

      <div>
        <div className="flex justify-between text-sm mb-1">
          <span className="text-gray-500">Volume Used:</span>
          <span className="font-medium">{Math.round(volumeUsedPercent)}%</span>
        </div>
        <Progress value={volumeUsedPercent} className="h-2" />
      </div>
    </div>
  );
}
