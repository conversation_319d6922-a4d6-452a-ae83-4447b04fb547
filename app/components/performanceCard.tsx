"use client";

import { <PERSON>, <PERSON><PERSON><PERSON>er, <PERSON>Title, CardDescription, CardContent } from "@/components/ui/card";

type StatItem = {
  label: string;
  value: string | number;
  valueClassName?: string;
};

type PerformanceCardProps = {
  title: string;
  description: string;
  chart?: React.ReactNode;
  stats: StatItem[];
};

export default function PerformanceCard({ title, description, chart, stats }: PerformanceCardProps) {
  return (
    <Card>
      <CardHeader>
        <CardTitle>{title}</CardTitle>
        <CardDescription>{description}</CardDescription>
      </CardHeader>
      <CardContent>
        {chart || (
          <div className="h-[200px] flex items-center justify-center bg-gray-50 rounded-md border">
            <p className="text-gray-500">Chart goes here</p>
          </div>
        )}
        <div className="grid grid-cols-2 gap-4 mt-4">
          {stats.map((stat, i) => (
            <div key={i}>
              <p className="text-sm text-gray-500">{stat.label}</p>
              <p className={`text-lg font-medium ${stat.valueClassName ?? ""}`}>{stat.value}</p>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
}
