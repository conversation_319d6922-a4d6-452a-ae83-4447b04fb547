"use client";

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardDescription, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import Link from "next/link";
import { LucideIcon } from "lucide-react";

type ConfigItem = {
  icon: LucideIcon;
  label: string;
  value: string | number;
  hint?: string;
};

type ConfigCardProps = {
  title: string;
  description?: string;
  items: ConfigItem[];
  columns?: number;
  action?: {
    label: string;
    href: string;
  };
};

export default function ConfigCard({ title, description, items, columns = 2, action }: ConfigCardProps) {
  return (
    <Card>
      <CardHeader>
        <CardTitle>{title}</CardTitle>
        {description && <CardDescription>{description}</CardDescription>}
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div className={`grid grid-cols-1 md:grid-cols-${columns} gap-4`}>
            {items.map((item, i) => (
              <div key={i} className="rounded-lg border p-4">
                <div className="flex items-center gap-2 mb-2">
                  <item.icon className="h-4 w-4 text-[#245c1a]" />
                  <h3 className="font-medium">{item.label}</h3>
                </div>
                <p className="text-gray-500">{item.value}</p>
                {item.hint && <p className="text-xs text-gray-500 mt-2">{item.hint}</p>}
              </div>
            ))}
          </div>

          {action && (
            <div className="flex justify-end">
              <Link href={action.href}>
                <Button className="bg-[#245c1a] hover:bg-[#1a4513]">
                  {action.label}
                </Button>
              </Link>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
