"use client";

import { AlertCircle, Plus } from "lucide-react";

import { <PERSON>, Card<PERSON>eader, CardT<PERSON>le, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";

type ColumnType = "text" | "badge" | "currency" | "progress";

export type Column = {
  label: string;
  key: string;
  type?: ColumnType;
  badgeColors?: Record<string, string>;
  formtatter?: (value: any) => string;
};

type TableCardProps<T> = {
  data: T[];
  description?: string;
  columns: Column[];
  title?: string;
  accountId?: string;
  daysRemaining?: number;
  emptyMessage?: string;
  action?: {
    label?: string;
    onClick?: (accountId?: string) => void;
  };
};

const formatCurrency = (value: number) =>
  new Intl.NumberFormat("en-US", {
    style: "currency",
    currency: "USD",
    minimumFractionDigits: 2,
  }).format(value);

export default function TableCard<T>({
  data,
  description,
  columns,
  title = "Table",
  accountId,
  daysRemaining,
  emptyMessage = "No data available",
  action,
}: TableCardProps<T>) {
  return (
    <Card>
      <CardHeader>
        <div className="flex justify-between items-center">
          <div className="flex flex-col gap-1">
            <CardTitle>{title}</CardTitle>
            {description && (
              <p className="text-sm text-gray-500">{description}</p>
            )}
          </div>

          {action && daysRemaining !== undefined && daysRemaining > 0 && (
            <Button
              onClick={() => action.onClick?.(accountId)}
              className="bg-[#245c1a] hover:bg-[#1a4513]"
            >
              <Plus className="mr-2 h-4 w-4" />
              {action.label}
            </Button>
          )}
        </div>
      </CardHeader>

      <CardContent>
        {data.length > 0 ? (
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b">
                  {columns.map((col) => (
                    <th
                      key={String(col.key)}
                      className="px-4 py-3 text-left  text-sm font-medium"
                    >
                      {col.label}
                    </th>
                  ))}
                </tr>
              </thead>
              <tbody className="divide-y">
                {data.map((row, rowIndex) => (
                  <tr
                    key={rowIndex}
                    className="hover:bg-gray-50 dark:hover:bg-gray-800"
                  >
                    {columns.map((col) => {
                      const value = (row as Record<string, any>)[col.key];
                      let content: React.ReactNode = value as any;

                      if (
                        col.type === "currency" &&
                        typeof value === "number"
                      ) {
                        content = formatCurrency(value);
                      } else if (col.formtatter) {
                        content = col.formtatter(value);
                      } else if (col.type === "badge") {
                        content = (
                          <Badge
                            variant="default"
                            className={col.badgeColors?.[value]}
                          >
                            {String(value)}
                          </Badge>
                        );
                      } else if (
                        col.type === "progress" &&
                        typeof value === "number"
                      ) {
                        content = (
                          <div className="space-y-1">
                            <Progress value={value} className="h-2" />
                            <div className="text-xs text-gray-500">
                              {value.toFixed(1)}%
                            </div>
                          </div>
                        );
                      }

                      return (
                        <td
                          key={String(col.key)}
                          className="px-4 py-3 text-sm text-gray-700 dark:text-gray-300"
                        >
                          {content}
                        </td>
                      );
                    })}
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        ) : (
          <div className="p-8 text-center text-gray-500">
            <AlertCircle className="mx-auto mb-4 h-12 w-12 text-gray-300" />
            <p className="mb-4">{emptyMessage}</p>
            {action && daysRemaining !== undefined && daysRemaining > 0 && (
              <Button
                onClick={() => action?.onClick?.(accountId)}
                className="bg-[#245c1a] hover:bg-[#1a4513]"
              >
                {action.label}
              </Button>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  );
}
