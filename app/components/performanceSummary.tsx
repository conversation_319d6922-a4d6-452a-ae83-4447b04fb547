"use client";

import {
  <PERSON>,
  CardHeader,
  CardTitle,
  CardDescription,
  CardContent,
} from "@/components/ui/card";
import { TrendingUp } from "lucide-react";

type PerformanceCardProps = {
  title?: string;
  description?: string;
  totalReturn: number; // e.g. percentage gain/loss
  daysActive: number;
  avgDailyProfit: number;
};

export function PerformanceSummary({
  title = "Performance Summary",
  description = "Your trading performance overview",
  totalReturn,
  daysActive,
  avgDailyProfit,
}: PerformanceCardProps) {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <TrendingUp className="w-5 h-5" />
          {title}
        </CardTitle>
        {description && <CardDescription>{description}</CardDescription>}
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            {/* Total Return */}
            <div className="text-center p-3 bg-green-50 rounded-lg">
              <div className="text-2xl font-bold text-green-600">
                {totalReturn > 0 ? "+" : ""}
                {totalReturn.toFixed(1)}%
              </div>
              <div className="text-sm text-gray-600">Total Return</div>
            </div>

            {/* Days Active */}
            <div className="text-center p-3 bg-blue-50 rounded-lg">
              <div className="text-2xl font-bold text-blue-600">
                {daysActive}
              </div>
              <div className="text-sm text-gray-600">Days Active</div>
            </div>
          </div>

          {/* Average Daily Profit */}
          <div className="text-center p-3 bg-gray-50 rounded-lg">
            <div className="text-lg font-bold text-gray-800">
              ${avgDailyProfit.toFixed(2)}
            </div>
            <div className="text-sm text-gray-600">Average Daily Profit</div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
