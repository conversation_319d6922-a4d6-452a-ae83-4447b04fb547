'use client'

import { useState, useTransition } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { Network, Check, CheckCircle, Globe, Shield } from "lucide-react";
import { toast } from "sonner";
import { assignProxyToAccountAction } from "../actions";

interface AccountProxyStepProps {
  onStepComplete: () => void;
  proxies: Array<{
    id: string;
    ip_address: string;
    type: "http" | "socks5";
    created: string;
    updated: string;
    user: string;
  }>;
  account: {
    id: string;
    exchange: string;
    owner: string;
    email: string;
    created: string;
    updated: string;
    proxy?: string;
  };
}

export function AccountProxyStep({ onStepComplete, proxies, account }: AccountProxyStepProps) {
  const [selectedProxyId, setSelectedProxyId] = useState<string>(account.proxy || (proxies.length > 0 ? proxies[0].id : ""));
  const [isPending, startTransition] = useTransition();

  // If account already has a proxy assigned, show completed state
  if (account.proxy && !selectedProxyId) {
    const assignedProxy = proxies.find(p => p.id === account.proxy);
    
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <CheckCircle className="h-5 w-5 text-green-600" />
            Proxy Configured
          </CardTitle>
          <CardDescription>
            Your account is already configured with a proxy connection.
          </CardDescription>
        </CardHeader>
        <CardContent>
          {assignedProxy && (
            <div className="flex items-center gap-2 p-3 bg-green-50 rounded-lg mb-4">
              <Network className="h-4 w-4 text-green-600" />
              <div className="flex-1">
                <div className="font-medium text-green-900">
                  {assignedProxy.ip_address.split('@')[1] || assignedProxy.ip_address}
                </div>
                <div className="text-sm text-green-700">
                  {assignedProxy.type.toUpperCase()} Proxy
                </div>
              </div>
              <Badge variant="secondary" className="bg-green-100 text-green-800">
                Active
              </Badge>
            </div>
          )}
          <Button onClick={onStepComplete} className="w-full">
            Continue to Next Step
          </Button>
        </CardContent>
      </Card>
    );
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!selectedProxyId) {
      toast.error("Please select a proxy");
      return;
    }

    startTransition(async () => {
      try {
        const result = await assignProxyToAccountAction({
          accountId: account.id,
          proxyId: selectedProxyId,
        });

        if (result.success) {
          toast.success("Proxy assigned successfully");
          onStepComplete();
        } else {
          toast.error(result.error || "Failed to assign proxy");
        }
      } catch (error) {
        console.error("Proxy assignment error:", error);
        toast.error("An error occurred while assigning the proxy");
      }
    });
  };

  if (proxies.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Network className="h-5 w-5 text-blue-600" />
            Proxy Setup Required
          </CardTitle>
          <CardDescription>
            You need to set up a proxy before configuring this account.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="text-center py-6">
            <Globe className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="font-medium text-gray-900 mb-2">No Proxies Available</h3>
            <p className="text-sm text-gray-500 mb-4">
              You need to set up at least one proxy to continue with account setup.
            </p>
            <Button 
              onClick={() => window.open('/onboarding', '_blank')}
              variant="outline"
            >
              Set Up Proxy in Onboarding
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Network className="h-5 w-5 text-blue-600" />
          Select Proxy
        </CardTitle>
        <CardDescription>
          Choose a proxy connection for your {account.owner} account on {account.exchange}.
        </CardDescription>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-3">
            <Label className="text-sm font-medium">Available Proxies</Label>
            <RadioGroup
              value={selectedProxyId}
              onValueChange={setSelectedProxyId}
              className="space-y-2"
            >
              {proxies.map((proxy) => (
                <div key={proxy.id} className="flex items-center space-x-3 p-3 border rounded-lg hover:bg-gray-50">
                  <RadioGroupItem value={proxy.id} id={proxy.id} />
                  <Label htmlFor={proxy.id} className="flex-1 cursor-pointer">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <Shield className="h-4 w-4 text-gray-500" />
                        <div>
                          <div className="font-medium">
                            {proxy.ip_address.split('@')[1] || proxy.ip_address}
                          </div>
                          <div className="text-sm text-gray-500">
                            {proxy.type.toUpperCase()} Proxy
                          </div>
                        </div>
                      </div>
                      <Badge variant="outline">
                        {proxy.type}
                      </Badge>
                    </div>
                  </Label>
                </div>
              ))}
            </RadioGroup>
          </div>

          <div className="bg-blue-50 p-4 rounded-lg">
            <h4 className="font-medium text-blue-900 mb-2">Why use a proxy?</h4>
            <ul className="text-sm text-blue-800 space-y-1">
              <li>• Enhanced security and privacy</li>
              <li>• Bypass geographical restrictions</li>
              <li>• Improved connection stability</li>
            </ul>
          </div>

          <Button 
            type="submit" 
            className="w-full" 
            disabled={isPending || !selectedProxyId}
          >
            {isPending ? (
              <>
                <div className="h-4 w-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2" />
                Assigning Proxy...
              </>
            ) : (
              <>
                <Check className="h-4 w-4 mr-2" />
                Assign Proxy
              </>
            )}
          </Button>
        </form>
      </CardContent>
    </Card>
  );
}
