'use client'

import { useState, useTransition } from "react";
import { useRouter } from "next/navigation";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON><PERSON>2, <PERSON><PERSON><PERSON><PERSON>, Rocket } from "lucide-react";
import { toast } from "sonner";
import { completeAccountSetupAction } from "../actions";

interface AccountActivationStepProps {
  user: {
    id: string;
    email: string;
    emailVisibility: boolean;
    verified: boolean;
    name: string;
    avatar: string;
    settings: any;
    createdAt: string;
    updatedAt: string;
  };
  account: {
    id: string;
    exchange: string;
    owner: string;
    email: string;
    created: string;
    updated: string;
    proxy?: string;
  };
}

export function AccountActivationStep({ user, account }: AccountActivationStepProps) {
  const [isPending, startTransition] = useTransition();
  const router = useRouter();

  const handleActivateAccount = async () => {
    startTransition(async () => {
      try {
        const result = await completeAccountSetupAction({
          accountId: account.id,
        });

        if (result.success) {
          toast.success("Account setup completed successfully!");
          
          // Redirect back to accounts page
          setTimeout(() => {
            router.push('/accounts');
          }, 1000);
        } else {
          toast.error(result.error || "Failed to complete account setup");
        }
      } catch (error) {
        console.error("Account activation error:", error);
        toast.error("An error occurred while completing the account setup");
      }
    });
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Rocket className="h-5 w-5 text-green-600" />
          Activate Account
        </CardTitle>
        <CardDescription>
          Complete the setup for your {account.owner} trading account.
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-6">
          {/* Setup Summary */}
          <div className="bg-green-50 p-4 rounded-lg">
            <h3 className="font-medium text-green-900 mb-3">Setup Complete!</h3>
            <div className="space-y-2">
              <div className="flex items-center gap-2 text-green-800">
                <CheckCircle className="h-4 w-4" />
                <span className="text-sm">Secret key configured</span>
              </div>
              <div className="flex items-center gap-2 text-green-800">
                <CheckCircle className="h-4 w-4" />
                <span className="text-sm">Proxy connection established</span>
              </div>
              <div className="flex items-center gap-2 text-green-800">
                <CheckCircle className="h-4 w-4" />
                <span className="text-sm">{account.exchange.charAt(0).toUpperCase() + account.exchange.slice(1)} exchange connected</span>
              </div>
            </div>
          </div>

          {/* Account Details */}
          <div className="border rounded-lg p-4">
            <h4 className="font-medium mb-3">Account Details</h4>
            <div className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span className="text-gray-600">Account Name:</span>
                <span className="font-medium">{account.owner}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Exchange:</span>
                <span className="font-medium">{account.exchange.charAt(0).toUpperCase() + account.exchange.slice(1)}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Email:</span>
                <span className="font-medium">{account.email}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Created:</span>
                <span className="font-medium">{new Date(account.created).toLocaleDateString()}</span>
              </div>
            </div>
          </div>

          {/* Next Steps */}
          <div className="bg-blue-50 p-4 rounded-lg">
            <h4 className="font-medium text-blue-900 mb-2">What's Next?</h4>
            <ul className="text-sm text-blue-800 space-y-1">
              <li>• Your account is ready for trading</li>
              <li>• You can switch between accounts from the accounts page</li>
              <li>• Monitor your trading performance from the dashboard</li>
              <li>• Adjust settings anytime from the settings page</li>
            </ul>
          </div>

          {/* Activation Button */}
          <Button 
            onClick={handleActivateAccount}
            className="w-full bg-green-600 hover:bg-green-700"
            disabled={isPending}
            size="lg"
          >
            {isPending ? (
              <>
                <div className="h-4 w-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2" />
                Activating Account...
              </>
            ) : (
              <>
                <BarChart2 className="h-4 w-4 mr-2" />
                Activate Account & Start Trading
              </>
            )}
          </Button>

          {/* Alternative Actions */}
          <div className="flex gap-2">
            <Button 
              variant="outline" 
              onClick={() => router.push('/accounts')}
              className="flex-1"
              disabled={isPending}
            >
              Back to Accounts
            </Button>
            <Button 
              variant="outline" 
              onClick={() => router.push('/dashboard')}
              className="flex-1"
              disabled={isPending}
            >
              Go to Dashboard
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
