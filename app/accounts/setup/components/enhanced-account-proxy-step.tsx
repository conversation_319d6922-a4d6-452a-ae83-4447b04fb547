'use client'

import { useState, useTransition } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Network, Check, CheckCircle, Globe, Shield, Plus, ShoppingCart } from "lucide-react";
import { toast } from "sonner";
import { assignProxyToAccountAction, createProxyForAccountAction } from "../actions";
import { useRouter } from "next/navigation";

interface EnhancedAccountProxyStepProps {
  onStepComplete: () => void;
  proxies: Array<{
    id: string;
    ip_address: string;
    type: "http" | "socks5";
    created: string;
    updated: string;
    user: string;
  }>;
  account: {
    id: string;
    exchange: string;
    owner: string;
    email: string;
    created: string;
    updated: string;
    proxy?: string;
  };
}

export function EnhancedAccountProxyStep({ onStepComplete, proxies, account }: EnhancedAccountProxyStepProps) {
  const router = useRouter();
  const [selectedProxyId, setSelectedProxyId] = useState<string>(account.proxy || (proxies.length > 0 ? proxies[0].id : ""));
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [isPending, startTransition] = useTransition();

  // Create proxy form state
  const [proxyUsername, setProxyUsername] = useState("");
  const [proxyPassword, setProxyPassword] = useState("");
  const [proxyHost, setProxyHost] = useState("");
  const [proxyPort, setProxyPort] = useState("");
  const [proxyType, setProxyType] = useState<"http" | "socks5">("http");

  // If account already has a proxy assigned, show completed state
  if (account.proxy && !selectedProxyId) {
    const assignedProxy = proxies.find(p => p.id === account.proxy);
    
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <CheckCircle className="h-5 w-5 text-green-600" />
            Proxy Configured
          </CardTitle>
          <CardDescription>
            Your account is already configured with a proxy connection.
          </CardDescription>
        </CardHeader>
        <CardContent>
          {assignedProxy && (
            <div className="flex items-center gap-2 p-3 bg-green-50 rounded-lg mb-4">
              <Network className="h-4 w-4 text-green-600" />
              <div className="flex-1">
                <div className="font-medium text-green-900">
                  {assignedProxy.ip_address.split('@')[1] || assignedProxy.ip_address}
                </div>
                <div className="text-sm text-green-700">
                  {assignedProxy.type.toUpperCase()} Proxy
                </div>
              </div>
              <Badge variant="secondary" className="bg-green-100 text-green-800">
                Active
              </Badge>
            </div>
          )}
          <Button onClick={onStepComplete} className="w-full">
            Continue to Next Step
          </Button>
        </CardContent>
      </Card>
    );
  }

  const handleCreateProxy = () => {
    if (!proxyHost || !proxyPort) {
      toast.error("Host and port are required");
      return;
    }

    startTransition(async () => {
      try {
        const result = await createProxyForAccountAction({
          username: proxyUsername || undefined,
          password: proxyPassword || undefined,
          host: proxyHost,
          port: parseInt(proxyPort),
          type: proxyType,
          accountId: account.id,
        });

        if (result.success) {
          toast.success(result.message);
          // Clear form
          setProxyUsername("");
          setProxyPassword("");
          setProxyHost("");
          setProxyPort("");
          setProxyType("http");
          // Hide create form
          setShowCreateForm(false);
          // Complete the step
          onStepComplete();
        } else {
          toast.error(result.error || "Failed to create proxy");
        }
      } catch (error) {
        console.error("Proxy creation error:", error);
        toast.error("An error occurred while creating the proxy");
      }
    });
  };

  const handleAssignExistingProxy = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!selectedProxyId) {
      toast.error("Please select a proxy");
      return;
    }

    startTransition(async () => {
      try {
        const result = await assignProxyToAccountAction({
          accountId: account.id,
          proxyId: selectedProxyId,
        });

        if (result.success) {
          toast.success("Proxy assigned successfully");
          onStepComplete();
        } else {
          toast.error(result.error || "Failed to assign proxy");
        }
      } catch (error) {
        console.error("Proxy assignment error:", error);
        toast.error("An error occurred while assigning the proxy");
      }
    });
  };

  return (
    <div className="space-y-4">
      {/* Existing Proxies */}
      {proxies.length > 0 && (
        <div className="space-y-3">
          <Label className="text-sm font-medium">Select an existing proxy:</Label>
          <RadioGroup value={selectedProxyId} onValueChange={setSelectedProxyId}>
            {proxies.map((proxy) => (
              <div key={proxy.id} className="flex items-center space-x-3">
                <RadioGroupItem value={proxy.id} id={proxy.id} />
                <Label htmlFor={proxy.id} className="flex-1 cursor-pointer">
                  <Card className={`transition-colors ${selectedProxyId === proxy.id ? 'border-[#245c1a] bg-green-50' : 'hover:bg-gray-50'}`}>
                    <CardContent className="p-4">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-3">
                          <Shield className="h-4 w-4 text-gray-500" />
                          <div>
                            <div className="font-medium">
                              {proxy.ip_address.split('@')[1] || proxy.ip_address}
                            </div>
                            <div className="text-sm text-gray-500">
                              {proxy.type.toUpperCase()} Proxy
                            </div>
                          </div>
                        </div>
                        <Badge variant="outline" className={selectedProxyId === proxy.id ? 'border-[#245c1a] text-[#245c1a]' : ''}>
                          {proxy.type}
                        </Badge>
                      </div>
                    </CardContent>
                  </Card>
                </Label>
              </div>
            ))}
          </RadioGroup>

          {selectedProxyId && (
            <Button 
              onClick={handleAssignExistingProxy} 
              className="w-full" 
              disabled={isPending}
            >
              {isPending ? (
                <>
                  <div className="h-4 w-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2" />
                  Assigning Proxy...
                </>
              ) : (
                <>
                  <Check className="h-4 w-4 mr-2" />
                  Use Selected Proxy
                </>
              )}
            </Button>
          )}
        </div>
      )}

      {/* Create New Proxy */}
      <div className="space-y-3">
        {!showCreateForm ? (
          <Button
            variant="outline"
            onClick={() => setShowCreateForm(true)}
            className="w-full border-dashed border-2 h-auto p-4 flex flex-col items-center gap-2 hover:border-[#245c1a] hover:bg-green-50"
          >
            <Plus className="h-5 w-5" />
            <span>Create New Proxy</span>
            <span className="text-xs text-muted-foreground">
              {proxies.length === 0 ? "No existing proxies found" : "Add another proxy configuration"}
            </span>
          </Button>
        ) : (
          <Card className="border-[#245c1a]">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Plus className="h-5 w-5" />
                Create New Proxy
              </CardTitle>
              <CardDescription>
                Configure a new proxy for secure trading connections
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="proxy-username">Username (Optional)</Label>
                  <Input
                    id="proxy-username"
                    placeholder="proxy_user"
                    value={proxyUsername}
                    onChange={(e) => setProxyUsername(e.target.value)}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="proxy-password">Password (Optional)</Label>
                  <Input
                    id="proxy-password"
                    type="password"
                    placeholder="proxy_pass"
                    value={proxyPassword}
                    onChange={(e) => setProxyPassword(e.target.value)}
                  />
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="proxy-host">Host *</Label>
                  <Input
                    id="proxy-host"
                    placeholder="proxy.example.com"
                    value={proxyHost}
                    onChange={(e) => setProxyHost(e.target.value)}
                    required
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="proxy-port">Port *</Label>
                  <Input
                    id="proxy-port"
                    placeholder="8080"
                    value={proxyPort}
                    onChange={(e) => setProxyPort(e.target.value)}
                    required
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="proxy-type">Proxy Type</Label>
                <Select value={proxyType} onValueChange={(value: "http" | "socks5") => setProxyType(value)}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="http">HTTP</SelectItem>
                    <SelectItem value="socks5">SOCKS5</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="flex gap-2">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => setShowCreateForm(false)}
                  className="flex-1"
                >
                  Cancel
                </Button>
                <Button
                  onClick={handleCreateProxy}
                  disabled={isPending || !proxyHost || !proxyPort}
                  className="flex-1"
                >
                  {isPending ? (
                    <>
                      <div className="h-4 w-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2" />
                      Creating...
                    </>
                  ) : (
                    <>
                      <Plus className="h-4 w-4 mr-2" />
                      Create & Use
                    </>
                  )}
                </Button>
              </div>
            </CardContent>
          </Card>
        )}
      </div>

      {/* Proxy Purchase Option */}
      {proxies.length === 0 && !showCreateForm && (
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="font-medium text-blue-800">Need a Proxy?</h3>
              <p className="text-sm text-blue-700">
                Get a reliable proxy service for secure trading connections
              </p>
            </div>
            <Button
              variant="outline"
              size="sm"
              className="border-blue-300 text-blue-700 hover:bg-blue-100"
              onClick={() => router.push(`/proxy/purchase?returnTo=/accounts/setup/${encodeURIComponent(account.owner)}`)}
            >
              <ShoppingCart className="h-4 w-4 mr-2" />
              Get Proxy
            </Button>
          </div>
        </div>
      )}
    </div>
  );
}
