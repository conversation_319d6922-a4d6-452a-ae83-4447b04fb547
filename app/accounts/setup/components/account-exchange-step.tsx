'use client'

import { useState, useTransition } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { LinkIcon, CheckCircle, Eye, EyeOff, TestTube } from "lucide-react";
import { toast } from "sonner";
import { connectAccountExchangeAction, testAccountExchangeAction } from "../actions";

interface AccountExchangeStepProps {
  onStepComplete: () => void;
  account: {
    id: string;
    exchange: string;
    owner: string;
    email: string;
    created: string;
    updated: string;
    proxy?: string;
  };
  credentials: Array<{
    name: string;
    email: string;
    exchange: string;
    api_key: string;
    api_secret: string;
  }>;
}

export function AccountExchangeStep({ onStepComplete, account, credentials }: AccountExchangeStepProps) {
  const [api<PERSON><PERSON>, setApi<PERSON>ey] = useState("");
  const [apiSecret, setApiSecret] = useState("");
  const [showApiSecret, setShowApiSecret] = useState(false);
  const [isPending, startTransition] = useTransition();
  const [isTestingConnection, setIsTestingConnection] = useState(false);

  // Check if credentials already exist for this account
  const existingCredential = credentials.find(
    cred => cred.name.toLowerCase() === account.owner.toLowerCase() && cred.exchange === account.exchange
  );

  // If credentials already exist, show completed state
  if (existingCredential && !apiKey && !apiSecret) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <CheckCircle className="h-5 w-5 text-green-600" />
            Exchange Connected
          </CardTitle>
          <CardDescription>
            Your {account.exchange} account is already connected and configured.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center gap-2 p-3 bg-green-50 rounded-lg mb-4">
            <LinkIcon className="h-4 w-4 text-green-600" />
            <div className="flex-1">
              <div className="font-medium text-green-900">
                {account.exchange.charAt(0).toUpperCase() + account.exchange.slice(1)} Connected
              </div>
              <div className="text-sm text-green-700">
                API Key: {existingCredential.api_key.substring(0, 8)}...
              </div>
            </div>
          </div>
          
          <div className="space-y-3">
            <Button
              variant="outline"
              className="w-full"
              onClick={async () => {
                setIsTestingConnection(true);
                try {
                  const result = await testAccountExchangeAction({
                    accountName: account.owner,
                    exchange: account.exchange,
                  });

                  if (result.success) {
                    toast.success("Connection test successful");
                  } else {
                    toast.error(result.error || "Connection test failed");
                  }
                } catch (error) {
                  toast.error("Failed to test connection");
                } finally {
                  setIsTestingConnection(false);
                }
              }}
              disabled={isTestingConnection}
            >
              {isTestingConnection ? (
                <>
                  <div className="h-4 w-4 border-2 border-gray-400 border-t-transparent rounded-full animate-spin mr-2" />
                  Testing Connection...
                </>
              ) : (
                <>
                  <TestTube className="h-4 w-4 mr-2" />
                  Test Connection
                </>
              )}
            </Button>
            
            <Button onClick={onStepComplete} className="w-full">
              Continue to Next Step
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!apiKey.trim()) {
      toast.error("Please enter your API key");
      return;
    }

    if (!apiSecret.trim()) {
      toast.error("Please enter your API secret");
      return;
    }

    startTransition(async () => {
      try {
        const result = await connectAccountExchangeAction({
          accountId: account.id,
          accountName: account.owner,
          exchange: account.exchange,
          apiKey: apiKey.trim(),
          apiSecret: apiSecret.trim(),
        });

        if (result.success) {
          toast.success("Exchange connected successfully");
          onStepComplete();
        } else {
          toast.error(result.error || "Failed to connect exchange");
        }
      } catch (error) {
        console.error("Exchange connection error:", error);
        toast.error("An error occurred while connecting the exchange");
      }
    });
  };

  const handleTestConnection = async () => {
    if (!apiKey.trim() || !apiSecret.trim()) {
      toast.error("Please enter both API key and secret before testing");
      return;
    }

    setIsTestingConnection(true);
    try {
      // First save the credentials, then test
      const saveResult = await connectAccountExchangeAction({
        accountId: account.id,
        accountName: account.owner,
        exchange: account.exchange,
        apiKey: apiKey.trim(),
        apiSecret: apiSecret.trim(),
      });

      if (saveResult.success) {
        const testResult = await testAccountExchangeAction({
          accountName: account.owner,
          exchange: account.exchange,
        });

        if (testResult.success) {
          toast.success("Connection test successful");
        } else {
          toast.error(testResult.error || "Connection test failed");
        }
      } else {
        toast.error(saveResult.error || "Failed to save credentials");
      }
    } catch (error) {
      console.error("Test connection error:", error);
      toast.error("Failed to test connection");
    } finally {
      setIsTestingConnection(false);
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <LinkIcon className="h-5 w-5 text-blue-600" />
          Connect {account.exchange.charAt(0).toUpperCase() + account.exchange.slice(1)}
        </CardTitle>
        <CardDescription>
          Enter your {account.exchange} API credentials for the {account.owner} account.
        </CardDescription>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="api-key">API Key</Label>
            <Input
              id="api-key"
              type="text"
              placeholder="Enter your API key"
              value={apiKey}
              onChange={(e) => setApiKey(e.target.value)}
              className="font-mono"
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="api-secret">API Secret</Label>
            <div className="relative">
              <Input
                id="api-secret"
                type={showApiSecret ? "text" : "password"}
                placeholder="Enter your API secret"
                value={apiSecret}
                onChange={(e) => setApiSecret(e.target.value)}
                className="font-mono pr-10"
              />
              <Button
                type="button"
                variant="ghost"
                size="sm"
                className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                onClick={() => setShowApiSecret(!showApiSecret)}
              >
                {showApiSecret ? (
                  <EyeOff className="h-4 w-4" />
                ) : (
                  <Eye className="h-4 w-4" />
                )}
              </Button>
            </div>
          </div>

          <div className="bg-amber-50 p-4 rounded-lg">
            <h4 className="font-medium text-amber-900 mb-2">Security Notice:</h4>
            <ul className="text-sm text-amber-800 space-y-1">
              <li>• Your credentials are encrypted and stored securely</li>
              <li>• Only trading permissions are required</li>
              <li>• Never share your API credentials with anyone</li>
            </ul>
          </div>

          <div className="space-y-2">
            <Button
              type="button"
              variant="outline"
              className="w-full"
              onClick={handleTestConnection}
              disabled={isTestingConnection || isPending || !apiKey.trim() || !apiSecret.trim()}
            >
              {isTestingConnection ? (
                <>
                  <div className="h-4 w-4 border-2 border-gray-400 border-t-transparent rounded-full animate-spin mr-2" />
                  Testing Connection...
                </>
              ) : (
                <>
                  <TestTube className="h-4 w-4 mr-2" />
                  Test Connection
                </>
              )}
            </Button>

            <Button 
              type="submit" 
              className="w-full" 
              disabled={isPending || !apiKey.trim() || !apiSecret.trim()}
            >
              {isPending ? (
                <>
                  <div className="h-4 w-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2" />
                  Connecting...
                </>
              ) : (
                "Connect Exchange"
              )}
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  );
}
