'use client'

import { useState, useTransition, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Key, CheckCircle, AlertCircle, Loader2, Eye, EyeOff } from "lucide-react";
import { toast } from "sonner";
import { validatePasswordStrength } from "@/lib/encryption";
import { createSecretKeyAction } from "@/app/onboarding/actions";

interface AccountSecretKeyStepProps {
  onStepComplete: () => void;
  secretKeyData?: {
    secretKey?: string;
    hasEncryptedPassword: boolean;
    hasEncryptedCredentials: boolean;
  };
}

export function AccountSecretKeyStep({ onStepComplete, secretKeyData }: AccountSecretKeyStepProps) {
  const [isPending, startTransition] = useTransition();
  const [secretKey, setSecretKey] = useState("");
  const [validation, setValidation] = useState<{ success: boolean; message: string } | null>(null);
  const [showPassword, setShowPassword] = useState(false);

  // Initialize with existing secret key if available
  useEffect(() => {
    if (secretKeyData?.secretKey) {
      setSecretKey(secretKeyData.secretKey);
    }
  }, [secretKeyData]);

  // Validate password in real-time
  useEffect(() => {
    if (secretKey) {
      const result = validatePasswordStrength(secretKey);
      setValidation(result);
    } else {
      setValidation(null);
    }
  }, [secretKey]);

  // If user already has a secret key, show completed state
  if (secretKeyData?.hasEncryptedPassword && !secretKey) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <CheckCircle className="h-5 w-5 text-green-600" />
            Secret Key Configured
          </CardTitle>
          <CardDescription>
            Your encryption secret key is already set up and ready to use.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center gap-2 text-green-600 mb-4">
            <CheckCircle className="h-4 w-4" />
            <span className="text-sm font-medium">Secret key is configured</span>
          </div>
          <Button onClick={onStepComplete} className="w-full">
            Continue to Next Step
          </Button>
        </CardContent>
      </Card>
    );
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!secretKey) {
      toast.error("Please enter a secret key");
      return;
    }

    if (!validation?.success) {
      toast.error("Please enter a stronger secret key");
      return;
    }

    startTransition(async () => {
      try {
        // Use the same server action as onboarding
        const result = await createSecretKeyAction({
          secretKey,
        });

        if (result.success) {
          toast.success("Secret key configured successfully");
          onStepComplete();
        } else {
          toast.error(result.message || "Failed to configure secret key");
        }
      } catch (error) {
        console.error("Secret key setup error:", error);
        toast.error("An error occurred while setting up your secret key");
      }
    });
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Key className="h-5 w-5 text-blue-600" />
          Secret Key Setup
        </CardTitle>
        <CardDescription>
          {secretKeyData?.hasEncryptedPassword 
            ? "Update your encryption secret key for this account"
            : "Create a secure secret key to encrypt your trading credentials"
          }
        </CardDescription>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="secret-key">Secret Key</Label>
            <div className="relative">
              <Input
                id="secret-key"
                type={showPassword ? "text" : "password"}
                placeholder="Enter a strong secret key"
                value={secretKey}
                onChange={(e) => setSecretKey(e.target.value)}
                className={validation && !validation.success ? "border-red-500" : ""}
              />
              <Button
                type="button"
                variant="ghost"
                size="sm"
                className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                onClick={() => setShowPassword(!showPassword)}
              >
                {showPassword ? (
                  <EyeOff className="h-4 w-4" />
                ) : (
                  <Eye className="h-4 w-4" />
                )}
              </Button>
            </div>
            
            {validation && (
              <div className={`flex items-center gap-2 text-sm ${
                validation.success ? "text-green-600" : "text-red-600"
              }`}>
                {validation.success ? (
                  <CheckCircle className="h-4 w-4" />
                ) : (
                  <AlertCircle className="h-4 w-4" />
                )}
                <span>{validation.message}</span>
              </div>
            )}
          </div>

          <div className="bg-blue-50 p-4 rounded-lg">
            <h4 className="font-medium text-blue-900 mb-2">Security Requirements:</h4>
            <ul className="text-sm text-blue-800 space-y-1">
              <li>• At least 12 characters long</li>
              <li>• Mix of uppercase and lowercase letters</li>
              <li>• Include numbers and special characters</li>
              <li>• Avoid common words or patterns</li>
            </ul>
          </div>

          <Button 
            type="submit" 
            className="w-full" 
            disabled={isPending || !validation?.success}
          >
            {isPending ? (
              <>
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                Setting up...
              </>
            ) : (
              "Configure Secret Key"
            )}
          </Button>
        </form>
      </CardContent>
    </Card>
  );
}
