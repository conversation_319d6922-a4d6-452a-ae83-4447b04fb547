'use server'

import { revalidatePath } from "next/cache";
import { redirect } from "next/navigation";
import { createUltimateService } from "@/lib/services/ultimate-service";
import { currentUser } from "@clerk/nextjs/server";

export interface AccountSetupResult {
  success: boolean;
  message?: string;
  error?: string;
  data?: any;
}

export interface CreateAccountPayload {
  name: string;
  exchange?: string;
}

export interface AccountProxyPayload {
  accountId: string;
  proxyId: string;
}

export interface AccountExchangePayload {
  accountId: string;
  accountName: string;
  exchange: string;
  apiKey: string;
  apiSecret: string;
}

export interface AccountActivationPayload {
  accountId: string;
}

export interface CreateProxyForAccountPayload {
  username?: string;
  password?: string;
  host: string;
  port: number;
  type: "http" | "socks5";
  accountId: string;
}

/**
 * Get current user email from Clerk
 */
async function getCurrentUserEmail(): Promise<string> {
  const user = await currentUser();
  if (!user) {
    throw new Error("Not authenticated");
  }

  const userEmail = user.emailAddresses[0]?.emailAddress;
  if (!userEmail) {
    throw new Error("User email not found");
  }

  return userEmail;
}

/**
 * Create a new trading account
 */
export async function createAccountAction(payload: CreateAccountPayload): Promise<AccountSetupResult> {
  try {
    const userEmail = await getCurrentUserEmail();
    const { name, exchange = "binance" } = payload;

    // Validate required fields
    if (!name || !name.trim()) {
      return {
        success: false,
        error: "Account name is required",
      };
    }

    // Validate name length
    if (name.length > 50) {
      return {
        success: false,
        error: "Account name must be 50 characters or less",
      };
    }

    // Create service instance
    const service = createUltimateService(userEmail);

    // Get user to get their ID
    const userResult = await service.getUserByEmail(userEmail);

    if (!userResult.success || !userResult.data) {
      return {
        success: false,
        error: "User not found in database",
      };
    }

    const userId = userResult.data.id;

    // Check if account name is unique for this user
    const existingAccountsResult = await service.getExchangeAccountsByUser(userId);
    
    if (existingAccountsResult.success && existingAccountsResult.data) {
      const existingNames = existingAccountsResult.data.map(account => account.owner.toLowerCase());
      
      if (existingNames.includes(name.toLowerCase())) {
        return {
          success: false,
          error: "An account with this name already exists. Please choose a different name.",
        };
      }
    }

    // Create the exchange account
    const createAccountResult = await service.createExchangeAccount({
      exchange,
      owner: name, // Use the provided name as the owner
      email: userEmail,
      userId,
    });

    if (!createAccountResult.success) {
      return {
        success: false,
        error: createAccountResult.message || "Failed to create account",
      };
    }

    revalidatePath('/accounts');
    
    return {
      success: true,
      message: "Account created successfully",
      data: {
        id: createAccountResult.data?.id,
        name,
        exchange,
        owner: name,
        setupComplete: false,
        created: createAccountResult.data?.created,
      },
    };

  } catch (error) {
    console.error("Account creation error:", error);
    return {
      success: false,
      error: "An error occurred while creating the account",
    };
  }
}

/**
 * Assign proxy to account
 */
export async function assignProxyToAccountAction(payload: AccountProxyPayload): Promise<AccountSetupResult> {
  try {
    const userEmail = await getCurrentUserEmail();
    const { accountId, proxyId } = payload;

    const service = createUltimateService(userEmail);

    // Update the exchange account with the proxy
    const updateResult = await service.updateExchangeAccount(accountId, {
      proxy: proxyId,
    });

    if (!updateResult.success) {
      return {
        success: false,
        error: updateResult.message || "Failed to assign proxy to account",
      };
    }

    revalidatePath('/accounts');
    
    return {
      success: true,
      message: "Proxy assigned successfully",
      data: updateResult.data,
    };

  } catch (error) {
    console.error("Proxy assignment error:", error);
    return {
      success: false,
      error: "An error occurred while assigning the proxy",
    };
  }
}

/**
 * Connect exchange credentials to account
 */
export async function connectAccountExchangeAction(payload: AccountExchangePayload): Promise<AccountSetupResult> {
  try {
    const userEmail = await getCurrentUserEmail();
    const { accountId, accountName, exchange, apiKey, apiSecret } = payload;

    const service = createUltimateService(userEmail);

    // Add credentials using the Ultimate service
    const credentialResult = await service.addNewCredentials({
      name: accountName,
      email: userEmail,
      exchange,
      api_key: apiKey,
      api_secret: apiSecret,
    });

    if (!credentialResult.success) {
      return {
        success: false,
        error: credentialResult.message || "Failed to save exchange credentials",
      };
    }

    revalidatePath('/accounts');
    
    return {
      success: true,
      message: "Exchange connected successfully",
      data: credentialResult.data,
    };

  } catch (error) {
    console.error("Exchange connection error:", error);
    return {
      success: false,
      error: "An error occurred while connecting the exchange",
    };
  }
}

/**
 * Test exchange connection
 */
export async function testAccountExchangeAction(payload: { accountName: string; exchange: string }): Promise<AccountSetupResult> {
  try {
    const userEmail = await getCurrentUserEmail();
    const { accountName, exchange } = payload;

    const service = createUltimateService(userEmail);

    // Test the exchange account connection
    const testResult = await service.getExchangeAccount(accountName, exchange);

    if (!testResult.success) {
      return {
        success: false,
        error: testResult.message || "Exchange connection test failed",
      };
    }

    return {
      success: true,
      message: "Exchange connection test successful",
      data: testResult.data,
    };

  } catch (error) {
    console.error("Exchange test error:", error);
    return {
      success: false,
      error: "An error occurred while testing the exchange connection",
    };
  }
}

/**
 * Create a new proxy and assign it to an account
 */
export async function createProxyForAccountAction(payload: CreateProxyForAccountPayload): Promise<AccountSetupResult> {
  try {
    const userEmail = await getCurrentUserEmail();
    const { host, port, type, username, password, accountId } = payload;

    // Validate required fields
    if (!host || !port) {
      return {
        success: false,
        error: "Host and port are required"
      };
    }

    const service = createUltimateService(userEmail);

    // Get user to get their ID
    const userResult = await service.getUserByEmail(userEmail);
    if (!userResult.success || !userResult.data) {
      return {
        success: false,
        error: "User not found"
      };
    }

    // Format the proxy string: username:password@host:port
    let ipAddress = `${host}:${port}`;
    if (username && password) {
      ipAddress = `${username}:${password}@${host}:${port}`;
    } else if (username) {
      ipAddress = `${username}@${host}:${port}`;
    }

    // Create the proxy
    const createProxyResult = await service.createProxy({
      ip_address: ipAddress,
      type: type,
      userId: userResult.data.id,
    });

    if (!createProxyResult.success) {
      return {
        success: false,
        error: createProxyResult.message || "Failed to create proxy"
      };
    }

    // Assign the proxy to the account
    const assignResult = await service.updateExchangeAccount(accountId, {
      proxy: createProxyResult.data?.id,
    });

    if (!assignResult.success) {
      return {
        success: false,
        error: assignResult.message || "Failed to assign proxy to account"
      };
    }

    revalidatePath('/accounts');

    return {
      success: true,
      message: "Proxy created and assigned successfully",
      data: { proxyId: createProxyResult.data?.id ?? "" }
    };

  } catch (error) {
    console.error("Error creating proxy for account:", error);
    return {
      success: false,
      error: "Failed to create proxy. Please try again."
    };
  }
}

/**
 * Complete account setup
 */
export async function completeAccountSetupAction(payload: AccountActivationPayload): Promise<AccountSetupResult> {
  try {
    const userEmail = await getCurrentUserEmail();
    const { accountId } = payload;

    const service = createUltimateService(userEmail);

    // Mark account as setup complete (this would be a custom field if needed)
    // For now, we'll just return success since the account exists and has credentials

    revalidatePath('/accounts');

    return {
      success: true,
      message: "Account setup completed successfully",
    };

  } catch (error) {
    console.error("Account activation error:", error);
    return {
      success: false,
      error: "An error occurred while completing the account setup",
    };
  }
}
