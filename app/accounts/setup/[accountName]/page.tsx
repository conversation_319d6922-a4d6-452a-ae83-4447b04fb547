import { redirect } from "next/navigation";
import { currentUser } from "@clerk/nextjs/server";
import { createUltimateService } from "@/lib/services/ultimate-service";
import { AccountSetupClient } from "./account-setup-client";

interface AccountSetupPageProps {
  params: Promise<{
    accountName: string;
  }>;
}

async function getAccountSetupData(accountName: string, userEmail: string) {
  try {
    const service = createUltimateService(userEmail);
    
    // Get user data
    const userResult = await service.getUserByEmail(userEmail);
    if (!userResult.success || !userResult.data) {
      throw new Error("User not found");
    }

    const user = userResult.data;
    const userId = user.id;

    // Get existing exchange accounts to find the one being set up
    const accountsResult = await service.getExchangeAccountsByUser(userId);
    const accounts = accountsResult.success ? accountsResult.data || [] : [];
    
    // Find the account with the matching name (owner field)
    const targetAccount = accounts.find(account => 
      account.owner.toLowerCase() === decodeURIComponent(accountName).toLowerCase()
    );

    if (!targetAccount) {
      throw new Error("Account not found");
    }

    // Get user's proxies
    const proxiesResult = await service.getProxiesByUser(userId);
    const proxies = proxiesResult.success ? proxiesResult.data || [] : [];

    // Get user's credentials for this account
    const credentialsResult = await service.getUserCredentials(userEmail);
    const allCredentials = credentialsResult.success ? credentialsResult.data || [] : [];
    
    // Filter credentials for this specific account/exchange
    const accountCredentials = allCredentials.filter(cred => 
      cred.name.toLowerCase() === targetAccount.owner.toLowerCase() && 
      cred.exchange === targetAccount.exchange
    );

    // Parse user settings to get secret key data
    let settings: any = {};
    try {
      if (typeof user.settings === 'string') {
        settings = JSON.parse(user.settings || '{}');
      } else if (typeof user.settings === 'object' && user.settings !== null) {
        settings = user.settings;
      }
    } catch (error) {
      console.log("Error parsing user settings:", error);
    }

    const secretKeyData = {
      secretKey: settings.password || '',
      hasEncryptedPassword: !!settings.password,
      hasEncryptedCredentials: !!settings.credentials,
    };

    return {
      user: {
        id: user.id,
        email: user.email,
        emailVisibility: user.emailVisibility,
        verified: user.verified,
        name: user.name,
        avatar: user.avatar,
        settings: user.settings,
        createdAt: user.created,
        updatedAt: user.updated,
      },
      account: {
        id: targetAccount.id,
        exchange: targetAccount.exchange,
        owner: targetAccount.owner,
        email: targetAccount.email,
        created: targetAccount.created,
        updated: targetAccount.updated,
        proxy: targetAccount.proxy,
      },
      proxies: proxies.map(proxy => ({
        id: proxy.id,
        ip_address: proxy.ip_address,
        type: proxy.type as "http" | "socks5",
        created: proxy.created,
        updated: proxy.updated,
        user: proxy.user,
      })),
      credentials: accountCredentials,
      secretKeyData,
    };

  } catch (error) {
    console.error("Error getting account setup data:", error);
    throw error;
  }
}

export default async function AccountSetupPage({ params }: AccountSetupPageProps) {
  // Await params first
  const resolvedParams = await params;

  // Check authentication
  const user = await currentUser();
  if (!user) {
    redirect('/sign-in');
  }

  const userEmail = user.emailAddresses[0]?.emailAddress;
  if (!userEmail) {
    redirect('/sign-in');
  }

  // Validate params
  if (!resolvedParams.accountName) {
    console.error("Account name parameter is missing");
    redirect('/accounts');
  }

  try {
    // Get account setup data
    const setupData = await getAccountSetupData(resolvedParams.accountName, userEmail);

    return <AccountSetupClient data={setupData} />;
  } catch (error) {
    console.error("Account setup page error:", error);
    // Redirect back to dashboard instead of accounts
    redirect('/dashboard');
  }
}
