'use client'

import { useState, useEffect } from "react";
import { use<PERSON>out<PERSON> } from "next/navigation";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { Container } from "@/components/container";
import { ArrowLef<PERSON>, ArrowRight, Check } from "lucide-react";
import { BottomNav } from "@/components/bottom-nav";

// Import step components (we'll create these)
import { EnhancedAccountProxyStep } from "../components/enhanced-account-proxy-step";
import { AccountExchangeStep } from "../components/account-exchange-step";
import { AccountSecretKeyStep } from "../components/account-secret-key-step";
import { AccountActivationStep } from "../components/account-activation-step";

interface AccountSetupData {
  user: {
    id: string;
    email: string;
    emailVisibility: boolean;
    verified: boolean;
    name: string;
    avatar: string;
    settings: any;
    createdAt: string;
    updatedAt: string;
  };
  account: {
    id: string;
    exchange: string;
    owner: string;
    email: string;
    created: string;
    updated: string;
    proxy?: string;
  };
  proxies: Array<{
    id: string;
    ip_address: string;
    type: "http" | "socks5";
    created: string;
    updated: string;
    user: string;
  }>;
  credentials: Array<{
    name: string;
    email: string;
    exchange: string;
    api_key: string;
    api_secret: string;
  }>;
  secretKeyData: {
    secretKey?: string;
    hasEncryptedPassword: boolean;
    hasEncryptedCredentials: boolean;
  };
}

interface AccountSetupClientProps {
  data: AccountSetupData;
}

const STEPS = [
  { id: 1, title: "Secret Key", description: "Set up your encryption key" },
  { id: 2, title: "Proxy Setup", description: "Configure proxy settings" },
  { id: 3, title: "Exchange Connection", description: "Connect your exchange account" },
  { id: 4, title: "Activate Account", description: "Complete account setup" },
];

export function AccountSetupClient({ data }: AccountSetupClientProps) {
  const router = useRouter();
  const [currentStep, setCurrentStep] = useState(1);
  const [completedSteps, setCompletedSteps] = useState<number[]>([]);

  // Determine initial step and completed steps based on existing data
  useEffect(() => {
    const completed: number[] = [];
    let nextStep = 1;

    // Step 1: Secret Key - check if user has encrypted password
    if (data.secretKeyData.hasEncryptedPassword) {
      completed.push(1);
      nextStep = 2;
    }

    // Step 2: Proxy - check if account has proxy assigned
    if (data.account.proxy && completed.includes(1)) {
      completed.push(2);
      nextStep = 3;
    }

    // Step 3: Exchange - check if credentials exist for this account
    if (data.credentials.length > 0 && completed.includes(2)) {
      completed.push(3);
      nextStep = 4;
    }

    setCompletedSteps(completed);
    setCurrentStep(nextStep);
  }, [data]);

  const handleStepComplete = (step: number) => {
    if (!completedSteps.includes(step)) {
      setCompletedSteps([...completedSteps, step]);
    }
    
    // Move to next step
    if (step < STEPS.length) {
      setCurrentStep(step + 1);
    }
  };

  const handleStepEdit = (step: number) => {
    setCurrentStep(step);
  };

  const handleBack = () => {
    router.push('/dashboard');
  };

  const handleNext = () => {
    if (currentStep < STEPS.length) {
      setCurrentStep(currentStep + 1);
    }
  };

  const handlePrevious = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  const progress = (completedSteps.length / STEPS.length) * 100;

  const renderStep = () => {
    switch (currentStep) {
      case 1:
        return (
          <AccountSecretKeyStep
            onStepComplete={() => handleStepComplete(1)}
            secretKeyData={data.secretKeyData}
          />
        );
      case 2:
        return (
          <EnhancedAccountProxyStep
            onStepComplete={() => handleStepComplete(2)}
            proxies={data.proxies}
            account={data.account}
          />
        );
      case 3:
        return (
          <AccountExchangeStep
            onStepComplete={() => handleStepComplete(3)}
            account={data.account}
            credentials={data.credentials}
          />
        );
      case 4:
        return (
          <AccountActivationStep
            user={data.user}
            account={data.account}
          />
        );
      default:
        return null;
    }
  };

  return (
    <div className="flex min-h-screen flex-col">
      <main className="flex-1 py-6">
        <Container className="max-w-4xl">
          <div className="mb-6">
            <div className="flex items-center gap-4 mb-4">
              <Button
                variant="ghost"
                size="sm"
                onClick={handleBack}
                className="flex items-center gap-2"
              >
                <ArrowLeft className="h-4 w-4" />
                Back to Dashboard
              </Button>
            </div>
            <h1 className="text-2xl font-bold">Setup {data.account.owner}</h1>
            <p className="text-gray-500">Complete the setup for your {data.account.exchange} trading account.</p>
          </div>

          {/* Mobile Progress Bar */}
          <div className="lg:hidden mb-6">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Setup Progress</CardTitle>
                <CardDescription>
                  Step {currentStep} of {STEPS.length}: {STEPS[currentStep - 1]?.title}
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Progress value={progress} className="mb-4" />
                <div className="flex justify-between text-sm text-gray-500">
                  <span>{completedSteps.length} of {STEPS.length} completed</span>
                  <span>{Math.round(progress)}%</span>
                </div>
              </CardContent>
            </Card>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Left Column - Progress and Steps Overview (Desktop Only) */}
            <div className="hidden lg:block lg:col-span-1 space-y-6">
              {/* Progress */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Setup Progress</CardTitle>
                  <CardDescription>
                    Step {currentStep} of {STEPS.length}: {STEPS[currentStep - 1]?.title}
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <Progress value={progress} className="mb-4" />
                  <div className="flex justify-between text-sm text-gray-500">
                    <span>{completedSteps.length} of {STEPS.length} completed</span>
                    <span>{Math.round(progress)}%</span>
                  </div>
                </CardContent>
              </Card>

              {/* Steps Overview */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Steps</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {STEPS.map((step) => (
                      <div key={step.id} className="flex items-center gap-3">
                        <div className={`
                          flex items-center justify-center w-8 h-8 rounded-full text-sm font-medium
                          ${completedSteps.includes(step.id)
                            ? 'bg-green-100 text-green-700'
                            : currentStep === step.id
                            ? 'bg-blue-100 text-blue-700'
                            : 'bg-gray-100 text-gray-500'
                          }
                        `}>
                          {completedSteps.includes(step.id) ? (
                            <Check className="h-4 w-4" />
                          ) : (
                            step.id
                          )}
                        </div>
                        <div className="flex-1">
                          <div className={`font-medium text-sm ${
                            completedSteps.includes(step.id) ? 'text-green-700' :
                            currentStep === step.id ? 'text-blue-700' : 'text-gray-500'
                          }`}>
                            {step.title}
                          </div>
                          <div className="text-xs text-gray-500">{step.description}</div>
                        </div>
                        {completedSteps.includes(step.id) && (
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleStepEdit(step.id)}
                            className="text-blue-600 hover:text-blue-700 text-xs px-2 py-1 h-auto"
                          >
                            Edit
                          </Button>
                        )}
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Right Column - Current Step Content */}
            <div className="lg:col-span-2">
              {/* Current Step */}
              {renderStep()}

              {/* Navigation */}
              <div className="flex justify-between mt-6">
                <Button
                  variant="outline"
                  onClick={handlePrevious}
                  disabled={currentStep === 1}
                >
                  <ArrowLeft className="h-4 w-4 mr-2" />
                  Previous
                </Button>
                <Button
                  onClick={handleNext}
                  disabled={currentStep === STEPS.length || !completedSteps.includes(currentStep)}
                >
                  Next
                  <ArrowRight className="h-4 w-4 ml-2" />
                </Button>
              </div>
            </div>
          </div>


        </Container>
      </main>
      <BottomNav />
    </div>
  );
}
