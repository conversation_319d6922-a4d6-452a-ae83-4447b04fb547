"use client"

import { useState, useEffect } from "react"
import { useRout<PERSON> } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Container } from "@/components/container"
import { AlertCircle, Check, Plus, Wallet, ArrowLeft, Loader2 } from "lucide-react"
import { cn } from "@/lib/utils"
import { CreateAccountDialog } from "@/components/create-account-dialog"
import { BottomNav } from "@/components/bottom-nav"
import { useLegendTradingAccount } from "@/lib/hooks/use-legend-trading-account"
import { observer } from "@legendapp/state/react"
import { fetchUserAccountsAction } from "./actions"
import { toast } from "sonner"

const AccountsPage = observer(function AccountsPage() {
  const {
    accounts,
    switchAccount,
    getActiveAccount,
    resumeAccountSetup,
    setAccounts,
    setLoading,
    isLoading: stateLoading
  } = useLegendTradingAccount()

  const router = useRouter()
  const [isLoading, setIsLoading] = useState<string | null>(null)
  const [showCreateDialog, setShowCreateDialog] = useState(false)

  // Fetch real account data on component mount
  useEffect(() => {
    const fetchAccounts = async () => {
      try {
        setLoading(true)
        const result = await fetchUserAccountsAction()

        if (result.success && result.data) {
          setAccounts(result.data)
        } else {
          console.error("Failed to fetch accounts:", result.error)
          toast.error(result.error || "Failed to load accounts")
        }
      } catch (error) {
        console.error("Error fetching accounts:", error)
        toast.error("An error occurred while loading accounts")
      } finally {
        setLoading(false)
      }
    }

    fetchAccounts()
  }, [setAccounts, setLoading])

  const handleSwitchAccount = (accountId: string) => {
    setIsLoading(accountId)

    // Simulate a brief loading state
    setTimeout(() => {
      switchAccount(accountId)
      router.push("/dashboard")
    }, 500)
  }

  const handleResumeSetup = (account: any) => {
    setIsLoading(account.id)

    // Simulate a brief loading state
    setTimeout(() => {
      resumeAccountSetup(account.id)
      // Redirect to the account-specific setup page
      router.push(`/accounts/setup/${encodeURIComponent(account.name)}`)
    }, 500)
  }

  // Sort accounts: active first, then by creation date (newest first)
  const sortedAccounts = [...accounts].sort((a, b) => {
    if (a.isActive) return -1
    if (b.isActive) return 1
    return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
  })

  return (
    <div className="flex min-h-screen flex-col">
      <main className="flex-1 py-6">
        <Container className="max-w-2xl">
          <div className="mb-6">
            <div className="flex items-center gap-4 mb-4">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => router.push('/dashboard')}
                className="flex items-center gap-2"
              >
                <ArrowLeft className="h-4 w-4" />
                Back to Dashboard
              </Button>
            </div>
            <h1 className="text-2xl font-bold">Your Accounts</h1>
            <p className="text-gray-500">Select an account to manage or add a new trading account.</p>
          </div>
          <Card>
            <CardHeader>
              <CardTitle>Switch Account</CardTitle>
              <CardDescription>Select an account to manage or add a new trading account.</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {stateLoading ? (
                <div className="flex items-center justify-center py-8">
                  <Loader2 className="h-6 w-6 animate-spin text-gray-500" />
                  <span className="ml-2 text-gray-500">Loading accounts...</span>
                </div>
              ) : accounts.length === 0 ? (
                <div className="text-center py-8">
                  <Wallet className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="font-medium text-gray-900 mb-2">No Trading Accounts</h3>
                  <p className="text-sm text-gray-500 mb-4">
                    Create your first trading account to get started.
                  </p>
                </div>
              ) : (
                <div className="flex flex-col gap-2">
                  {sortedAccounts.map((account) => (
                  <div key={account.id} className="relative">
                    <button
                      className={cn(
                        "flex items-center justify-between w-full px-4 py-3 rounded-md text-left transition-colors",
                        account.isActive ? "bg-[#e6f0e4] text-[#245c1a]" : "hover:bg-gray-100",
                        isLoading === account.id ? "opacity-70" : "",
                        !account.setupComplete ? "border border-dashed border-amber-300" : "",
                      )}
                      onClick={() =>
                        account.setupComplete ? handleSwitchAccount(account.id) : handleResumeSetup(account)
                      }
                      disabled={account.isActive || isLoading !== null}
                    >
                      <div className="flex items-center gap-3">
                        <Wallet className={cn("h-5 w-5", account.isActive ? "text-[#245c1a]" : "text-gray-500")} />
                        <div className="flex flex-col">
                          <span className="font-medium">{account.name}</span>
                          <span className="text-xs text-gray-500">
                            {account.setupComplete
                              ? `${account.exchange} • ${account.tradingPair}`
                              : "Setup incomplete"}
                          </span>
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        {!account.setupComplete && (
                          <span className="text-xs bg-amber-100 text-amber-700 px-2 py-1 rounded-full font-medium flex items-center">
                            <AlertCircle className="h-3 w-3 mr-1" />
                            Complete Setup
                          </span>
                        )}
                        {account.setupComplete && account.isActive && (
                          <span className="text-xs bg-green-100 text-green-700 px-2 py-1 rounded-full font-medium flex items-center">
                            <Check className="h-3 w-3 mr-1" />
                            Active
                          </span>
                        )}
                        {account.setupComplete && !account.isActive && (
                          <span className="text-xs text-gray-500">Ready</span>
                        )}
                        {isLoading === account.id && (
                          <div className="h-4 w-4 border-2 border-[#245c1a] border-t-transparent rounded-full animate-spin" />
                        )}
                      </div>
                    </button>

                    {!account.setupComplete && (
                      <div className="mt-2 ml-12 text-xs">
                        <div className="flex items-center gap-1 text-amber-600 font-medium">
                          <span>→</span>
                          <span>Click to complete account setup</span>
                        </div>
                        <div className="text-gray-500 mt-1">
                          Set up proxy, exchange connection, and credentials
                        </div>
                      </div>
                    )}
                  </div>
                  ))}
                </div>
              )}

              <Button
                variant="outline"
                className="w-full mt-4 flex items-center justify-center gap-2"
                onClick={() => setShowCreateDialog(true)}
                disabled={isLoading !== null}
              >
                <Plus className="h-4 w-4" />
                Add New Trading Account
              </Button>
            </CardContent>
          </Card>
        </Container>
      </main>
      <BottomNav />
      <CreateAccountDialog
        open={showCreateDialog}
        onOpenChange={setShowCreateDialog}
      />
    </div>
  )
})

export default AccountsPage
