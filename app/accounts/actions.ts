'use server'

import { currentUser } from "@clerk/nextjs/server";
import { createUltimateService } from "@/lib/services/ultimate-service";

export interface AccountData {
  id: string;
  name: string;
  exchange: string;
  apiKey: string;
  balance: string;
  tradingPair: string;
  isActive: boolean;
  setupComplete: boolean;
  createdAt: string;
  // Additional trading account fields
  usdt?: number;
  usdc?: number;
  proxy?: string;
  bullish?: boolean;
  bearish?: boolean;
  totalRisk?: number;
  movePercent?: number;
  max_non_essential?: number;
  profit_percent?: number;
  risk_reward?: number;
  exclude_coins?: string[];
  include_delisted?: boolean;
}

export interface FetchAccountsResult {
  success: boolean;
  data?: AccountData[];
  message?: string;
  error?: string;
}

/**
 * Get current user email from Clerk
 */
async function getCurrentUserEmail(): Promise<string> {
  const user = await currentUser();
  if (!user) {
    throw new Error("Not authenticated");
  }

  const userEmail = user.emailAddresses[0]?.emailAddress;
  if (!userEmail) {
    throw new Error("User email not found");
  }

  return userEmail;
}

/**
 * Fetch all trading accounts for the current user
 */
export async function fetchUserAccountsAction(): Promise<FetchAccountsResult> {
  try {
    const userEmail = await getCurrentUserEmail();
    const service = createUltimateService(userEmail);

    // Get user to get their ID
    const userResult = await service.getUserByEmail(userEmail);

    if (!userResult.success || !userResult.data) {
      return {
        success: false,
        error: "User not found in database",
      };
    }

    const userId = userResult.data.id;

    // Get user's exchange accounts
    const accountsResult = await service.getExchangeAccountsByUser(userId);

    if (!accountsResult.success) {
      return {
        success: false,
        error: accountsResult.message || "Failed to fetch accounts",
      };
    }

    const exchangeAccounts = accountsResult.data || [];

    // Get user's credentials to check which accounts have API keys
    const credentialsResult = await service.getUserCredentials(userEmail);
    const credentials = credentialsResult.success ? credentialsResult.data || [] : [];

    // Transform exchange accounts to match the TradingAccount interface
    const accounts: AccountData[] = exchangeAccounts.map((account, index) => {
      // Find matching credentials for this account
      const accountCredentials = credentials.find(
        cred => cred.name.toLowerCase() === account.owner.toLowerCase() && 
                cred.exchange === account.exchange
      );

      // Determine if setup is complete (has credentials)
      const setupComplete = !!accountCredentials;

      return {
        id: account.id,
        name: account.owner, // Use owner as the display name
        exchange: account.exchange,
        apiKey: accountCredentials?.api_key || "",
        balance: "$0.00", // Default balance, could be fetched from exchange
        tradingPair: "BTC/USDT", // Default trading pair
        isActive: index === 0, // First account is active by default
        setupComplete,
        createdAt: account.created,
        // Additional fields from exchange account
        usdt: account.usdt || 0,
        usdc: account.usdc || 0,
        proxy: account.proxy,
        bullish: account.bullish || false,
        bearish: account.bearish || false,
        totalRisk: account.totalRisk || 0,
        movePercent: account.movePercent || 0,
        max_non_essential: account.max_non_essential || 0,
        profit_percent: account.profit_percent || 0,
        risk_reward: account.risk_reward || 0,
        exclude_coins: account.exclude_coins || [],
        include_delisted: account.include_delisted || false,
      };
    });

    return {
      success: true,
      data: accounts,
      message: `Found ${accounts.length} trading accounts`,
    };

  } catch (error) {
    console.error("Fetch accounts error:", error);
    return {
      success: false,
      error: "An error occurred while fetching accounts",
    };
  }
}

/**
 * Set active account
 */
export async function setActiveAccountAction(accountId: string): Promise<FetchAccountsResult> {
  try {
    const userEmail = await getCurrentUserEmail();
    const service = createUltimateService(userEmail);

    // Get user to get their ID
    const userResult = await service.getUserByEmail(userEmail);

    if (!userResult.success || !userResult.data) {
      return {
        success: false,
        error: "User not found in database",
      };
    }

    // For now, we'll just return success since we don't store active account in the database
    // The active account is managed in the client state
    return {
      success: true,
      message: "Active account updated",
    };

  } catch (error) {
    console.error("Set active account error:", error);
    return {
      success: false,
      error: "An error occurred while setting active account",
    };
  }
}
