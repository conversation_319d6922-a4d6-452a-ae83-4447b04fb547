import { validateSuperUserRequest } from "@/ultils/helpers";
import { NextResponse } from "next/server";

interface Params {
  params: {
    investmentId: string;
    action: string;
  };
}

const actions = ["activate", "close", "reject"];

export async function GET(req: Request, { params }: Params) {
  try {
    const { dbService } = await validateSuperUserRequest(req);
    if (!actions.includes(params.action)) {
      throw new Error(`Invalid action: ${params.action}`);
    }

    const result = await dbService.triggerSuperUserActions({
      investmentId: params.investmentId,
      action: params.action as "activate" | "close" | "reject",
    });
    const messages: Record<string, string> = {
      activate: "Investment activated successfully",
      close: "Investment closed successfully",
      reject: "Investment rejected successfully",
    };
    return NextResponse.json(
      {
        success: true,
        message: messages[params.action] || "Action completed successfully",
        investment: result || null,
      },
      { status: 200 }
    );
  } catch (error: any) {
    return NextResponse.json({ error: error.message }, { status: 401 });
  }
}
