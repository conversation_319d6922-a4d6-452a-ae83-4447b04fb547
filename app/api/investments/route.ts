import { validateSuperUserRequest } from "@/ultils/helpers";
import { NextResponse } from "next/server";

export async function GET(req: Request) {
  try {
    const { dbService } = await validateSuperUserRequest(req);
    const result = await dbService.getInvestmentsWithWithdrawalRequest();

    return NextResponse.json({ result }, { status: 200 });
  } catch (error:any) {
    return NextResponse.json({ error: error.message }, { status: 401 });
  }
}
