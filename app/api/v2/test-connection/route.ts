/**
 * API Route: Test Connection (v2)
 * Tests the new refactored Ultimate service and database layers
 */

import { NextRequest, NextResponse } from "next/server";
import { currentUser } from "@clerk/nextjs/server";
import { createUltimateService } from "@/lib/services/ultimate-service";

/**
 * GET /api/v2/test-connection
 * Test the new refactored service layers
 */
export async function GET(_request: NextRequest) {
  try {
    console.log("=== V2 Service Layer Test ===");
    
    // Get current user from Clerk
    const clerkUser = await currentUser();
    const userEmail = clerkUser?.emailAddresses[0]?.emailAddress || "<EMAIL>";
    
    console.log("Testing with user email:", userEmail);
    
    // Create Ultimate service instance
    const service = createUltimateService(userEmail);
    
    // Test connection to both Ultimate app and database
    console.log("Testing service connections...");
    const connectionTest = await service.testConnection();
    console.log("Connection test result:", connectionTest);
    
    if (!connectionTest.success) {
      return NextResponse.json({
        success: false,
        message: "Service connection failed",
        details: connectionTest,
        userEmail,
      }, { status: 500 });
    }
    
    // Test user operations
    console.log("Testing user operations...");
    const userResult = await service.getUserByEmail(userEmail);
    console.log("User lookup result:", userResult.success ? "Found" : "Not found");
    
    // Test credential operations if user exists
    let credentialsTest = null;
    if (userResult.success && userResult.data) {
      console.log("Testing credential operations...");
      const credentialsResult = await service.getUserCredentials(userEmail);
      credentialsTest = {
        success: credentialsResult.success,
        count: credentialsResult.data?.length || 0,
      };
      console.log("Credentials test:", credentialsTest);
    }
    
    return NextResponse.json({
      success: true,
      message: "V2 service layer test completed successfully",
      details: {
        userEmail,
        connections: connectionTest.data,
        user: {
          exists: userResult.success && !!userResult.data,
          email: userResult.data?.email,
        },
        credentials: credentialsTest,
        serviceLayer: "v2-refactored",
        timestamp: new Date().toISOString(),
      }
    });
    
  } catch (error) {
    console.error("V2 service test failed:", error);
    
    return NextResponse.json({
      success: false,
      message: "V2 service test failed",
      error: error instanceof Error ? error.message : "Unknown error",
      stack: error instanceof Error ? error.stack : undefined,
    }, { status: 500 });
  }
}
