/**
 * API Route: Users (v2)
 * User management using the new refactored service layer
 */

import { NextRequest, NextResponse } from "next/server";
import { currentUser } from "@clerk/nextjs/server";
import { createUltimateService } from "@/lib/services/ultimate-service";

/**
 * GET /api/v2/users
 * Get current user information
 */
export async function GET(_request: NextRequest) {
  try {
    // Get current user from Clerk
    const clerkUser = await currentUser();
    
    if (!clerkUser) {
      return NextResponse.json(
        { success: false, message: "Not authenticated" },
        { status: 401 }
      );
    }

    const userEmail = clerkUser.emailAddresses[0]?.emailAddress;
    if (!userEmail) {
      return NextResponse.json(
        { success: false, message: "User email not found" },
        { status: 400 }
      );
    }

    // Create service instance
    const service = createUltimateService(userEmail);
    
    // Get user from database
    const result = await service.getUserByEmail(userEmail);
    
    if (!result.success) {
      return NextResponse.json({
        success: false,
        message: result.message || "Failed to get user",
      }, { status: 500 });
    }

    return NextResponse.json({
      success: true,
      data: result.data,
      message: "User retrieved successfully",
    });

  } catch (error) {
    console.error("Get user error:", error);
    
    return NextResponse.json({
      success: false,
      message: error instanceof Error ? error.message : "Failed to get user",
    }, { status: 500 });
  }
}

/**
 * POST /api/v2/users
 * Create a new user (sync from Clerk)
 */
export async function POST(_request: NextRequest) {
  try {
    // Get current user from Clerk
    const clerkUser = await currentUser();
    
    if (!clerkUser) {
      return NextResponse.json(
        { success: false, message: "Not authenticated" },
        { status: 401 }
      );
    }

    const userEmail = clerkUser.emailAddresses[0]?.emailAddress;
    if (!userEmail) {
      return NextResponse.json(
        { success: false, message: "User email not found" },
        { status: 400 }
      );
    }

    const userName = `${clerkUser.firstName || ""} ${clerkUser.lastName || ""}`.trim() || "User";

    // Create service instance
    const service = createUltimateService(userEmail);
    
    // Check if user already exists
    const existingUser = await service.getUserByEmail(userEmail);
    if (existingUser.success && existingUser.data) {
      return NextResponse.json({
        success: true,
        data: existingUser.data,
        message: "User already exists",
      });
    }

    // Create new user
    const result = await service.createUser({
      email: userEmail,
      name: userName,
    });
    
    if (!result.success) {
      return NextResponse.json({
        success: false,
        message: result.message || "Failed to create user",
      }, { status: 500 });
    }

    return NextResponse.json({
      success: true,
      data: result.data,
      message: "User created successfully",
    }, { status: 201 });

  } catch (error) {
    console.error("Create user error:", error);
    
    return NextResponse.json({
      success: false,
      message: error instanceof Error ? error.message : "Failed to create user",
    }, { status: 500 });
  }
}
