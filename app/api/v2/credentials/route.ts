/**
 * API Route: Credentials (v2)
 * Credential management using the new refactored service layer
 */

import { NextRequest, NextResponse } from "next/server";
import { currentUser } from "@clerk/nextjs/server";
import { createUltimateService } from "@/lib/services/ultimate-service";

/**
 * GET /api/v2/credentials
 * Get user's credentials
 */
export async function GET(_request: NextRequest) {
  try {
    // Get current user from Clerk
    const clerkUser = await currentUser();
    
    if (!clerkUser) {
      return NextResponse.json(
        { success: false, message: "Not authenticated" },
        { status: 401 }
      );
    }

    const userEmail = clerkUser.emailAddresses[0]?.emailAddress;
    if (!userEmail) {
      return NextResponse.json(
        { success: false, message: "User email not found" },
        { status: 400 }
      );
    }

    // Create service instance
    const service = createUltimateService(userEmail);
    
    // Get user credentials
    const result = await service.getUserCredentials(userEmail);
    
    if (!result.success) {
      return NextResponse.json({
        success: false,
        message: result.message || "Failed to get credentials",
      }, { status: 500 });
    }

    return NextResponse.json({
      success: true,
      data: result.data,
      message: "Credentials retrieved successfully",
    });

  } catch (error) {
    console.error("Get credentials error:", error);
    
    return NextResponse.json({
      success: false,
      message: error instanceof Error ? error.message : "Failed to get credentials",
    }, { status: 500 });
  }
}

/**
 * POST /api/v2/credentials
 * Add new credentials
 */
export async function POST(request: NextRequest) {
  try {
    // Get current user from Clerk
    const clerkUser = await currentUser();
    
    if (!clerkUser) {
      return NextResponse.json(
        { success: false, message: "Not authenticated" },
        { status: 401 }
      );
    }

    const userEmail = clerkUser.emailAddresses[0]?.emailAddress;
    if (!userEmail) {
      return NextResponse.json(
        { success: false, message: "User email not found" },
        { status: 400 }
      );
    }

    // Parse request body
    const body = await request.json();
    const { name, exchange, api_key, api_secret, exchange_email } = body;

    // Validate required fields
    if (!name || !exchange || !api_key || !api_secret) {
      return NextResponse.json({
        success: false,
        message: "Missing required fields: name, exchange, api_key, api_secret",
      }, { status: 400 });
    }

    // Create service instance
    const service = createUltimateService(userEmail);
    
    // Add credentials
    const result = await service.addCredential({
      name,
      email: exchange_email || userEmail,
      exchange,
      api_key,
      api_secret,
    });
    
    if (!result.success) {
      return NextResponse.json({
        success: false,
        message: result.message || "Failed to add credentials",
      }, { status: 500 });
    }

    return NextResponse.json({
      success: true,
      data: result.data,
      message: "Credentials added successfully",
    }, { status: 201 });

  } catch (error) {
    console.error("Add credentials error:", error);
    
    return NextResponse.json({
      success: false,
      message: error instanceof Error ? error.message : "Failed to add credentials",
    }, { status: 500 });
  }
}
