/**
 * API Route: Exchange Test (v2)
 * Test exchange operations using the new refactored service layer
 */

import { NextRequest, NextResponse } from "next/server";
import { currentUser } from "@clerk/nextjs/server";
import { createUltimateService } from "@/lib/services/ultimate-service";

/**
 * POST /api/v2/exchange/test
 * Test exchange account connection and get open symbols
 */
export async function POST(request: NextRequest) {
  try {
    // Get current user from Clerk
    const clerkUser = await currentUser();
    
    if (!clerkUser) {
      return NextResponse.json(
        { success: false, message: "Not authenticated" },
        { status: 401 }
      );
    }

    const userEmail = clerkUser.emailAddresses[0]?.emailAddress;
    if (!userEmail) {
      return NextResponse.json(
        { success: false, message: "User email not found" },
        { status: 400 }
      );
    }

    // Parse request body
    const body = await request.json();
    const { owner, exchange } = body;

    // Validate required fields
    if (!owner || !exchange) {
      return NextResponse.json({
        success: false,
        message: "Missing required fields: owner, exchange",
      }, { status: 400 });
    }

    // Create service instance
    const service = createUltimateService(userEmail);
    
    console.log(`Testing exchange account: ${owner} on ${exchange}`);
    
    // Test getting exchange account
    const accountResult = await service.getExchangeAccount(owner, exchange);
    
    if (!accountResult.success) {
      return NextResponse.json({
        success: false,
        message: accountResult.message || "Failed to get exchange account",
        details: {
          owner,
          exchange,
          step: "get_exchange_account",
        }
      }, { status: 500 });
    }

    console.log("Exchange account retrieved successfully");
    
    // Test getting open symbols
    const symbolsResult = await service.getAllOpenSymbols(owner, exchange);
    
    if (!symbolsResult.success) {
      return NextResponse.json({
        success: false,
        message: symbolsResult.message || "Failed to get open symbols",
        details: {
          owner,
          exchange,
          step: "get_open_symbols",
          account_connected: true,
        }
      }, { status: 500 });
    }

    console.log(`Retrieved ${symbolsResult.data?.length || 0} open symbols`);

    return NextResponse.json({
      success: true,
      data: {
        account: accountResult.data,
        symbols: symbolsResult.data,
        symbolCount: symbolsResult.data?.length || 0,
      },
      message: "Exchange test completed successfully",
      details: {
        owner,
        exchange,
        tests: {
          account_connection: true,
          api_validation: true,
          symbols_retrieval: true,
        }
      }
    });

  } catch (error) {
    console.error("Exchange test error:", error);
    
    return NextResponse.json({
      success: false,
      message: error instanceof Error ? error.message : "Exchange test failed",
      error: error instanceof Error ? error.stack : undefined,
    }, { status: 500 });
  }
}
