import { NextRequest, NextResponse } from "next/server";
import { getUltimateApp, testPocketBaseConnection } from "@/lib/ultimate-app-manager";

/**
 * GET /api/test-db
 * Test database connection and Ultimate app initialization
 */
export async function GET(_request: NextRequest) {
  try {
    console.log("=== Database Connection Test ===");
    
    // Check environment variables
    console.log("Environment variables check:");
    console.log("POCKETBASE_HOST:", process.env.POCKETBASE_HOST ? "✓ Set" : "✗ Missing");
    console.log("POCKETBASE_EMAIL:", process.env.POCKETBASE_EMAIL ? "✓ Set" : "✗ Missing");
    console.log("POCKETBASE_PASSWORD:", process.env.POCKETBASE_PASSWORD ? "✓ Set" : "✗ Missing");
    console.log("SALT:", process.env.SALT ? "✓ Set" : "✗ Missing");
    
    // Test PocketBase connection
    console.log("\nTesting PocketBase connection...");
    const connectionTest = await testPocketBaseConnection("<EMAIL>");
    console.log("Connection test result:", connectionTest);
    
    if (!connectionTest.success) {
      return NextResponse.json({
        success: false,
        message: "PocketBase connection failed",
        details: connectionTest.message,
        env_check: {
          POCKETBASE_HOST: !!process.env.POCKETBASE_HOST,
          POCKETBASE_EMAIL: !!process.env.POCKETBASE_EMAIL,
          POCKETBASE_PASSWORD: !!process.env.POCKETBASE_PASSWORD,
          SALT: !!process.env.SALT,
        }
      });
    }
    
    // Test Ultimate app initialization
    console.log("\nTesting Ultimate app initialization...");
    const app = await getUltimateApp({ email: "<EMAIL>" });
    console.log("Ultimate app initialized:", !!app);
    
    // Test database query
    console.log("\nTesting database query...");
    const pb = (app as any).app_db.pb;
    const testQuery = await pb.collection('users').getList(1, 1);
    console.log("Test query successful, found", testQuery.items.length, "users");
    
    return NextResponse.json({
      success: true,
      message: "Database connection successful",
      details: {
        pocketbase_connection: connectionTest.success,
        ultimate_app_initialized: !!app,
        test_query_successful: true,
        users_found: testQuery.items.length
      }
    });
    
  } catch (error) {
    console.error("Database test failed:", error);
    
    return NextResponse.json({
      success: false,
      message: "Database test failed",
      error: error instanceof Error ? error.message : "Unknown error",
      stack: error instanceof Error ? error.stack : undefined,
      env_check: {
        POCKETBASE_HOST: !!process.env.POCKETBASE_HOST,
        POCKETBASE_EMAIL: !!process.env.POCKETBASE_EMAIL,
        POCKETBASE_PASSWORD: !!process.env.POCKETBASE_PASSWORD,
        SALT: !!process.env.SALT,
      }
    }, { status: 500 });
  }
}
