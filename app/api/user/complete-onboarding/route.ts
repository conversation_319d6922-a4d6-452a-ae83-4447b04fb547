import { NextRequest, NextResponse } from "next/server";
import { currentUser } from "@clerk/nextjs/server";
import { createUltimateService, hasCompletedOnboarding } from "@/lib/services/ultimate-service";

/**
 * POST /api/user/complete-onboarding
 * Mark the current user's onboarding as complete by updating settings
 */
export async function POST(request: NextRequest) {
  try {
    // Get current user from Clerk
    const clerkUser = await currentUser();

    if (!clerkUser) {
      return NextResponse.json(
        { success: false, message: "Not authenticated" },
        { status: 401 }
      );
    }

    const userEmail = clerkUser.emailAddresses[0]?.emailAddress || "";
    const service = createUltimateService(userEmail);

    // Get user from our database
    let appUser = await service.getUserByEmailLegacy(userEmail);

    if (!appUser) {
      // User doesn't exist in our database yet, sync them first
      const userData = {
        email: userEmail,
        name: `${clerkUser.firstName || ""} ${clerkUser.lastName || ""}`.trim() || "User",
      };
      appUser = await service.createUserLegacy(userData);
    }

    // Get the credentials data from request body
    const body = await request.json();
    const { credentials } = body;

    if (!credentials || !credentials.api_key || !credentials.api_secret || !credentials.exchange) {
      return NextResponse.json(
        { success: false, message: "Credentials with api_key, api_secret, and exchange are required to complete onboarding" },
        { status: 400 }
      );
    }

    // Complete onboarding using the new method
    const updatedUser = await service.completeOnboardingLegacy(appUser.email, {
      name: credentials.name || "default",
      exchange: credentials.exchange,
      api_key: credentials.api_key,
      api_secret: credentials.api_secret,
    });

    return NextResponse.json({
      success: true,
      data: updatedUser,
      hasCompletedOnboarding: hasCompletedOnboarding(updatedUser),
      message: "Onboarding completed successfully",
    });

  } catch (error) {
    console.error("Complete onboarding error:", error);

    return NextResponse.json(
      {
        success: false,
        message: error instanceof Error ? error.message : "Failed to complete onboarding",
      },
      { status: 500 }
    );
  }
}
