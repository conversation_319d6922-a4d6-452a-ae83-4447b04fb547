import { NextRequest, NextResponse } from "next/server";
import { currentUser } from "@clerk/nextjs/server";
import { createUltimateService, AppUser, hasCompletedOnboarding } from "@/lib/services/ultimate-service";
import { createDualDatabaseService } from "@/lib/services/dual-database-service";
import { PocketBaseUser } from "@/lib/database/types";

/**
 * Convert PocketBaseUser to AppUser format
 */
function convertToAppUser(pbUser: PocketBaseUser): AppUser {
  return {
    id: pbUser.id,
    email: pbUser.email,
    emailVisibility: pbUser.emailVisibility,
    verified: pbUser.verified,
    name: pbUser.name,
    avatar: pbUser.avatar,
    settings: pbUser.settings,
    createdAt: pbUser.created,
    updatedAt: pbUser.updated,
  };
}

/**
 * POST /api/user/sync
 * Sync the current Clerk user with our database
 */
export async function POST(_request: NextRequest) {
  try {
    // Get current user from Clerk
    const clerkUser = await currentUser();

    if (!clerkUser) {
      return NextResponse.json(
        { success: false, message: "Not authenticated" },
        { status: 401 }
      );
    }

    const userData = {
      email: clerkUser.emailAddresses[0]?.emailAddress || "",
      name: `${clerkUser.firstName || ""} ${clerkUser.lastName || ""}`.trim() || "User",//use the firstpart of the email
    };

    try {
      // Create dual database service
      const dualDbService = createDualDatabaseService(userData.email);

      // Check if user exists in old database first
      const existingUser = await dualDbService.getUserFromOldDb(userData.email);

      if (existingUser) {
        // User exists in old database, ensure they also exist in new database
        const syncResult = await dualDbService.syncUserToNewDb(userData.email);

        if (!syncResult.success) {
          console.warn('Failed to sync user to new database:', syncResult.error);
        }

        // Use the ultimate service to ensure onboarding status
        const service = createUltimateService(userData.email);
        const appUser = convertToAppUser(existingUser);
        const updatedUser = await service.ensureOnboardingStatus(appUser);

        return NextResponse.json({
          success: true,
          data: updatedUser,
          hasCompletedOnboarding: hasCompletedOnboarding(updatedUser),
          message: "User synced successfully",
        });
      } else {
        // Create new user in both databases
        const createResults = await dualDbService.createUserInBothDatabases({
          email: userData.email,
          name: userData.name,
          settings: { hasCompletedOnboarding: false },
          approved: false // New users need approval in invite flow
        });

        // Check if creation in old database was successful
        if (!createResults.oldDb.success) {
          console.error('Failed to create user in old database:', createResults.oldDb.error);
          return NextResponse.json(
            { success: false, message: "Failed to create user in main database" },
            { status: 500 }
          );
        }

        // Log if new database creation failed but don't fail the request
        if (!createResults.newDb.success) {
          console.warn('Failed to create user in new database:', createResults.newDb.error);
        }

        // Use the ultimate service to ensure proper password generation
        const service = createUltimateService(userData.email);
        const convertedUser = convertToAppUser(createResults.oldDb.data!);
        const appUser = await service.ensureOnboardingStatus(convertedUser);

        return NextResponse.json({
          success: true,
          data: appUser,
          hasCompletedOnboarding: hasCompletedOnboarding(appUser),
          message: "User created and synced successfully",
        });
      }
    } catch (error) {
      console.error("Error syncing user:", error);
      return NextResponse.json(
        { success: false, message: "Failed to sync user with database" },
        { status: 500 }
      );
    }
  } catch (error) {
    console.error("User sync error:", error);

    return NextResponse.json(
      {
        success: false,
        message: error instanceof Error ? error.message : "Failed to sync user",
      },
      { status: 500 }
    );
  }
}

/**
 * GET /api/user/sync
 * Get the current user's data from our database
 */
export async function GET(_request: NextRequest) {
  try {
    // Get current user from Clerk
    const clerkUser = await currentUser();

    if (!clerkUser) {
      return NextResponse.json(
        { success: false, message: "Not authenticated" },
        { status: 401 }
      );
    }

    const userEmail = clerkUser.emailAddresses[0]?.emailAddress || "";
    const userName = `${clerkUser.firstName || ""} ${clerkUser.lastName || ""}`.trim() || "User";

    try {
      // Create dual database service
      console.log("Creating dual database service for email:", userEmail);
      const dualDbService = createDualDatabaseService(userEmail);

      // Try to get user from old database first
      console.log("Attempting to get user by email from old database");
      let existingUser = await dualDbService.getUserFromOldDb(userEmail);

      if (!existingUser) {
        // User doesn't exist in old database yet, create them in both databases
        console.log("Creating new user in both databases");
        const createResults = await dualDbService.createUserInBothDatabases({
          email: userEmail,
          name: userName,
          settings: { hasCompletedOnboarding: false },
          approved: false
        });

        if (!createResults.oldDb.success) {
          throw new Error("Failed to create user in main database");
        }

        // Log if new database creation failed but don't fail the request
        if (!createResults.newDb.success) {
          console.warn('Failed to create user in new database:', createResults.newDb.error);
        }

        existingUser = createResults.oldDb.data!;
      } else {
        // User exists in old database, ensure they also exist in new database
        const syncResult = await dualDbService.syncUserToNewDb(userEmail);
        if (!syncResult.success) {
          console.warn('Failed to sync user to new database:', syncResult.error);
        }
      }

      // Use the ultimate service to ensure proper settings
      const service = createUltimateService(userEmail);
      const appUser = convertToAppUser(existingUser);
      const finalUser = await service.ensureOnboardingStatus(appUser);

      return NextResponse.json({
        success: true,
        data: finalUser,
        hasCompletedOnboarding: hasCompletedOnboarding(finalUser),
        message: existingUser ? "User retrieved successfully" : "User created successfully",
      });

    } catch (error) {
      console.error("Database error details:", {
        message: error instanceof Error ? error.message : "Unknown error",
        stack: error instanceof Error ? error.stack : undefined,
        error: error
      });
      return NextResponse.json(
        {
          success: false,
          message: "Failed to access user database",
          error: error instanceof Error ? error.message : "Unknown error"
        },
        { status: 500 }
      );
    }
  } catch (error) {
    console.error("Get user error:", error);

    return NextResponse.json(
      {
        success: false,
        message: error instanceof Error ? error.message : "Failed to get user",
      },
      { status: 500 }
    );
  }
}
