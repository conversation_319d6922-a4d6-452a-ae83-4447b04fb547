import { NextRequest, NextResponse } from "next/server";
import { getLoggedInUser } from "@/app/auth/actions";

/**
 * GET /api/user/onboarding-status
 * Check if the current user has completed onboarding
 */
export async function GET(request: NextRequest) {
  try {
    // Get current user from new auth system
    const user = await getLoggedInUser();

    if (!user) {
      return NextResponse.json(
        { success: false, message: "Not authenticated" },
        { status: 401 }
      );
    }

    // Check onboarding status based on user settings
    const hasCompletedOnboarding = user.settings?.hasCompletedOnboarding === true;

    return NextResponse.json({
      success: true,
      hasCompletedOnboarding,
      userType: user.type,
      approved: user.approved,
    });

  } catch (error) {
    console.error("Onboarding status check error:", error);

    return NextResponse.json(
      {
        success: false,
        message: error instanceof Error ? error.message : "Failed to check onboarding status",
      },
      { status: 500 }
    );
  }
}
