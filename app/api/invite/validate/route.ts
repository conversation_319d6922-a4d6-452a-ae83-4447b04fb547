import { NextRequest, NextResponse } from "next/server";
import { getLoggedInUser } from "@/app/auth/actions";
import type { InviteCodeValidationPayload, InviteCodeValidationResult } from "@/app/onboarding/types";

export async function POST(request: NextRequest): Promise<NextResponse<InviteCodeValidationResult>> {
  try {
    // Check authentication
    const user = await getLoggedInUser();
    if (!user) {
      return NextResponse.json({
        success: false,
        error: "Authentication required"
      }, { status: 401 });
    }

    const body: InviteCodeValidationPayload = await request.json();
    const { inviteCode } = body;

    if (!inviteCode || typeof inviteCode !== 'string') {
      return NextResponse.json({
        success: false,
        error: "Invalid invite code format"
      }, { status: 400 });
    }

    // For now, we'll implement a simple validation system
    // In production, this would check against a database of valid invite codes
    const validInviteCodes = [
      "TRADE2024",
      "WELCOME123", 
      "EARLY2024",
      "BETA123",
      "INVITE001"
    ];

    const isValid = validInviteCodes.includes(inviteCode.toUpperCase());

    if (isValid) {
      return NextResponse.json({
        success: true,
        message: "Invite code is valid",
        data: {
          isValid: true,
          codeDetails: {
            createdBy: "admin",
            createdAt: new Date().toISOString(),
            usageCount: 0,
            maxUses: 100
          }
        }
      });
    } else {
      return NextResponse.json({
        success: false,
        error: "Invalid invite code. Please check your code and try again.",
        data: {
          isValid: false
        }
      });
    }

  } catch (error) {
    console.error("Error validating invite code:", error);
    return NextResponse.json({
      success: false,
      error: "Internal server error"
    }, { status: 500 });
  }
}