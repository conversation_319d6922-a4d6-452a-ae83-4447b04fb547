import { NextRequest, NextResponse } from "next/server";
import { testPocketBaseConnectionSimple } from "@/lib/ultimate-app";

/**
 * GET /api/test-ultimate
 * Test the Ultimate app integration - public endpoint for testing
 */
export async function GET(_request: NextRequest) {
  try {
    // Test the PocketBase connection
    const result = await testPocketBaseConnectionSimple();

    return NextResponse.json({
      success: true,
      data: result,
      message: "Ultimate app test completed",
    });

  } catch (error) {
    console.error("Ultimate app test error:", error);

    return NextResponse.json(
      {
        success: false,
        message: error instanceof Error ? error.message : "Failed to test Ultimate app",
      },
      { status: 500 }
    );
  }
}
