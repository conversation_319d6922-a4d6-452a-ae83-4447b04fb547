import { NextRequest, NextResponse } from "next/server";
import { createUltimateService } from "@/lib/services/ultimate-service";

/**
 * POST /api/test-user-creation
 * Test user creation functionality - public endpoint for testing
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { email, name, clerk_id } = body;

    if (!email || !name || !clerk_id) {
      return NextResponse.json(
        { success: false, message: "Missing required fields: email, name, clerk_id" },
        { status: 400 }
      );
    }

    // Test creating a user
    const service = createUltimateService(email);
    const user = await service.createUserLegacy({
      email,
      name,
    });

    return NextResponse.json({
      success: true,
      data: user,
      message: "User created successfully",
    });

  } catch (error) {
    console.error("User creation test error:", error);
    
    return NextResponse.json(
      {
        success: false,
        message: error instanceof Error ? error.message : "Failed to create user",
      },
      { status: 500 }
    );
  }
}

/**
 * GET /api/test-user-creation
 * Test user retrieval functionality - public endpoint for testing
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const email = searchParams.get('email');
    const clerkId = searchParams.get('clerk_id');

    if (!email && !clerkId) {
      return NextResponse.json(
        { success: false, message: "Either email or clerk_id parameter is required" },
        { status: 400 }
      );
    }

    let user = null;

    if (email) {
      const service = createUltimateService(email);
      user = await service.getUserByEmailLegacy(email);
    } else if (clerkId) {
      // clerk_id lookup is no longer supported since we removed it from the database
      return NextResponse.json(
        { success: false, message: "clerk_id lookup is no longer supported. Use email instead." },
        { status: 400 }
      );
    }

    return NextResponse.json({
      success: true,
      data: user,
      message: user ? "User found" : "User not found",
    });

  } catch (error) {
    console.error("User retrieval test error:", error);
    
    return NextResponse.json(
      {
        success: false,
        message: error instanceof Error ? error.message : "Failed to retrieve user",
      },
      { status: 500 }
    );
  }
}
