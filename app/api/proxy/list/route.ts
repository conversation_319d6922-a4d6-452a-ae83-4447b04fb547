import { NextRequest, NextResponse } from "next/server";
import { currentUser } from "@clerk/nextjs/server";
import { createUltimateService } from "@/lib/services/ultimate-service";

/**
 * GET /api/proxy/list
 * Get all proxies for the current user
 */
export async function GET(_request: NextRequest) {
  try {
    // Get current user from Clerk
    const clerkUser = await currentUser();

    if (!clerkUser) {
      return NextResponse.json(
        { success: false, message: "Not authenticated" },
        { status: 401 }
      );
    }

    const userEmail = clerkUser.emailAddresses[0]?.emailAddress;
    if (!userEmail) {
      return NextResponse.json(
        { success: false, message: "User email not found" },
        { status: 400 }
      );
    }

    try {
      // Create service instance
      const service = createUltimateService(userEmail);

      // Get user to get their ID
      const userResult = await service.getUserByEmail(userEmail);

      if (!userResult.success || !userResult.data) {
        return NextResponse.json(
          { success: false, message: "User not found in database" },
          { status: 404 }
        );
      }

      // Get user's proxies using the service
      console.log("Attempting to fetch proxies for user:", userResult.data.id);

      const proxiesResult = await service.getProxiesByUser(userResult.data.id);

      if (!proxiesResult.success) {
        console.log("Failed to get proxies:", proxiesResult.message);
        // Return empty list instead of error to prevent breaking the UI
        return NextResponse.json({
          success: true,
          data: [],
          total: 0,
          message: "No proxies found or unable to access proxy data"
        });
      }

      const proxies = proxiesResult.data || [];
      console.log("Found", proxies.length, "proxies for user");

      return NextResponse.json({
        success: true,
        data: proxies.map((proxy: any) => ({
          id: proxy.id,
          ip_address: proxy.ip_address,
          type: proxy.type,
          created: proxy.created,
          updated: proxy.updated,
        })),
        total: proxies.length,
      });

    } catch (error) {
      console.error("Error fetching proxies:", error);

      // Return empty list instead of error to prevent breaking the UI
      return NextResponse.json({
        success: true,
        data: [],
        total: 0,
        message: "No proxies found or unable to access proxy data"
      });
    }

  } catch (error) {
    console.error("Proxy list error:", error);

    // Return empty list to prevent breaking the onboarding flow
    return NextResponse.json({
      success: true,
      data: [],
      total: 0,
      message: "Unable to fetch proxy data"
    });
  }
}
