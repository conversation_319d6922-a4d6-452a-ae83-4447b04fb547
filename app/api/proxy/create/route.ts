import { NextRequest, NextResponse } from "next/server";
import { currentUser } from "@clerk/nextjs/server";
import { createUltimateService } from "@/lib/services/ultimate-service";

export interface ProxyCreateRequest {
  username?: string;
  password?: string;
  host: string;
  port: number;
  type: "http" | "socks5";
}

/**
 * POST /api/proxy/create
 * Create a new proxy configuration for the current user
 */
export async function POST(request: NextRequest) {
  try {
    // Get current user from Clerk
    const clerkUser = await currentUser();

    if (!clerkUser) {
      return NextResponse.json(
        { success: false, message: "Not authenticated" },
        { status: 401 }
      );
    }

    // Parse request body
    const body: ProxyCreateRequest = await request.json();
    const { username, password, host, port, type } = body;

    // Validate required fields
    if (!host || !port) {
      return NextResponse.json(
        { success: false, message: "Host and port are required" },
        { status: 400 }
      );
    }

    // Validate port number
    if (port < 1 || port > 65535) {
      return NextResponse.json(
        { success: false, message: "Port must be between 1 and 65535" },
        { status: 400 }
      );
    }

    // Validate proxy type
    if (!["http", "socks5"].includes(type)) {
      return NextResponse.json(
        { success: false, message: "Proxy type must be 'http' or 'socks5'" },
        { status: 400 }
      );
    }

    const userEmail = clerkUser.emailAddresses[0]?.emailAddress;
    if (!userEmail) {
      return NextResponse.json(
        { success: false, message: "User email not found" },
        { status: 400 }
      );
    }

    try {
      // Create service instance
      const service = createUltimateService(userEmail);

      // Get the current user to get their ID
      const userResult = await service.getUserByEmail(userEmail);

      if (!userResult.success || !userResult.data) {
        return NextResponse.json(
          { success: false, message: "User not found in database" },
          { status: 404 }
        );
      }

      // Format the proxy string: username:password@host:port
      let ipAddress = `${host}:${port}`;
      if (username && password) {
        ipAddress = `${username}:${password}@${host}:${port}`;
      } else if (username) {
        ipAddress = `${username}@${host}:${port}`;
      }

      // Create proxy using the service
      const proxyResult = await service.createProxy({
        ip_address: ipAddress,
        type: type,
        userId: userResult.data.id,
      });

      if (!proxyResult.success || !proxyResult.data) {
        throw new Error(proxyResult.message || "Failed to create proxy");
      }

      return NextResponse.json({
        success: true,
        message: "Proxy created successfully",
        data: {
          id: proxyResult.data.id,
          ip_address: proxyResult.data.ip_address,
          type: proxyResult.data.type,
          created: proxyResult.data.created,
        },
      });

    } catch (error) {
      console.error("Error creating proxy:", error);
      
      // Handle specific PocketBase errors
      if (error instanceof Error) {
        if (error.message.includes("Failed to create record")) {
          return NextResponse.json(
            { success: false, message: "Failed to create proxy record" },
            { status: 500 }
          );
        }
      }

      return NextResponse.json(
        { success: false, message: "Failed to create proxy" },
        { status: 500 }
      );
    }

  } catch (error) {
    console.error("Proxy creation error:", error);

    return NextResponse.json(
      {
        success: false,
        message: error instanceof Error ? error.message : "Failed to create proxy",
      },
      { status: 500 }
    );
  }
}
