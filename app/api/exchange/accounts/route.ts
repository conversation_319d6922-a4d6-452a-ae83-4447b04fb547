import { NextRequest, NextResponse } from "next/server";
import { currentUser } from "@clerk/nextjs/server";
import { createUltimateService } from "@/lib/services/ultimate-service";

/**
 * GET /api/exchange/accounts
 * Get all exchange accounts for the current user
 */
export async function GET(_request: NextRequest) {
  try {
    // Get current user from Clerk
    const clerkUser = await currentUser();

    if (!clerkUser) {
      return NextResponse.json(
        { success: false, message: "Not authenticated" },
        { status: 401 }
      );
    }

    const userEmail = clerkUser.emailAddresses[0]?.emailAddress;
    if (!userEmail) {
      return NextResponse.json(
        { success: false, message: "User email not found" },
        { status: 400 }
      );
    }

    try {
      // Create service instance
      const service = createUltimateService(userEmail);

      // Get user to get their ID
      const userResult = await service.getUserByEmail(userEmail);

      if (!userResult.success || !userResult.data) {
        return NextResponse.json(
          { success: false, message: "User not found in database" },
          { status: 404 }
        );
      }

      // Get user's exchange accounts using the service
      console.log("Attempting to fetch exchange accounts for user:", userResult.data.id);

      const accountsResult = await service.getExchangeAccountsByUser(userResult.data.id);

      if (!accountsResult.success) {
        console.log("Failed to get exchange accounts:", accountsResult.message);
        // Return empty list instead of error to prevent breaking the UI
        return NextResponse.json({
          success: true,
          data: [],
          total: 0,
          message: "No exchange accounts found or unable to access account data"
        });
      }

      const accounts = accountsResult.data || [];
      console.log("Found", accounts.length, "exchange accounts for user");

      return NextResponse.json({
        success: true,
        data: accounts.map((account: any) => ({
          id: account.id,
          exchange: account.exchange,
          owner: account.owner,
          email: account.email,
          usdt: account.usdt || 0,
          usdc: account.usdc || 0,
          proxy: account.proxy,
          bullish: account.bullish || false,
          bearish: account.bearish || false,
          totalRisk: account.totalRisk || 0,
          movePercent: account.movePercent || 0,
          max_non_essential: account.max_non_essential || 0,
          profit_percent: account.profit_percent || 0,
          risk_reward: account.risk_reward || 0,
          exclude_coins: account.exclude_coins || [],
          include_delisted: account.include_delisted || false,
          user: account.user,
          created: account.created,
          updated: account.updated,
        })),
        total: accounts.length,
      });

    } catch (error) {
      console.error("Error fetching exchange accounts:", error);

      // Return empty list instead of error to prevent breaking the UI
      return NextResponse.json({
        success: true,
        data: [],
        total: 0,
        message: "No exchange accounts found or unable to access account data"
      });
    }

  } catch (error) {
    console.error("Exchange accounts list error:", error);

    // Return empty list to prevent breaking the onboarding flow
    return NextResponse.json({
      success: true,
      data: [],
      total: 0,
      message: "Unable to fetch exchange account data"
    });
  }
}
