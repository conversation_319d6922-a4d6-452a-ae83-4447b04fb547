import { NextRequest, NextResponse } from "next/server";
import { currentUser } from "@clerk/nextjs/server";
import { createUltimateService } from "@/lib/services/ultimate-service";

export interface BinanceConnectRequest {
  apiKey: string;
  secretKey: string;
  binanceEmail: string;
  proxyId?: string;
}

/**
 * POST /api/exchange/binance/connect
 * Connect Binance account and store credentials
 */
export async function POST(request: NextRequest) {
  try {
    // Get current user from Clerk
    const clerkUser = await currentUser();

    if (!clerkUser) {
      return NextResponse.json(
        { success: false, message: "Not authenticated" },
        { status: 401 }
      );
    }

    // Parse request body
    const body: BinanceConnectRequest = await request.json();
    const { apiKey, secretKey, binanceEmail, proxyId } = body;

    // Validate required fields
    if (!apiKey || !secretKey || !binanceEmail) {
      return NextResponse.json(
        { success: false, message: "API key, secret key, and Binance email are required" },
        { status: 400 }
      );
    }

    const userEmail = clerkUser.emailAddresses[0]?.emailAddress;
    const userName = `${clerkUser.firstName || ""} ${clerkUser.lastName || ""}`.trim() || "User";
    
    if (!userEmail) {
      return NextResponse.json(
        { success: false, message: "User email not found" },
        { status: 400 }
      );
    }

    try {
      // Create service instance
      const service = createUltimateService(userEmail);

      // Get the current user to get their ID
      const userResult = await service.getUserByEmail(userEmail);

      if (!userResult.success || !userResult.data) {
        return NextResponse.json(
          { success: false, message: "User not found in database" },
          { status: 404 }
        );
      }

      // Generate unique owner name (incremental if duplicate)
      const baseOwnerName = userName.toLowerCase().replace(/\s+/g, '_');
      let ownerName = baseOwnerName;
      let counter = 1;

      // Check for existing exchange accounts with same owner name
      const existingAccountsResult = await service.getExchangeAccountsByUser(userResult.data.id);

      if (existingAccountsResult.success && existingAccountsResult.data) {
        const existingAccounts = existingAccountsResult.data.filter((account: any) =>
          account.owner.startsWith(baseOwnerName)
        );

        if (existingAccounts.length > 0) {
          // Find the highest counter for this base name
          const existingOwners = existingAccounts.map((account: any) => account.owner);
          const counters = existingOwners
            .filter((owner: string) => owner.startsWith(baseOwnerName))
            .map((owner: string) => {
              const match = owner.match(/_(\d+)$/);
              return match ? parseInt(match[1]) : 0;
            });

          if (counters.length > 0) {
            counter = Math.max(...counters) + 1;
          }
          ownerName = `${baseOwnerName}_${counter}`;
        }
      }

      // Step 1: Store credentials using Ultimate service
      console.log("Storing Binance credentials...");
      const credentialResult = await service.addCredential({
        name: `binance-${ownerName}`, // Unique name
        email: binanceEmail,
        exchange: "binance",
        api_key: apiKey,
        api_secret: secretKey,
      });

      if (!credentialResult.success) {
        throw new Error(credentialResult.message || "Failed to store credentials");
      }

      // Step 2: Create exchange account record using service
      console.log("Creating exchange account record...");
      const exchangeAccountResult = await service.createExchangeAccount({
        exchange: "binance",
        owner: ownerName,
        email: binanceEmail,
        proxy: proxyId || undefined,
        userId: userResult.data.id,
      });

      if (!exchangeAccountResult.success || !exchangeAccountResult.data) {
        throw new Error(exchangeAccountResult.message || "Failed to create exchange account");
      }

      // Step 3: Test Binance API connection
      console.log("Testing Binance API connection...");
      let apiValidation = {
        connected: false,
        symbols: [] as string[],
        error: undefined as string | undefined,
      };

      try {
        // Test API connection using service
        const exchangeAccountResult = await service.getExchangeAccount(ownerName, "binance");

        if (exchangeAccountResult.success) {
          // Test API connection by getting symbols
          const symbolsResult = await service.getAllOpenSymbols(ownerName, "binance");

          if (symbolsResult.success && symbolsResult.data && Array.isArray(symbolsResult.data)) {
            apiValidation.connected = true;
            apiValidation.symbols = symbolsResult.data.slice(0, 5); // Return first 5 symbols as proof
            console.log("Binance API connection successful");
          } else {
            apiValidation.error = symbolsResult.message || "Invalid response from Binance API";
            console.log("Binance API connection failed: Invalid response");
          }
        } else {
          apiValidation.error = exchangeAccountResult.message || "Failed to get exchange account";
          console.log("Failed to get exchange account:", apiValidation.error);
        }
      } catch (apiError) {
        apiValidation.error = apiError instanceof Error ? apiError.message : "Unknown API error";
        console.log("Binance API connection failed:", apiValidation.error);
      }

      return NextResponse.json({
        success: true,
        message: "Binance account connected successfully",
        data: {
          exchangeAccount: {
            id: exchangeAccountResult.data.id,
            exchange: exchangeAccountResult.data.exchange,
            owner: exchangeAccountResult.data.owner,
            email: exchangeAccountResult.data.email,
            created: exchangeAccountResult.data.created,
          },
          credentials: {
            name: `binance-${ownerName}`,
            exchange: "binance",
            encrypted: true,
          },
          apiValidation,
        },
      });

    } catch (error) {
      console.error("Error connecting Binance account:", error);
      
      // Handle specific errors
      if (error instanceof Error) {
        if (error.message.includes("Failed to create record")) {
          return NextResponse.json(
            { success: false, message: "Failed to create exchange account record" },
            { status: 500 }
          );
        }
        if (error.message.includes("credential")) {
          return NextResponse.json(
            { success: false, message: "Failed to store credentials" },
            { status: 500 }
          );
        }
      }

      return NextResponse.json(
        { success: false, message: "Failed to connect Binance account" },
        { status: 500 }
      );
    }

  } catch (error) {
    console.error("Binance connection error:", error);

    return NextResponse.json(
      {
        success: false,
        message: error instanceof Error ? error.message : "Failed to connect Binance account",
      },
      { status: 500 }
    );
  }
}
