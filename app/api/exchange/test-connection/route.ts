import { NextRequest, NextResponse } from "next/server";
import { currentUser } from "@clerk/nextjs/server";
import { createUltimateService } from "@/lib/services/ultimate-service";

export interface TestConnectionRequest {
  owner: string;
  exchange: string;
}

/**
 * POST /api/exchange/test-connection
 * Test exchange API connection using stored credentials
 */
export async function POST(request: NextRequest) {
  try {
    // Get current user from Clerk
    const clerkUser = await currentUser();

    if (!clerkUser) {
      return NextResponse.json(
        { success: false, message: "Not authenticated" },
        { status: 401 }
      );
    }

    // Parse request body
    const body: TestConnectionRequest = await request.json();
    const { owner, exchange } = body;

    // Validate required fields
    if (!owner || !exchange) {
      return NextResponse.json(
        { success: false, message: "Owner and exchange are required" },
        { status: 400 }
      );
    }

    const userEmail = clerkUser.emailAddresses[0]?.emailAddress;
    if (!userEmail) {
      return NextResponse.json(
        { success: false, message: "User email not found" },
        { status: 400 }
      );
    }

    try {
      // Create service instance
      const service = createUltimateService(userEmail);

      console.log(`Testing ${exchange} API connection for owner: ${owner}`);

      // Test API connection using service
      const exchangeAccountResult = await service.getExchangeAccount(owner, exchange);

      if (!exchangeAccountResult.success) {
        return NextResponse.json({
          success: false,
          connected: false,
          message: `Failed to get exchange account: ${exchangeAccountResult.message}`,
        });
      }

      // Test API connection by getting symbols
      const symbolsResult = await service.getAllOpenSymbols(owner, exchange);

      if (symbolsResult.success && symbolsResult.data && Array.isArray(symbolsResult.data)) {
        console.log(`${exchange} API connection successful, found ${symbolsResult.data.length} symbols`);

        return NextResponse.json({
          success: true,
          connected: true,
          message: `${exchange} API connection successful`,
          data: {
            symbolCount: symbolsResult.data.length,
            sampleSymbols: symbolsResult.data.slice(0, 5), // Return first 5 symbols as proof
          },
        });
      } else {
        console.log(`${exchange} API connection failed: ${symbolsResult.message || 'Invalid response'}`);

        return NextResponse.json({
          success: false,
          connected: false,
          message: symbolsResult.message || `Invalid response from ${exchange} API`,
        });
      }

    } catch (error) {
      console.error(`Error testing ${exchange} connection:`, error);
      
      const errorMessage = error instanceof Error ? error.message : "Unknown API error";
      
      return NextResponse.json({
        success: false,
        connected: false,
        message: `${exchange} API connection failed: ${errorMessage}`,
      });
    }

  } catch (error) {
    console.error("Test connection error:", error);

    return NextResponse.json(
      {
        success: false,
        connected: false,
        message: error instanceof Error ? error.message : "Failed to test connection",
      },
      { status: 500 }
    );
  }
}
