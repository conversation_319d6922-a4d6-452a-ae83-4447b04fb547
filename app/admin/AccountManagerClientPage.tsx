"use client";

import { useAdmin } from "@/contexts/admin-provider";
import { useEffect } from "react";
import { InvestmentList } from "../components/investments/InvestmentList";

export default function AccountManagerClientPage({
  withdraw,
}: {
  withdraw: boolean;
}) {
  const { dialogRef } = useAdmin();

  useEffect(() => {
    if (withdraw) {
      dialogRef.current?.open();
    }
  }, []);
  return <InvestmentList />;
}
