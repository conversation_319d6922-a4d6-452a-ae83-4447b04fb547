"use client";

import { BottomNav } from "@/components/bottom-nav";
import { Container } from "@/components/container";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { useAdmin } from "@/contexts/admin-provider";
import { useTranslation } from "@/contexts/translation-context";

import {
  ArrowLeft,
  Clock,
  Loader2,
  Search,
  UserCheck,
  Users,
  UserX,
} from "lucide-react";
import Link from "next/link";
import { useState } from "react";

export function InvitationsPageClient({}) {
  const { t } = useTranslation();

  const { stats, followers: initialInvitations, user: adminUser } = useAdmin();
  const [searchQuery, setSearchQuery] = useState("");
  const [activeTab, setActiveTab] = useState("all");

  // Filter users based on search and tab
  const filteredInvitations = initialInvitations.filter((user) => {
    const matchesSearch =
      !searchQuery ||
      user.email.toLowerCase().includes(searchQuery.toLowerCase()) ||
      user.name.toLowerCase().includes(searchQuery.toLowerCase());

    // Map approval status to tab names
    let userStatus = "pending";
    if (user.approved === true) {
      userStatus = "approved";
    } else if (user.approved === false) {
      userStatus = "pending";
    }

    const matchesTab = activeTab === "all" || userStatus === activeTab;

    return matchesSearch && matchesTab;
  });

  return (
    <>
      <Container className="py-8">
        <div className="space-y-6">
          {/* Header */}
          <div className="mb-6 md:mb-8">
            <Link
              href="/admin"
              className="flex items-center space-x-2 mb-4 text-sm font-medium text-muted-foreground hover:underline"
            >
              <ArrowLeft className="w-5 h-5" />
              <span>Back to Dashboard</span>
            </Link>

            <div>
              <h1 className="text-3xl font-bold">{t("invite_header")}</h1>
              <p className="text-muted-foreground mt-2">
                {t("invite_description")}{" "}
                <code className="bg-muted px-2 py-1 rounded">
                  {adminUser?.invite_code}
                </code>
              </p>
            </div>
          </div>

          {/* Stats Cards */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center space-x-2">
                  <Users className="w-4 h-4 text-blue-600" />
                  <div>
                    <p className="text-sm font-medium">
                      {t("invite_stats_total")}
                    </p>
                    <p className="text-2xl font-bold">{stats.total}</p>
                  </div>
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center space-x-2">
                  <Clock className="w-4 h-4 text-yellow-600" />
                  <div>
                    <p className="text-sm font-medium">{t("invite_pending")}</p>
                    <p className="text-2xl font-bold">{stats.pending}</p>
                  </div>
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center space-x-2">
                  <UserCheck className="w-4 h-4 text-green-600" />
                  <div>
                    <p className="text-sm font-medium">
                      {t("invite_approved")}
                    </p>
                    <p className="text-2xl font-bold">{stats.approved}</p>
                  </div>
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center space-x-2">
                  <UserX className="w-4 h-4 text-red-600" />
                  <div>
                    <p className="text-sm font-medium">
                      {t("invite_rejected")}
                    </p>
                    <p className="text-2xl font-bold">{stats.rejected}</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Search */}
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
            <Input
              placeholder={t("invite_table_search_table")}
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10"
            />
          </div>

          {/* Invitations Table */}
          <Card>
            <CardHeader>
              <CardTitle>{t("invite_table_name")}</CardTitle>
              <CardDescription>{t("invite_description")}</CardDescription>
            </CardHeader>
            <CardContent>
              <Tabs value={activeTab} onValueChange={setActiveTab}>
                <TabsList className="grid w-full grid-cols-4">
                  <TabsTrigger value="all"  className="text-xs md:text-sm" >
                    {t("invite_stats_all")} 
                  </TabsTrigger>
                  <TabsTrigger value="pending" className="text-xs md:text-sm">
                    {t("invite_pending")}
                  </TabsTrigger>
                  <TabsTrigger value="approved" className="text-xs md:text-sm">
                    {t("invite_approved")} 
                  </TabsTrigger>
                  <TabsTrigger value="rejected" className="text-xs md:text-sm">
                    {t("invite_rejected")}
                  </TabsTrigger>
                </TabsList>

                <TabsContent value={activeTab} className="mt-4">
                  <div className="space-y-4">
                    {filteredInvitations.length === 0 ? (
                      <div className="text-center py-8 text-muted-foreground">
                        {t("invite_empty")}
                      </div>
                    ) : (
                      filteredInvitations.map((user) => {
                        return <InviteUserDetail key={user.id} user={user} />;
                      })
                    )}
                  </div>
                </TabsContent>
              </Tabs>
            </CardContent>
          </Card>
        </div>
      </Container>
      <BottomNav />
    </>
  );
}

type InviteUserDetailProps = {
  user: UserProps;
};
const InviteUserDetail = ({ user }: InviteUserDetailProps) => {
  const { onApprove: approve, onReject: reject } = useAdmin();
  const [loading, setLoading] = useState<"approve" | "reject" | null>(null);
  const { t } = useTranslation();
  const getStatusBadge = (status: string) => {
    switch (status) {
      case "pending":
        return (
          <Badge variant="outline" className="text-yellow-600">
            <Clock className="w-3 h-3 mr-1" />
            {t("invite_pending")}
          </Badge>
        );
      case "approved":
        return (
          <Badge variant="outline" className="text-green-600">
            <UserCheck className="w-3 h-3 mr-1" />
            {t("invite_approved")}
          </Badge>
        );
      case "rejected":
        return (
          <Badge variant="outline" className="text-red-600">
            <UserX className="w-3 h-3 mr-1" />
            {t("invite_rejected")}
          </Badge>
        );
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  const userStatus = user.approved ? "approved" : "pending";
  async function onApprove() {
    setLoading("approve");
    try {
      await approve(user.id);
    } catch (error) {
      console.log(error);
    } finally {
      setLoading(null);
    }
  }

  async function onReject() {
    setLoading("reject");
    try {
      await reject(user.id);
    } catch (error) {
      console.log(error);
    } finally {
      setLoading(null);
    }
  }
  return (
    <div key={user.id} className="border rounded-lg p-4">
      <div className="flex items-center justify-between">
        <div className="space-y-1">
          <div className="flex items-center space-x-2">
            <h3 className="font-medium">{user.name}</h3>
            {getStatusBadge(userStatus)}
          </div>
          <p className="text-sm text-muted-foreground">{user.email}</p>
          <p className="text-xs text-muted-foreground">
            {t("invite_table_date")}: {formatDate(user.created)}
            {user.updated !== user.created && (
              <> • Updated: {formatDate(user.updated)}</>
            )}
          </p>
        </div>

        {!user.approved && (
          <div className="flex space-x-2">
            <Button size="sm" onClick={onApprove} disabled={loading !== null}>
              {loading === "approve" ? (
                <Loader2 className="w-4 h-4 animate-spin" />
              ) : (
                t("invite_action_approve")
              )}
            </Button>
            <Button
              size="sm"
              variant="outline"
              onClick={onReject}
              disabled={loading !== null}
            >
              {loading === "reject" ? (
                <Loader2 className="w-4 h-4 animate-spin" />
              ) : (
                t("invite_action_reject")
              )}
            </Button>
          </div>
        )}
      </div>
    </div>
  );
};
