import { redirect } from "next/navigation";
import { getLoggedInUser } from "../auth/actions";
import { AdminProvider } from "@/contexts/admin-provider";
import { getAdminFollowers } from "@/contexts/actions";

export const dynamic = 'force-dynamic';

export default async function AdminLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  
  const user = await getLoggedInUser();

  if (!user) {
    redirect("/auth/login");
  }

  if (user.role !== "admin") {
    redirect("/dashboard");
  }
  const followers = await getAdminFollowers(user.id!);


  return <AdminProvider  followers={followers}>{children}</AdminProvider>;
}
