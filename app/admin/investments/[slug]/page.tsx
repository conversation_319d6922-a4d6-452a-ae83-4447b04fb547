"use client";

import { But<PERSON> } from "@/components/ui/button";
import { ArrowLeft } from "lucide-react";
import { useParams, useRouter } from "next/navigation";
import { useState } from "react";

export default function InvestmentDetailPage() {
  const router = useRouter();
  const { slug } = useParams();
  const [investment, setInvestment] = useState<any>(null);
  return (
    <>
      <div>
        <Button
          variant="ghost"
          size="icon"
          onClick={() => router.push("/admin/investments")}
        >
          <ArrowLeft className="h-5 w-5" />
        </Button>
      </div>
      <span>This is the investment detail page for {slug}</span>
    </>
  );
}
