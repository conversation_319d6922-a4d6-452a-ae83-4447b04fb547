import { Container } from "@/components/container";
import { getInvestments } from "./actions";
import { InvestmentProvider } from "@/contexts/investment-context";
import AccountManagerClientPage from "./AccountManagerClientPage";

export default async function AccountManagerPage({
  searchParams,
}: {
  searchParams: { [key: string]: string | string[] | undefined };
}) {
  const investments = await getInvestments();
  
   // Await searchParams before using it
  const params = await searchParams;
  const withdraw = params.withdraw === "true";


  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 pb-20 md:pb-0">
      <Container className="py-4 md:py-8">
        <div className="max-w-7xl mx-auto">
          <InvestmentProvider investments={investments}>
            <AccountManagerClientPage withdraw={withdraw} />
          </InvestmentProvider>
        </div>
      </Container>
    </div>
  );
}
