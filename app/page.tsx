// pages/landing-page.jsx (or wherever your component is)
"use client";

import { BottomNav } from "@/components/bottom-nav";
import { Container } from "@/components/container";
import { SectionHeader } from "@/components/section-header";
import { Button } from "@/components/ui/button";
import { useUser } from "@/contexts/user-provider";
import { ArrowRight } from "lucide-react";
import Link from "next/link";

// Import the extracted constants
import {
  FAQS,
  FEATURES,
  PLANS,
  SITE_CONFIG,
  STEPS,
} from "@/lib/constants/landing-page";

export default function LandingPage() {
  const {  isAuthenticated } = useUser();
  return (
    <div className="flex flex-col min-h-screen">
      <main className="flex-1">
        {/* Hero Section */}
        <section className="w-full py-12 md:py-24 lg:py-32 bg-[#f8faf7] dark:bg-muted/50">
          <Container>
            <div className="grid gap-6 lg:grid-cols-2 lg:gap-12 items-center">
              <div className="space-y-4">
                <h1 className="text-3xl font-bold tracking-tighter sm:text-4xl md:text-5xl">
                  Start Trading Crypto with Confidence
                </h1>
                <p className="text-muted-foreground md:text-xl/relaxed lg:text-base/relaxed xl:text-xl/relaxed">
                  {SITE_CONFIG.name} makes cryptocurrency trading simple for
                  beginners. Our automated trading bot handles the complex
                  decisions while you focus on your investment goals.
                </p>
                <div className="flex flex-col gap-2 sm:flex-row">
                  {isAuthenticated ? (
                    <Link
                      href={SITE_CONFIG.links.dashboard}
                      className="w-full sm:w-auto"
                    >
                      <Button
                        className={`w-full sm:w-auto bg-[${SITE_CONFIG.colors.primary}] hover:bg-[${SITE_CONFIG.colors.primaryHover}]`}
                      >
                        Go to Dashboard
                        <ArrowRight className="ml-2 h-4 w-4" />
                      </Button>
                    </Link>
                  ) : (
                    <Link
                      href={SITE_CONFIG.links.signup}
                      className="w-full sm:w-auto"
                    >
                      <Button
                        className={`w-full sm:w-auto bg-[${SITE_CONFIG.colors.primary}] hover:bg-[${SITE_CONFIG.colors.primaryHover}]`}
                      >
                        Get Started
                        <ArrowRight className="ml-2 h-4 w-4" />
                      </Button>
                    </Link>
                  )}
                  <Link href="#how-it-works" className="w-full sm:w-auto">
                    <Button
                      variant="outline"
                      className="w-full sm:w-auto bg-transparent"
                    >
                      Learn More
                    </Button>
                  </Link>
                </div>
              </div>
              <div className="flex justify-center mt-8 lg:mt-0">
                <img
                  src="/crypto-trading-dashboard.png"
                  alt="Trading Dashboard Preview"
                  className="rounded-lg object-cover border shadow-lg max-w-full h-auto"
                  width={500}
                  height={400}
                />
              </div>
            </div>
          </Container>
        </section>

        {/* Features Section */}
        <section id="features" className="w-full py-12 md:py-24 lg:py-32">
          <Container>
            <SectionHeader
              label="Features"
              title={`Why Choose ${SITE_CONFIG.name}?`}
              description="Our platform is designed specifically for beginners, with features that make trading accessible and less intimidating."
            />
            <div className="mx-auto grid max-w-5xl grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3 mt-12">
              {FEATURES.map((feature, index) => (
                <div
                  key={index}
                  className="flex flex-col items-center space-y-2 rounded-lg border p-6 shadow-sm"
                >
                  <div
                    className={`rounded-full bg-[${SITE_CONFIG.colors.accent}] p-3`}
                  >
                    <feature.icon
                      className={`h-6 w-6 text-[${SITE_CONFIG.colors.primary}]`}
                    />
                  </div>
                  <h3 className="text-xl font-bold">{feature.title}</h3>
                  <p className="text-center text-muted-foreground">
                    {feature.description}
                  </p>
                </div>
              ))}
            </div>
          </Container>
        </section>

        {/* How It Works Section */}
        <section
          id="how-it-works"
          className="w-full py-12 md:py-24 lg:py-32 bg-[#f8faf7] dark:bg-muted/50"
        >
          <Container>
            <SectionHeader
              label="Process"
              title={`How ${SITE_CONFIG.name} Works`}
              description="Get started with cryptocurrency trading in just a few simple steps."
            />
            <div className="mx-auto grid max-w-5xl gap-6 py-12 grid-cols-1 sm:grid-cols-2 lg:grid-cols-4">
              {STEPS.map((step, index) => (
                <div
                  key={index}
                  className="flex flex-col items-center space-y-2 rounded-lg border bg-background p-6 shadow-sm"
                >
                  <div
                    className={`flex h-12 w-12 items-center justify-center rounded-full bg-[${SITE_CONFIG.colors.primary}] text-white`}
                  >
                    {step.number}
                  </div>
                  <h3 className="text-xl font-bold">{step.title}</h3>
                  <p className="text-center text-muted-foreground">
                    {step.description}
                  </p>
                </div>
              ))}
            </div>
          </Container>
        </section>

        {/* Pricing Section */}
        <section id="pricing" className="w-full py-12 md:py-24 lg:py-32">
          <Container>
            <SectionHeader
              label="Pricing"
              title="Choose Your Trading Plan"
              description="Select a plan that matches your trading ambitions."
            />
            <div className="mx-auto grid max-w-5xl grid-cols-1 gap-6 md:grid-cols-3 lg:gap-8 py-12">
              {PLANS.map((plan, index) => (
                <div
                  key={index}
                  className={`flex flex-col rounded-lg border shadow-sm relative ${
                    plan.popular
                      ? `border-2 border-[${SITE_CONFIG.colors.primary}]`
                      : ""
                  }`}
                >
                  {plan.popular && (
                    <div
                      className={`absolute top-0 right-0 rounded-bl-lg rounded-tr-lg bg-[${SITE_CONFIG.colors.primary}] px-3 py-1 text-xs font-bold text-white`}
                    >
                      Popular
                    </div>
                  )}
                  <div className="p-6">
                    <h3 className="text-2xl font-bold">{plan.name}</h3>
                    <div className="mt-4 text-3xl font-bold">
                      ${plan.price}
                      <span className="text-base font-normal text-muted-foreground">
                        /month
                      </span>
                    </div>
                    <p className="mt-2 text-muted-foreground">
                      {plan.description}
                    </p>
                    <ul className="mt-6 space-y-2">
                      {plan.features.map((feature, i) => (
                        <li key={i} className="flex items-center">
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            width="24"
                            height="24"
                            viewBox="0 0 24 24"
                            fill="none"
                            stroke="currentColor"
                            strokeWidth="2"
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            className={`mr-2 h-4 w-4 text-[${SITE_CONFIG.colors.primary}]`}
                          >
                            <polyline points="20 6 9 17 4 12"></polyline>
                          </svg>
                          {feature}
                        </li>
                      ))}
                    </ul>
                  </div>
                  <div className="flex flex-col p-6 pt-0">
                    {isAuthenticated ? (
                      <Link
                        href={`${SITE_CONFIG.links.pricing}?plan=${plan.href}`}
                      >
                        <Button
                          className={`w-full bg-[${SITE_CONFIG.colors.primary}] hover:bg-[${SITE_CONFIG.colors.primaryHover}]`}
                        >
                          Upgrade Now
                        </Button>
                      </Link>
                    ) : (
                      <Link
                        href={`${SITE_CONFIG.links.signup}?plan=${plan.href}`}
                      >
                        <Button
                          className={`w-full bg-[${SITE_CONFIG.colors.primary}] hover:bg-[${SITE_CONFIG.colors.primaryHover}]`}
                        >
                          Get Started
                        </Button>
                      </Link>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </Container>
        </section>

        {/* FAQ Section */}
        <section
          id="faq"
          className="w-full py-12 md:py-24 lg:py-32 bg-[#f8faf7] dark:bg-muted/50"
        >
          <Container>
            <SectionHeader
              label="FAQs"
              title="Frequently Asked Questions"
              description="Find answers to common questions about our trading platform."
            />
            <div className="mx-auto grid max-w-5xl gap-6 py-12 lg:grid-cols-2">
              {FAQS.map((faq, index) => (
                <div
                  key={index}
                  className="rounded-lg border bg-background p-6 shadow-sm"
                >
                  <h3 className="text-lg font-bold">{faq.question}</h3>
                  <p className="mt-2 text-muted-foreground">{faq.answer}</p>
                </div>
              ))}
            </div>
          </Container>
        </section>
      </main>

      {/* Footer */}
      <footer className="flex flex-col gap-2 sm:flex-row py-6 w-full shrink-0 items-center border-t">
        <Container className="flex flex-col sm:flex-row w-full items-center justify-between">
          <p className="text-xs text-muted-foreground">
            © 2025 {SITE_CONFIG.name}. All rights reserved.
          </p>
          <nav className="sm:ml-auto flex gap-4 sm:gap-6">
            <Link
              className="text-xs hover:underline underline-offset-4"
              href="#"
            >
              Terms of Service
            </Link>
            <Link
              className="text-xs hover:underline underline-offset-4"
              href="#"
            >
              Privacy
            </Link>
          </nav>
        </Container>
      </footer>
      <BottomNav />
    </div>
  );
}
