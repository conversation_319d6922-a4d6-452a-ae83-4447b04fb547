"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Calendar, Filter, ArrowLeft } from "lucide-react"
import { useRouter } from "next/navigation"
import { BottomNav } from "@/components/bottom-nav"
import { Container } from "@/components/container"
import { Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { useTradingAccount } from "@/contexts/trading-account-context"

export default function TradingHistoryPage() {
  const router = useRouter()
  const [activeTab, setActiveTab] = useState("all")
  const [timeframe, setTimeframe] = useState("all")

  // Mock trading history data
  const tradingHistory = [
    {
      id: 1,
      type: "Buy",
      amount: "0.05 BTC",
      price: "$64,230.45",
      date: "May 15, 2025",
      time: "14:32:21",
      profit: null,
      status: "completed",
    },
    {
      id: 2,
      type: "Sell",
      amount: "0.03 BTC",
      price: "$65,120.78",
      date: "May 14, 2025",
      time: "09:15:43",
      profit: "+$267.89",
      status: "completed",
    },
    {
      id: 3,
      type: "Buy",
      amount: "0.02 BTC",
      price: "$63,890.12",
      date: "May 13, 2025",
      time: "16:45:12",
      profit: null,
      status: "completed",
    },
    {
      id: 4,
      type: "Sell",
      amount: "0.04 BTC",
      price: "$64,567.34",
      date: "May 12, 2025",
      time: "11:22:05",
      profit: "+$312.45",
      status: "completed",
    },
    {
      id: 5,
      type: "Buy",
      amount: "0.01 BTC",
      price: "$64,123.78",
      date: "May 11, 2025",
      time: "08:34:19",
      profit: null,
      status: "completed",
    },
    {
      id: 6,
      type: "Sell",
      amount: "0.02 BTC",
      price: "$63,987.45",
      date: "May 10, 2025",
      time: "15:56:32",
      profit: "-$45.67",
      status: "completed",
    },
    {
      id: 7,
      type: "Buy",
      amount: "0.03 BTC",
      price: "$65,432.10",
      date: "May 09, 2025",
      time: "10:12:45",
      profit: null,
      status: "completed",
    },
  ]

  // Filter trades based on active tab
  const filteredTrades = tradingHistory.filter((trade) => {
    if (activeTab === "all") return true
    if (activeTab === "buy") return trade.type === "Buy"
    if (activeTab === "sell") return trade.type === "Sell"
    return true
  })

  return (
    <div className="flex min-h-screen flex-col">
      <main className="flex-1 py-6 pb-20 md:pb-6">
        <Container className="max-w-6xl">
          <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6 gap-4">
            <div>
              <div className="flex items-center gap-4 mb-4">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => router.back()}
                  className="flex items-center gap-2"
                >
                  <ArrowLeft className="h-4 w-4" />
                  Back
                </Button>
              </div>
              <h1 className="text-2xl font-bold">Trading History</h1>
              <p className="text-gray-500">View your past BTC/USDT trades</p>
            </div>
            <div className="flex items-center gap-2 w-full md:w-auto">
              <Select value={timeframe} onValueChange={setTimeframe}>
                <SelectTrigger className="w-full md:w-[180px]">
                  <SelectValue placeholder="Select timeframe" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Time</SelectItem>
                  <SelectItem value="week">This Week</SelectItem>
                  <SelectItem value="month">This Month</SelectItem>
                  <SelectItem value="year">This Year</SelectItem>
                </SelectContent>
              </Select>
              <Button variant="outline" size="icon">
                <Calendar className="h-4 w-4" />
              </Button>
            </div>
          </div>

          <Card>
            <CardHeader className="pb-2">
              <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
                <div>
                  <CardTitle>Trade History</CardTitle>
                  <CardDescription>Your past BTC/USDT trades</CardDescription>
                </div>
                <div className="flex items-center gap-2">
                  <Button variant="outline" size="sm" className="hidden md:flex">
                    <Filter className="h-4 w-4 mr-2" />
                    Filter
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <Tabs defaultValue="all" value={activeTab} onValueChange={setActiveTab} className="mb-6">
                <TabsList className="grid w-full grid-cols-3">
                  <TabsTrigger value="all">All Trades</TabsTrigger>
                  <TabsTrigger value="buy">Buy Orders</TabsTrigger>
                  <TabsTrigger value="sell">Sell Orders</TabsTrigger>
                </TabsList>
              </Tabs>

              <div className="rounded-md border overflow-x-auto">
                <div className="min-w-[700px]">
                  <div className="grid grid-cols-7 gap-4 p-4 border-b bg-gray-50 font-medium text-sm">
                    <div>Type</div>
                    <div>Amount</div>
                    <div>Price</div>
                    <div>Date</div>
                    <div>Time</div>
                    <div>Status</div>
                    <div>Profit/Loss</div>
                  </div>
                  {filteredTrades.length > 0 ? (
                    filteredTrades.map((trade) => (
                      <div
                        key={trade.id}
                        className="grid grid-cols-7 gap-4 p-4 border-b last:border-0 text-sm hover:bg-gray-50"
                      >
                        <div>
                          <span
                            className={`px-2 py-0.5 rounded-full text-xs ${
                              trade.type === "Buy" ? "bg-green-100 text-green-800" : "bg-red-100 text-red-800"
                            }`}
                          >
                            {trade.type}
                          </span>
                        </div>
                        <div>{trade.amount}</div>
                        <div>{trade.price}</div>
                        <div>{trade.date}</div>
                        <div>{trade.time}</div>
                        <div>
                          <span className="px-2 py-0.5 rounded-full text-xs bg-blue-100 text-blue-800 capitalize">
                            {trade.status}
                          </span>
                        </div>
                        <div
                          className={
                            trade.profit ? (trade.profit.startsWith("+") ? "text-green-500" : "text-red-500") : ""
                          }
                        >
                          {trade.profit || "-"}
                        </div>
                      </div>
                    ))
                  ) : (
                    <div className="p-8 text-center text-gray-500">No trades found for the selected filter.</div>
                  )}
                </div>
              </div>

              <div className="flex justify-between items-center mt-4">
                <p className="text-sm text-gray-500">
                  Showing {filteredTrades.length} of {tradingHistory.length} trades
                </p>
                <div className="flex items-center gap-2">
                  <Button variant="outline" size="sm" disabled>
                    Previous
                  </Button>
                  <Button variant="outline" size="sm" disabled>
                    Next
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </Container>
      </main>
      <BottomNav />
    </div>
  )
}
