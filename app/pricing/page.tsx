"use client"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { BarChart2, Check, CreditCard, ArrowLeft } from "lucide-react"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Label } from "@/components/ui/label"
import { MobileNav } from "@/components/mobile-nav"
import { BottomNav } from "@/components/bottom-nav"
import { Container } from "@/components/container"
import { useUser } from "@/contexts/user-provider"

export default function PricingPage() {
  const router = useRouter()
  const { isAuthenticated } = useUser()
  const [selectedPlan, setSelectedPlan] = useState("enthusiast")
  const [isSubscribing, setIsSubscribing] = useState(false)

  // Redirect to login if not authenticated
  useEffect(() => {
    if (!isAuthenticated) {
      router.push("/auth/login")
    }
  }, [isAuthenticated, router])

  const plans = [
    {
      id: "noob",
      name: "Noob",
      price: 150,
      description: "Perfect for beginners starting their trading journey.",
      features: ["Up to $5,000 monthly trading volume", "Basic trading strategies", "Email support"],
    },
    {
      id: "enthusiast",
      name: "Enthusiast",
      price: 500,
      description: "For traders looking to increase their investment.",
      features: [
        "Up to $25,000 monthly trading volume",
        "Advanced trading strategies",
        "Priority email support",
        "Weekly performance reports",
      ],
      popular: true,
    },
    {
      id: "trader",
      name: "Trader",
      price: 1000,
      description: "For serious traders with larger investment capital.",
      features: [
        "Up to $100,000 monthly trading volume",
        "Premium trading strategies",
        "24/7 dedicated support",
        "Daily performance reports",
        "Custom strategy consultation",
      ],
    },
  ]

  const handleSubscribe = () => {
    setIsSubscribing(true)

    // Simulate payment processing
    setTimeout(() => {
      router.push("/dashboard")
    }, 2000)
  }

  // If not authenticated, show nothing while redirecting
  if (!isAuthenticated) {
    return null
  }

  return (
    <div className="flex min-h-screen flex-col">
      <header className="h-16 flex items-center border-b">
        <Container className="flex w-full items-center justify-between">
          <div className="flex items-center gap-4">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => router.back()}
              className="flex items-center gap-2"
            >
              <ArrowLeft className="h-4 w-4" />
              Back
            </Button>
            <div className="flex items-center gap-2">
              <BarChart2 className="h-6 w-6 text-[#245c1a]" />
              <span className="text-xl font-bold">TradeSmart</span>
            </div>
          </div>
          <div>
            <MobileNav />
          </div>
        </Container>
      </header>
      <main className="flex-1 py-12 pb-20 md:pb-12">
        <Container className="max-w-5xl">
          <div className="mb-8 text-center">
            <h1 className="text-3xl font-bold">Upgrade Your Trading Plan</h1>
            <p className="text-gray-500 mt-2">Select the plan that matches your trading ambitions</p>
          </div>

          <div className="grid gap-6 md:grid-cols-3 mb-8">
            {plans.map((plan) => (
              <Card key={plan.id} className={`relative ${selectedPlan === plan.id ? "border-[#245c1a] border-2" : ""}`}>
                {plan.popular && (
                  <div className="absolute top-0 right-0 rounded-bl-lg rounded-tr-lg bg-[#245c1a] px-3 py-1 text-xs font-bold text-white">
                    Popular
                  </div>
                )}
                <CardHeader>
                  <CardTitle>{plan.name}</CardTitle>
                  <CardDescription>{plan.description}</CardDescription>
                  <div className="mt-4 text-3xl font-bold">
                    ${plan.price}
                    <span className="text-base font-normal text-gray-500">/month</span>
                  </div>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-2">
                    {plan.features.map((feature, index) => (
                      <li key={index} className="flex items-start">
                        <Check className="mr-2 h-4 w-4 mt-1 text-[#245c1a]" />
                        <span>{feature}</span>
                      </li>
                    ))}
                  </ul>
                </CardContent>
                <CardFooter>
                  <RadioGroup value={selectedPlan} onValueChange={setSelectedPlan} className="w-full">
                    <div className="flex items-center space-x-2">
                      <RadioGroupItem value={plan.id} id={`plan-${plan.id}`} />
                      <Label htmlFor={`plan-${plan.id}`} className="flex-1">
                        Select Plan
                      </Label>
                    </div>
                  </RadioGroup>
                </CardFooter>
              </Card>
            ))}
          </div>

          <div className="rounded-lg border p-6 mb-8">
            <div className="flex items-start gap-4">
              <div className="rounded-full bg-[#e6f0e4] p-3">
                <CreditCard className="h-6 w-6 text-[#245c1a]" />
              </div>
              <div>
                <h3 className="text-lg font-bold mb-2">Payment Information</h3>
                <p className="text-gray-500 mb-4">
                  Your subscription will begin immediately after payment processing. You can cancel or change your plan
                  at any time.
                </p>
                <Button onClick={handleSubscribe} className="bg-[#245c1a] hover:bg-[#1a4513]" disabled={isSubscribing}>
                  {isSubscribing
                    ? "Processing..."
                    : `Subscribe to ${plans.find((p) => p.id === selectedPlan)?.name} Plan`}
                </Button>
              </div>
            </div>
          </div>

          <div className="text-center">
            <p className="text-sm text-gray-500">
              Need help choosing a plan?{" "}
              <Link href="#" className="text-[#245c1a] hover:underline">
                Contact our support team
              </Link>
            </p>
          </div>
        </Container>
      </main>
      <BottomNav />
    </div>
  )
}
