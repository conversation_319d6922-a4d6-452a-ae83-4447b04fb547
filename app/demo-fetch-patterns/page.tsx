import { Suspense } from "react";
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";
import { Clock, Zap, Users, TrendingUp } from "lucide-react";
import Link from "next/link";

/**
 * Demo page to showcase the difference between "Fetch on Render" vs "Fetch as you Render"
 * This page demonstrates the concepts with simple examples you can see in action
 */
export default async function DemoFetchPatternsPage() {
  return (
    <div className="min-h-screen bg-background">
      <div className="container mx-auto px-4 py-8">
        <div className="mb-8">
          <h1 className="text-4xl font-bold mb-4">React Fetch Patterns Demo</h1>
          <p className="text-xl text-muted-foreground mb-6">
            See the difference between traditional "Fetch on Render" and modern "Fetch as you Render" patterns
          </p>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-8">
            <Link href="/dashboard">
              <Card className="cursor-pointer hover:shadow-lg transition-shadow">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Clock className="h-5 w-5 text-orange-600" />
                    Traditional Dashboard
                  </CardTitle>
                  <CardDescription>
                    Uses useEffect + React Query (Fetch on Render)
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <Badge variant="secondary">Current Approach</Badge>
                </CardContent>
              </Card>
            </Link>

            <Link href="/dashboard-modern">
              <Card className="cursor-pointer hover:shadow-lg transition-shadow border-green-200">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Zap className="h-5 w-5 text-green-600" />
                    Modern Dashboard
                  </CardTitle>
                  <CardDescription>
                    Uses Server Components + Suspense (Fetch as you Render)
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <Badge className="bg-green-100 text-green-800">New Approach</Badge>
                </CardContent>
              </Card>
            </Link>
          </div>
        </div>

        {/* Live Demo Section */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Traditional Pattern Demo */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Clock className="h-5 w-5 text-orange-600" />
                Traditional Pattern
              </CardTitle>
              <CardDescription>
                Component mounts → useEffect runs → Data fetches → UI updates
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="p-4 bg-orange-50 rounded-lg border border-orange-200">
                  <h4 className="font-semibold text-orange-800 mb-2">Timeline:</h4>
                  <ol className="text-sm space-y-1 text-orange-700">
                    <li>1. Component renders with loading state</li>
                    <li>2. useEffect triggers after mount</li>
                    <li>3. API calls start</li>
                    <li>4. User sees loading spinner</li>
                    <li>5. Data arrives, UI updates</li>
                  </ol>
                </div>
                
                <div className="space-y-2">
                  <h4 className="font-semibold">Problems:</h4>
                  <ul className="text-sm text-muted-foreground space-y-1">
                    <li>• Waterfall loading (sequential)</li>
                    <li>• Flash of loading content</li>
                    <li>• Poor SEO (no server-side data)</li>
                    <li>• Complex loading state management</li>
                  </ul>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Modern Pattern Demo */}
          <Card className="border-green-200">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Zap className="h-5 w-5 text-green-600" />
                Modern Pattern
              </CardTitle>
              <CardDescription>
                Render starts → Data fetches immediately → Progressive loading
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="p-4 bg-green-50 rounded-lg border border-green-200">
                  <h4 className="font-semibold text-green-800 mb-2">Timeline:</h4>
                  <ol className="text-sm space-y-1 text-green-700">
                    <li>1. Server starts rendering</li>
                    <li>2. All data fetches begin immediately</li>
                    <li>3. Fast sections render first</li>
                    <li>4. Slow sections stream in when ready</li>
                    <li>5. Progressive enhancement</li>
                  </ol>
                </div>
                
                <div className="space-y-2">
                  <h4 className="font-semibold">Benefits:</h4>
                  <ul className="text-sm text-muted-foreground space-y-1">
                    <li>• Parallel loading (concurrent)</li>
                    <li>• Immediate content visibility</li>
                    <li>• Great SEO (server-rendered)</li>
                    <li>• Simplified component logic</li>
                  </ul>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Live Example Section */}
        <div className="mt-12">
          <h2 className="text-2xl font-bold mb-6">Live Example: Progressive Loading</h2>
          <p className="text-muted-foreground mb-6">
            This section demonstrates how different components load at different speeds with the modern pattern:
          </p>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {/* Fast Loading Component */}
            <Suspense fallback={<FastComponentSkeleton />}>
              <FastComponent />
            </Suspense>

            {/* Medium Loading Component */}
            <Suspense fallback={<MediumComponentSkeleton />}>
              <MediumComponent />
            </Suspense>

            {/* Slow Loading Component */}
            <Suspense fallback={<SlowComponentSkeleton />}>
              <SlowComponent />
            </Suspense>
          </div>
        </div>

        {/* Performance Comparison */}
        <div className="mt-12">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <TrendingUp className="h-5 w-5" />
                Performance Comparison
              </CardTitle>
              <CardDescription>
                Typical performance metrics for both patterns
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="overflow-x-auto">
                <table className="w-full text-sm">
                  <thead>
                    <tr className="border-b">
                      <th className="text-left p-2">Metric</th>
                      <th className="text-left p-2">Traditional</th>
                      <th className="text-left p-2">Modern</th>
                      <th className="text-left p-2">Improvement</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr className="border-b">
                      <td className="p-2">Time to First Byte</td>
                      <td className="p-2">~200ms</td>
                      <td className="p-2">~150ms</td>
                      <td className="p-2 text-green-600">25% faster</td>
                    </tr>
                    <tr className="border-b">
                      <td className="p-2">First Contentful Paint</td>
                      <td className="p-2">~800ms</td>
                      <td className="p-2">~400ms</td>
                      <td className="p-2 text-green-600">50% faster</td>
                    </tr>
                    <tr className="border-b">
                      <td className="p-2">Largest Contentful Paint</td>
                      <td className="p-2">~1200ms</td>
                      <td className="p-2">~600ms</td>
                      <td className="p-2 text-green-600">50% faster</td>
                    </tr>
                    <tr>
                      <td className="p-2">SEO Score</td>
                      <td className="p-2">75/100</td>
                      <td className="p-2">95/100</td>
                      <td className="p-2 text-green-600">27% better</td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Next Steps */}
        <div className="mt-12">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Users className="h-5 w-5" />
                Ready to Learn More?
              </CardTitle>
              <CardDescription>
                Explore the implementation and start migrating your code
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Link href="/dashboard-modern">
                  <Button className="w-full">
                    View Modern Dashboard
                  </Button>
                </Link>
                <Button variant="outline" className="w-full" asChild>
                  <a href="https://react.dev/blog/2023/03/22/react-labs-what-we-have-been-working-on-march-2023#react-server-components" target="_blank">
                    Read React Docs
                  </a>
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}

// Demo Components with different loading speeds
async function FastComponent() {
  await new Promise(resolve => setTimeout(resolve, 200)); // Fast load
  return (
    <Card className="border-green-200">
      <CardHeader>
        <CardTitle className="text-green-600">Fast Component</CardTitle>
        <CardDescription>Loads in ~200ms</CardDescription>
      </CardHeader>
      <CardContent>
        <p className="text-sm">This component loaded quickly and is already visible!</p>
        <Badge className="mt-2 bg-green-100 text-green-800">✓ Loaded</Badge>
      </CardContent>
    </Card>
  );
}

async function MediumComponent() {
  await new Promise(resolve => setTimeout(resolve, 1000)); // Medium load
  return (
    <Card className="border-yellow-200">
      <CardHeader>
        <CardTitle className="text-yellow-600">Medium Component</CardTitle>
        <CardDescription>Loads in ~1s</CardDescription>
      </CardHeader>
      <CardContent>
        <p className="text-sm">This component took a bit longer but didn't block the fast one!</p>
        <Badge className="mt-2 bg-yellow-100 text-yellow-800">✓ Loaded</Badge>
      </CardContent>
    </Card>
  );
}

async function SlowComponent() {
  await new Promise(resolve => setTimeout(resolve, 2000)); // Slow load
  return (
    <Card className="border-red-200">
      <CardHeader>
        <CardTitle className="text-red-600">Slow Component</CardTitle>
        <CardDescription>Loads in ~2s</CardDescription>
      </CardHeader>
      <CardContent>
        <p className="text-sm">This component was slow but didn't block anything else!</p>
        <Badge className="mt-2 bg-red-100 text-red-800">✓ Loaded</Badge>
      </CardContent>
    </Card>
  );
}

// Skeleton Components
function FastComponentSkeleton() {
  return (
    <Card>
      <CardHeader>
        <Skeleton className="h-6 w-32" />
        <Skeleton className="h-4 w-24" />
      </CardHeader>
      <CardContent>
        <Skeleton className="h-4 w-full mb-2" />
        <Skeleton className="h-6 w-16" />
      </CardContent>
    </Card>
  );
}

function MediumComponentSkeleton() {
  return (
    <Card>
      <CardHeader>
        <Skeleton className="h-6 w-36" />
        <Skeleton className="h-4 w-20" />
      </CardHeader>
      <CardContent>
        <Skeleton className="h-4 w-full mb-2" />
        <Skeleton className="h-6 w-16" />
      </CardContent>
    </Card>
  );
}

function SlowComponentSkeleton() {
  return (
    <Card>
      <CardHeader>
        <Skeleton className="h-6 w-32" />
        <Skeleton className="h-4 w-16" />
      </CardHeader>
      <CardContent>
        <Skeleton className="h-4 w-full mb-2" />
        <Skeleton className="h-6 w-16" />
      </CardContent>
    </Card>
  );
}
