/**
 * TypeScript interfaces for onboarding flow payload objects
 * These replace FormData usage with structured, type-safe objects
 */

export interface ActionResult {
  success: boolean;
  message?: string;
  data?: any;
  error?: string;
}

// ==========================================
// User Type and Beginner Flow Types
// ==========================================

export type UserType = "beginner" | "experienced";

export type ApprovalStatus = "pending" | "approved" | "rejected";

export interface BeginnerApplication {
  userEmail: string;
  userName: string;
  inviteCode: string;
  userType: UserType;
  approvalStatus: ApprovalStatus;
  submittedAt: string;
  reviewedAt?: string;
  reviewedBy?: string;
  rejectionReason?: string;
}



export interface InviteCodeValidationPayload {
  inviteCode: string;
}

export interface InviteCodeSubmissionResult extends ActionResult {
  data?: {
    applicationId: string;
    approvalStatus: ApprovalStatus;
  };
}

export interface InviteCodeValidationResult extends ActionResult {
  data?: {
    isValid: boolean;
    codeDetails?: {
      createdBy: string;
      createdAt: string;
      usageCount: number;
      maxUses?: number;
    };
  };
}

// ==========================================
// Payload Interfaces for Each Step
// ==========================================

export interface ProxySetupPayload {
  username?: string;
  password?: string;
  host: string;
  port: number;
  type: "http" | "socks5";
}

export interface ExchangeConnectionPayload {
  apiKey: string;
  secretKey: string;
  exchangeEmail: string;
  exchange: string;
  name: string;
  proxyId?: string;
}

export interface BinanceConnectionPayload {
  apiKey: string;
  secretKey: string;
  binanceEmail: string;
  proxyId?: string;
}

export interface ExchangeTestPayload {
  owner: string;
  exchange: string;
}

export interface SecretKeyPayload {
  secretKey: string;
}

export interface TradingSettingsPayload {
  tradingStrategy: string;
  riskLevel?: string;
  takeProfit?: string;
  stopLoss?: string;
  maxTradeSize?: string;
}

// ==========================================
// Response Data Types
// ==========================================

export interface ProxyCreationResponse {
  proxyId: string;
}

export interface ExchangeConnectionResponse {
  id: string;
  exchange: string;
  owner: string;
  email: string;
  proxy?: string;
  userId: string;
  created: string;
}

export interface ExchangeTestResponse {
  connected: boolean;
  symbolCount: number;
  symbols: string[];
}

export interface SecretKeyResponse {
  secretKeyStored: boolean;
}

export interface TradingSettingsResponse {
  settings: {
    tradingStrategy: string;
    riskLevel?: string;
    takeProfit?: string;
    stopLoss?: string;
    maxTradeSize?: string;
  };
}

// ==========================================
// Typed Action Results
// ==========================================

export interface ProxyActionResult extends ActionResult {
  data?: ProxyCreationResponse;
}

export interface ExchangeActionResult extends ActionResult {
  data?: ExchangeConnectionResponse;
}

export interface ExchangeTestActionResult extends ActionResult {
  data?: ExchangeTestResponse;
}

export interface SecretKeyActionResult extends ActionResult {
  data?: SecretKeyResponse;
}

export interface TradingSettingsActionResult extends ActionResult {
  data?: TradingSettingsResponse;
}
