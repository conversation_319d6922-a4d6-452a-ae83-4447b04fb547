import { redirect } from "next/navigation";
import { getLoggedInUser } from "../auth/actions";

export default async function OnboardingLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const user = await getLoggedInUser();
  if (!user) {
    redirect("/auth/login");
  }

  // Debug logging
  console.log("Onboarding Layout - User data:", {
    id: user.id,
    email: user.email,
    type: user.type,
    approved: user.approved,
    role: user.role,
    isAdmin: user.role === "admin",
    invitedBy: user.admin, // ID of admin who invited this user
    hasCompletedOnboarding: user.settings?.hasCompletedOnboarding
  });

  // Only redirect to dashboard if user is approved AND has completed onboarding (has type)
  if (user.approved && user.type && user.type.trim() !== "") {
    console.log("Onboarding Layout - Redirecting to dashboard: user approved and has type");
    redirect("/dashboard");
  }

  return <>{children}</>;
}
