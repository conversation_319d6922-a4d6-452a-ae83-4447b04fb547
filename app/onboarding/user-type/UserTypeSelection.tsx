"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Container } from "@/components/container";
import { MobileNav } from "@/components/mobile-nav";
import { BottomNav } from "@/components/bottom-nav";
import {
  Zap,
  GraduationCap,
  ArrowRight,
  Shield,
  Clock,
  Settings,
  Loader2,
} from "lucide-react";
import { updateUserType } from "./actions";

export function UserTypeSelection() {
  const router = useRouter();
  const [loadingType, setLoadingType] = useState<
    "beginner" | "experienced" | null
  >(null);

  const handleUserTypeSelect = async (userType: "beginner" | "experienced") => {
    setLoadingType(userType);

    try {
      const result = await updateUserType(userType);

      if (result) {
        router.push(`/onboarding/${userType}`);
        setTimeout(() => {
          setLoadingType(null);
        }, 5000);
      }
    } catch (error) {
      console.error("Error updating user type:", error);
      setLoadingType(null);
    }
  };
  function LoadingSpinner() {
    return <Loader2 className="h-6 w-6 animate-spin text-white" />;
  }

  return (
    <div className="flex min-h-screen flex-col">
      <header className="h-16 flex items-center border-b">
        <Container className="flex w-full items-center justify-between">
          <div className="flex items-center gap-2">
            <span className="text-xl font-bold">TradeSmart Setup</span>
          </div>
          <div className="flex items-center gap-4">
            <MobileNav />
          </div>
        </Container>
      </header>

      <main className="flex-1 py-8 pb-20 md:pb-8">
        <Container className="max-w-4xl">
          <div className="mb-8 text-center">
            <h1 className="text-4xl font-bold mb-4">Welcome to TradeSmart</h1>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              Choose your experience level to get started with the right setup
              process for you.
            </p>
          </div>

          <div className="grid md:grid-cols-2 gap-6 max-w-4xl mx-auto">
            {/* Beginner Option */}
            <Card
              className="relative overflow-hidden border-2 hover:border-[#245c1a] transition-all duration-200 cursor-pointer group"
              onClick={() => handleUserTypeSelect("beginner")}
            >
              <CardHeader className="pb-4">
                <div className="flex items-center gap-3 mb-2">
                  <div className="flex h-12 w-12 items-center justify-center rounded-full bg-blue-100 group-hover:bg-blue-200 transition-colors">
                    <GraduationCap className="h-6 w-6 text-blue-600" />
                  </div>
                  <div>
                    <CardTitle className="text-2xl">Beginner</CardTitle>
                    <CardDescription className="text-base">
                      New to trading
                    </CardDescription>
                  </div>
                </div>
              </CardHeader>

              <CardContent className="space-y-4">
                <p className="text-gray-600 mb-4">
                  Perfect for users who are new to automated trading and want a
                  simple getting started experience.
                </p>

                <div className="space-y-3">
                  <div className="flex items-center gap-3">
                    <Clock className="h-4 w-4 text-blue-600" />
                    <span className="text-sm text-gray-600">
                      Quick 2-minute setup
                    </span>
                  </div>
                  <div className="flex items-center gap-3">
                    <Shield className="h-4 w-4 text-blue-600" />
                    <span className="text-sm text-gray-600">
                      Invite code required
                    </span>
                  </div>
                  <div className="flex items-center gap-3">
                    <GraduationCap className="h-4 w-4 text-blue-600" />
                    <span className="text-sm text-gray-600">
                      Guided learning experience
                    </span>
                  </div>
                </div>

                <div className="pt-4">
                  <Button
                    className="w-full bg-blue-600 hover:bg-blue-700"
                    disabled={loadingType === "beginner"}
                  >
                    {loadingType === "beginner" ? (
                      <LoadingSpinner />
                    ) : (
                      <div className="flex flex-row items-center justify-center">
                        Get Started as Beginner Email
                        <ArrowRight className="ml-2 h-4 w-4" />
                      </div>
                    )}
                  </Button>
                </div>

                <div className="text-xs text-gray-500 bg-blue-50 p-3 rounded-lg">
                  <strong>What you'll need:</strong> Just an invite code from an
                  existing user to get started.
                </div>
              </CardContent>
            </Card>

            {/* Experienced Option */}
            <Card
              className="relative overflow-hidden border-2 hover:border-[#245c1a] transition-all duration-200 cursor-pointer group"
              onClick={() => handleUserTypeSelect("experienced")}
            >
              <CardHeader className="pb-4">
                <div className="flex items-center gap-3 mb-2">
                  <div className="flex h-12 w-12 items-center justify-center rounded-full bg-green-100 group-hover:bg-green-200 transition-colors">
                    <Zap className="h-6 w-6 text-[#245c1a]" />
                  </div>
                  <div>
                    <CardTitle className="text-2xl">Experienced</CardTitle>
                    <CardDescription className="text-base">
                      Ready to trade
                    </CardDescription>
                  </div>
                </div>
              </CardHeader>

              <CardContent className="space-y-4">
                <p className="text-gray-600 mb-4">
                  For users who have trading experience and want full control
                  over their automated trading setup.
                </p>

                <div className="space-y-3">
                  <div className="flex items-center gap-3">
                    <Settings className="h-4 w-4 text-[#245c1a]" />
                    <span className="text-sm text-gray-600">
                      Complete configuration
                    </span>
                  </div>
                  <div className="flex items-center gap-3">
                    <Shield className="h-4 w-4 text-[#245c1a]" />
                    <span className="text-sm text-gray-600">
                      Advanced security settings
                    </span>
                  </div>
                  <div className="flex items-center gap-3">
                    <Zap className="h-4 w-4 text-[#245c1a]" />
                    <span className="text-sm text-gray-600">
                      Immediate trading access
                    </span>
                  </div>
                </div>

                <div className="pt-4">
                  <Button
                    className="w-full bg-[#245c1a] hover:bg-[#1d4a15]"
                    disabled={loadingType === "experienced"}
                  >
                    {loadingType === "experienced" ? (
                      <LoadingSpinner />
                    ) : (
                      <div className="flex flex-row items-center justify-center">
                        Start Full Setup
                        <ArrowRight className="ml-2 h-4 w-4" />
                      </div>
                    )}
                  </Button>
                </div>

                <div className="text-xs text-gray-500 bg-green-50 p-3 rounded-lg">
                  <strong>What you'll need:</strong> Exchange API keys, proxy
                  settings, and trading preferences.
                </div>
              </CardContent>
            </Card>
          </div>

          <div className="mt-8 text-center">
            <p className="text-sm text-gray-500">
              Don't worry, you can always change your setup later in your
              account settings.
            </p>
          </div>
        </Container>
      </main>

      <BottomNav />
    </div>
  );
}
