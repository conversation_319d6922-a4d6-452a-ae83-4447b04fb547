"use server";

import { getLoggedInUser } from "@/app/auth/actions";
import { getDBService } from "@/lib/services/db";

export async function updateUserType(userType: "beginner" | "experienced") {
  try {
    const user = await getLoggedInUser();
    if (!user || !user.id) {
      throw new Error("User not logged in or missing id");
    }
    const dbService = await getDBService();
    await dbService.updateUserRecord({
      userId: user.id,
      updates: { type: userType },
    });
    return true;
  } catch (error) {
    console.error("Failed to update user type:", error);
    return false;
  }
}
