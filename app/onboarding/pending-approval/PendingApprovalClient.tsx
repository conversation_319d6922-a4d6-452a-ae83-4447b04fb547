"use client";

import { removeAdmin } from "@/app/auth/actions";
import { BottomNav } from "@/components/bottom-nav";
import { Container } from "@/components/container";
import { MobileNav } from "@/components/mobile-nav";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { useUser } from "@/contexts/user-provider";
import {
  AlertCircle,
  ArrowLeft,
  CheckCircle,
  Clock,
  Loader2,
  RefreshCw,
} from "lucide-react";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";

export function PendingApprovalClient() {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);
  const { approved, loading, error, user: authUser, refetch } = useUser();

  // Redirect to dashboard if approved
  useEffect(() => {
    if (authUser?.approved === true) {
      router.push("/dashboard");
    }
  }, [approved, router]);

  // Use initial user data if available, otherwise use hook data
  const currentUser = authUser;

  const handleBackToBeginner = async () => {
    if (currentUser?.admin) {
      setIsLoading(true);
      try {
        await removeAdmin(currentUser.id);
      } catch (error) {
        console.error(error);
        setIsLoading(false);
      }
    }
    router.push("/onboarding/beginner");
  };

  if (loading && !currentUser) {
    return (
      <div className="flex min-h-screen flex-col">
        <header className="h-16 flex items-center border-b">
          <Container className="flex w-full items-center justify-between">
            <span className="text-xl font-bold">Checking Status...</span>
            <MobileNav />
          </Container>
        </header>

        <main className="flex-1 flex items-center justify-center">
          <Card className="w-full max-w-md">
            <CardContent className="pt-6">
              <div className="flex flex-col items-center space-y-4">
                <Loader2 className="h-8 w-8 animate-spin text-blue-600" />
                <p className="text-sm text-gray-600">
                  Checking approval status...
                </p>
              </div>
            </CardContent>
          </Card>
        </main>

        <BottomNav />
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex min-h-screen flex-col">
        <header className="h-16 flex items-center border-b">
          <Container className="flex w-full items-center justify-between">
            <div className="flex items-center gap-2">
              <Button
                variant="ghost"
                size="icon"
                onClick={handleBackToBeginner}
              >
                <ArrowLeft className="h-5 w-5" />
              </Button>
              <span className="text-xl font-bold">Error</span>
            </div>
            <MobileNav />
          </Container>
        </header>

        <main className="flex-1 flex items-center justify-center">
          <Container className="max-w-md">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <AlertCircle className="h-5 w-5 text-red-600" />
                  <span>Error</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <p className="text-sm text-gray-600">{error}</p>
                <div className="flex space-x-2">
                  <Button onClick={refetch} className="flex-1">
                    <RefreshCw className="h-4 w-4 mr-2" />
                    Try Again
                  </Button>
                  <Button variant="outline" onClick={handleBackToBeginner}>
                    Back
                  </Button>
                </div>
              </CardContent>
            </Card>
          </Container>
        </main>

        <BottomNav />
      </div>
    );
  }

  if (authUser?.approved === true) {
    return (
      <div className="flex min-h-screen flex-col">
        <header className="h-16 flex items-center border-b">
          <Container className="flex w-full items-center justify-between">
            <span className="text-xl font-bold">Approved!</span>
            <MobileNav />
          </Container>
        </header>

        <main className="flex-1 flex items-center justify-center">
          <Container className="max-w-md">
            <Card>
              <CardHeader className="text-center">
                <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-green-100">
                  <CheckCircle className="h-6 w-6 text-green-600" />
                </div>
                <CardTitle>Welcome to TradeSmart!</CardTitle>
                <CardDescription>
                  Your account has been approved. Redirecting to dashboard...
                </CardDescription>
              </CardHeader>
              <CardContent className="text-center">
                <div className="flex items-center justify-center mb-4">
                  <Loader2 className="h-6 w-6 animate-spin text-blue-600" />
                </div>
                <p className="text-sm text-gray-600">
                  Taking you to the dashboard...
                </p>
              </CardContent>
            </Card>
          </Container>
        </main>

        <BottomNav />
      </div>
    );
  }

  // Show pending approval screen
  return (
    <div className="flex min-h-screen flex-col">
      <header className="h-16 flex items-center border-b">
        <Container className="flex w-full items-center justify-between">
          <div className="flex items-center gap-2">
            <Button variant="ghost" size="icon" onClick={handleBackToBeginner}>
              <ArrowLeft className="h-5 w-5" />
            </Button>
            <span className="text-xl font-bold">Approval Pending</span>
          </div>
          <MobileNav />
        </Container>
      </header>

      <main className="flex-1 py-8 pb-20 md:pb-8">
        <Container className="max-w-2xl">
          <Card>
            <CardHeader className="text-center">
              <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-yellow-100">
                <Clock className="h-6 w-6 text-yellow-600" />
              </div>
              <CardTitle>Application Under Review</CardTitle>
              <CardDescription>
                Your TradeSmart application is being reviewed
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="text-center space-y-2">
                <p className="text-sm text-gray-600">
                  Welcome,{" "}
                  <span className="font-medium">{currentUser?.name}</span>!
                </p>
                <p className="text-sm text-gray-600">
                  Your application has been submitted successfully and is
                  currently under review.
                </p>
                {currentUser?.invite_code && (
                  <p className="text-xs text-gray-500">
                    Invited with code:{" "}
                    <span className="font-mono bg-gray-100 px-2 py-1 rounded">
                      {currentUser.invite_code}
                    </span>
                  </p>
                )}
              </div>

              <div className="bg-blue-50 dark:bg-blue-900 border border-blue-200 dark:border-blue-700 rounded-lg p-4">
                <h4 className="text-sm font-medium text-blue-900 dark:text-blue-100 mb-2">
                  What happens next?
                </h4>
                <ul className="text-xs text-blue-800 dark:text-blue-200 space-y-1">
                  <li>• The person who invited you will be notified</li>
                  <li>• They will review and approve your application</li>
                  <li>• You'll be automatically redirected once approved</li>
                  <li>• This page updates in real-time - no need to refresh</li>
                  <li>
                    • You can continue with the full onboarding process after
                    approval
                  </li>
                </ul>
              </div>

              <div className="bg-green-50 dark:bg-green-900 border border-green-200 dark:border-green-700 rounded-lg p-4">
                <h4 className="text-sm font-medium text-green-900 dark:text-green-100 mb-2">
                  After approval, you'll get access to:
                </h4>
                <ul className="text-xs text-green-800 dark:text-green-200 space-y-1">
                  <li>• Complete onboarding with proxy setup</li>
                  <li>• Exchange account connection</li>
                  <li>• Automated trading configuration</li>
                  <li>• Full TradeSmart platform features</li>
                </ul>
              </div>

              <div className="flex flex-col space-y-2">
                <Button
                  variant="outline"
                  onClick={refetch}
                  className="w-full"
                  disabled={loading}
                >
                  {loading ? (
                    <>
                      <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                      Checking...
                    </>
                  ) : (
                    <>
                      <RefreshCw className="h-4 w-4 mr-2" />
                      Check Status
                    </>
                  )}
                </Button>
                <Button
                  variant="primary"
                  onClick={handleBackToBeginner}
                  disabled={isLoading}
                  className={`w-full text-sm transition-colors ${
                    !isLoading
                      ? "bg-transparent text-gray-700 hover:bg-gray-100"
                      : "bg-primary text-primary-foreground cursor-not-allowed"
                  }`}
                >
                  {isLoading ? (
                    <>
                      <Loader2 className="h-4 w-4 mr-2 animate-spin inline-block" />
                      Going back...
                    </>
                  ) : (
                    "Submit Different Code"
                  )}
                </Button>
              </div>

              <div className="text-center">
                <p className="text-xs text-gray-500">
                  Application submitted:{" "}
                  {currentUser?.created
                    ? new Date(currentUser.created).toLocaleDateString()
                    : "Unknown"}
                </p>
              </div>
            </CardContent>
          </Card>
        </Container>
      </main>

      <BottomNav />
    </div>
  );
}
