import { checkUserApprovalStatus } from "@/lib/actions/invite-actions";
import { redirect } from "next/navigation";
import { PendingApprovalClient } from "./PendingApprovalClient";
import { getLoggedInUser } from "@/app/auth/actions";

export default async function PendingApprovalPage() {
  // // Check approval status
  // const approvalStatus = await checkUserApprovalStatus();

  // console.log("approvalStatus", approvalStatus);

  // // If user is approved, redirect to experienced onboarding
  // if (approvalStatus.success && approvalStatus.approved) {
  //   redirect("/onboarding/experienced");
  // }

  // // If user not found in database, redirect to beginner onboarding
  // if (!approvalStatus.success) {
  //   redirect("/onboarding/beginner");
  // }

  const user = await getLoggedInUser();

  // make sure the user.type === beginner
  if (user?.type !== "beginner") {
    redirect("/onboarding/user-type");
  }

  //make sure the user has an admin
  if (!user?.admin) {
    redirect("/onboarding/user-type");
  }
  // make sure the admin is an admin
  if (user?.expand?.admin?.role !== "admin") {
    redirect("/onboarding/beginner");
  }
  //make sure the user is not approved and if approved it should be redirected to the dashboard
  if (user?.approved) {
    redirect("/dashboard");
  }


  return (
    // <PendingApprovalClient user={approvalStatus.user} userEmail={user.email} />
    <PendingApprovalClient />
  );
}
