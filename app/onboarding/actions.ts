'use server'

import { revalidatePath } from "next/cache";
import { redirect } from "next/navigation";
import { createUltimateService } from "@/lib/services/ultimate-service";
import { getCurrentUserEmail,getStepData } from "./lib/onboarding-data";
import type {
  ActionResult,
  ProxySetupPayload,
  ExchangeConnectionPayload,
  BinanceConnectionPayload,
  ExchangeTestPayload,
  SecretKeyPayload,
  TradingSettingsPayload,
  ProxyActionResult,
  ExchangeActionResult,
  ExchangeTestActionResult,
  SecretKeyActionResult,
  TradingSettingsActionResult,
} from "./types";

/**
 * Credential management for editing existing exchange accounts
 */
interface CredentialObject {
  name: string;
  email: string;
  exchange: string;
  api_key: string;
  api_secret: string;
}

interface DecryptCredentialsResult {
  success: boolean;
  credentials?: CredentialObject[];
  error?: string;
}

interface UpdateCredentialResult {
  success: boolean;
  message?: string;
  error?: string;
}

// Re-export types for convenience
export type {
  ActionResult,
  ProxySetupPayload,
  ExchangeConnectionPayload,
  BinanceConnectionPayload,
  ExchangeTestPayload,
  SecretKeyPayload,
  TradingSettingsPayload,
};

/**
 * Create a new proxy
 */
export async function createProxyAction(payload: ProxySetupPayload): Promise<ProxyActionResult> {
  try {
    const userEmail = await getCurrentUserEmail();
    const service = createUltimateService(userEmail);

    const { host, port, type, username, password } = payload;

    // Validate required fields
    if (!host || !port) {
      return {
        success: false,
        error: "Host and port are required"
      };
    }

    // Get user to get their ID
    const userResult = await service.getUserByEmail(userEmail);
    if (!userResult.success || !userResult.data) {
      return {
        success: false,
        error: "User not found"
      };
    }

    // Format the proxy string: username:password@host:port
    let ipAddress = `${host}:${port}`;
    if (username && password) {
      ipAddress = `${username}:${password}@${host}:${port}`;
    } else if (username) {
      ipAddress = `${username}@${host}:${port}`;
    }

    const result = await service.createProxy({
      ip_address: ipAddress,
      type: type,
      userId: userResult.data.id,
    });

    if (result.success) {
      revalidatePath('/onboarding');
      return {
        success: true,
        message: "Proxy created successfully",
        data: { proxyId: result.data?.id ?? "" }
      };
    }

    return {
      success: false,
      error: result.message || "Failed to create proxy"
    };
  } catch (error) {
    console.error("Error creating proxy:", error);
    return {
      success: false,
      error: "Failed to create proxy. Please try again."
    };
  }
}

/**
 * Connect exchange account (Binance or Bybit)
 */
export async function connectExchangeAccountAction(payload: ExchangeConnectionPayload): Promise<ExchangeActionResult> {
  try {
    const userEmail = await getCurrentUserEmail();
    const service = createUltimateService(userEmail);

    const { apiKey, secretKey, exchangeEmail, exchange, name, proxyId } = payload;

    // Validate required fields
    if (!apiKey || !secretKey || !exchangeEmail || !exchange || !name) {
      return {
        success: false,
        error: "API key, secret key, email, exchange, and name are required"
      };
    }

    // Validate exchange type
    if (!['binance', 'bybit'].includes(exchange.toLowerCase())) {
      return {
        success: false,
        error: "Unsupported exchange. Only Binance and Bybit are supported."
      };
    }

    // Get user to get their ID
    const userResult = await service.getUserByEmail(userEmail);
    if (!userResult.success || !userResult.data) {
      return {
        success: false,
        error: "User not found"
      };
    }

    // Create credential name using the provided name and exchange
    const credentialName = `${exchange.toLowerCase()}-${name.toLowerCase().replace(/[^a-z0-9]/g, '_')}`;

    // Add credentials using the service
    const credentialResult = await service.addCredential({
      name: credentialName,
      email: userEmail, // Use logged in user's email instead of exchange email
      exchange: exchange.toLowerCase(),
      api_key: apiKey,
      api_secret: secretKey,
    });

    const credentials = await service.getUserCredentials(exchangeEmail);
    console.log(credentials,credentialResult,"got here")

    if (!credentialResult.success) {
      return {
        success: false,
        error: credentialResult.message || "Failed to add credentials"
      };
    }

    const exchangeAccountResult = await service.createExchangeAccount({
      exchange: exchange.toLowerCase(),
      owner: credentialName,
      email: exchangeEmail,
      proxy: proxyId,
      userId: userResult.data.id,
    });

    if (exchangeAccountResult.success) {
      revalidatePath('/onboarding');
      return {
        success: true,
        message: `${exchange.charAt(0).toUpperCase() + exchange.slice(1)} account connected successfully`,
        data: exchangeAccountResult.data
          ? {
              ...exchangeAccountResult.data,
              userId: userResult.data.id,
            }
          : undefined
      };
    }

    return {
      success: false,
      error: exchangeAccountResult.message || "Failed to create exchange account"
    };
  } catch (error) {
    console.error(`Error connecting ${payload.exchange} account:`, error);
    return {
      success: false,
      error: `Failed to connect ${payload.exchange} account. Please try again.`
    };
  }
}

/**
 * @deprecated Use connectExchangeAccountAction instead
 * Connect Binance exchange account
 */
export async function connectBinanceAction(payload: BinanceConnectionPayload): Promise<ExchangeActionResult> {
  // Transform BinanceConnectionPayload to ExchangeConnectionPayload for backward compatibility
  const exchangePayload: ExchangeConnectionPayload = {
    apiKey: payload.apiKey,
    secretKey: payload.secretKey,
    exchangeEmail: payload.binanceEmail,
    exchange: 'binance',
    name: 'main', // Default name for backward compatibility
    proxyId: payload.proxyId,
  };

  return connectExchangeAccountAction(exchangePayload);
}

/**
 * Test exchange connection
 */
export async function testExchangeConnectionAction(payload: ExchangeTestPayload): Promise<ExchangeTestActionResult> {
  try {
    const userEmail = await getCurrentUserEmail();
    const service = createUltimateService(userEmail);

    const { owner, exchange } = payload;

    if (!owner || !exchange) {
      return {
        success: false,
        error: "Owner and exchange are required"
      };
    }

    // Test connection by getting symbols
    const symbolsResult = await service.getAllOpenSymbols(owner, exchange);

    if (symbolsResult.success && symbolsResult.data && Array.isArray(symbolsResult.data)) {
      return {
        success: true,
        message: "Connection successful",
        data: {
          connected: true,
          symbolCount: symbolsResult.data.length,
          symbols: symbolsResult.data.slice(0, 5) // Return first 5 symbols as proof
        }
      };
    }

    return {
      success: false,
      error: symbolsResult.message || "Failed to connect to exchange"
    };
  } catch (error) {
    console.error("Error testing connection:", error);
    return {
      success: false,
      error: "Failed to test connection. Please try again."
    };
  }
}

/**
 * Create account secret key with proper encryption
 */
export async function createSecretKeyAction(payload: SecretKeyPayload): Promise<SecretKeyActionResult> {
  try {
    const userEmail = await getCurrentUserEmail();
    const service = createUltimateService(userEmail);

    const { secretKey } = payload;

    if (!secretKey) {
      return {
        success: false,
        error: "Secret key is required"
      };
    }

    // Validate password strength
    const { validatePasswordStrength } = await import('@/lib/encryption');
    const validation = validatePasswordStrength(secretKey);
    if (!validation.success) {
      return {
        success: false,
        error: validation.message
      };
    }

    // Get user to get their ID
    const userResult = await service.getUserByEmail(userEmail);
    if (!userResult.success || !userResult.data) {
      return {
        success: false,
        error: "User not found"
      };
    }

    // Re-encrypt credentials with the new secret key
    const result = await reEncryptCredentialsAction(userResult.data, secretKey, service);

    if (result.success) {
      revalidatePath('/onboarding');
      return {
        success: true,
        message: "Secret key created successfully"
      };
    }

    return {
      success: false,
      error: result.error || "Failed to create secret key"
    };
  } catch (error) {
    console.error("Error creating secret key:", error);
    return {
      success: false,
      error: "Failed to create secret key. Please try again."
    };
  }
}

/**
 * Re-encrypt user credentials with new secret key using gbozee library (replaces manual encryption)
 */
async function reEncryptCredentialsAction(
  user: any,
  secretKey: string,
  service: any
): Promise<ActionResult> {
  try {
    // Import the getUltimateClient function to get the UltimateClient instance
    const { getUltimateClient } = await import('@/lib/client/ultimate-client');
    const client = await getUltimateClient({ email: user.email });

    // Get the app database instance to call changeUserPassword
    const appDb = client.getAppDatabase();
    const result = await appDb.changeUserPassword({ password: secretKey });

    if (result) {
      return {
        success: true,
        message: "Credentials re-encrypted successfully"
      };
    } else {
      return {
        success: false,
        error: "Failed to change user password"
      };
    }
  } catch (error) {
    console.error("Error re-encrypting credentials with library:", error);
    return {
      success: false,
      error: "Failed to re-encrypt credentials"
    };
  }
}

/**
 * Save trading settings
 */
export async function saveTradingSettingsAction(payload: TradingSettingsPayload): Promise<TradingSettingsActionResult> {
  try {
    const userEmail = await getCurrentUserEmail();
    const service = createUltimateService(userEmail);

    const tradingSettings = {
      tradingStrategy: payload.tradingStrategy,
      riskLevel: payload.riskLevel,
      takeProfit: payload.takeProfit,
      stopLoss: payload.stopLoss,
      maxTradeSize: payload.maxTradeSize,
    };

    // Get user to get their ID
    const userResult = await service.getUserByEmail(userEmail);
    if (!userResult.success || !userResult.data) {
      return {
        success: false,
        error: "User not found"
      };
    }

    // Update user settings with trading preferences
    const result = await service.updateUserSettings(userResult.data.id, {
      tradingSettings
    });

    if (result.success) {
      revalidatePath('/onboarding');
      return {
        success: true,
        message: "Trading settings saved successfully"
      };
    }

    return {
      success: false,
      error: result.message || "Failed to save trading settings"
    };
  } catch (error) {
    console.error("Error saving trading settings:", error);
    return {
      success: false,
      error: "Failed to save trading settings. Please try again."
    };
  }
}

/**
 * Complete onboarding process by setting hasCompletedOnboarding to true
 */
export async function completeOnboardingAction(): Promise<ActionResult> {
  try {
    const userEmail = await getCurrentUserEmail();
    const service = createUltimateService(userEmail);

    // Get user to get their ID
    const userResult = await service.getUserByEmail(userEmail);
    if (!userResult.success || !userResult.data) {
      return {
        success: false,
        error: "User not found"
      };
    }

    // Get current settings
    const currentSettings = typeof userResult.data.settings === 'string'
      ? JSON.parse(userResult.data.settings || '{}')
      : userResult.data.settings || {};

    // Update settings with hasCompletedOnboarding: true
    const updatedSettings = {
      ...currentSettings,
      hasCompletedOnboarding: true
    };

    // Update user settings
    const result = await service.updateUserSettings(userResult.data.id, updatedSettings);

    if (result.success) {
      revalidatePath('/onboarding');
      revalidatePath('/dashboard');
      redirect('/dashboard');
      // Note: redirect() throws an error to stop execution, so this line won't be reached
    }

    return {
      success: false,
      error: result.message || "Failed to complete onboarding"
    };
  } catch (error) {
    console.error("Error completing onboarding:", error);
    return {
      success: false,
      error: "Failed to complete onboarding. Please try again."
    };
  }
}

/**
 * Get step-specific data for onboarding
 */
export async function getStepDataAction(step: number): Promise<ActionResult> {
  try {
    const stepData = await getStepData(step);

    return {
      success: true,
      data: stepData
    };
  } catch (error) {
    console.error("Error fetching step data:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to fetch step data"
    };
  }
}

/**
 * Get user credentials for editing using gbozee library (replaces manual decryption)
 */
export async function decryptUserCredentialsAction(): Promise<DecryptCredentialsResult> {
  try {
    const userEmail = await getCurrentUserEmail();
    const service = createUltimateService(userEmail);

    // Use the Ultimate service to get credentials directly from the library
    // The library handles all decryption internally
    const credentialsResult = await service.getUserCredentials(userEmail);

    if (!credentialsResult.success || !credentialsResult.data) {
      return {
        success: false,
        error: credentialsResult.message || "Failed to get credentials from library"
      };
    }

    // Transform library credentials to CredentialObject format if needed
    const credentials: CredentialObject[] = credentialsResult.data.map((cred: any) => ({
      name: cred.name,
      email: cred.email,
      exchange: cred.exchange,
      api_key: cred.api_key,
      api_secret: cred.api_secret,
    }));

    return {
      success: true,
      credentials: credentials
    };
  } catch (error) {
    console.error("Error getting user credentials from library:", error);
    return {
      success: false,
      error: "Failed to get credentials"
    };
  }
}

/**
 * Update specific credential using gbozee library (replaces manual encryption)
 */
export async function updateCredentialAction(payload: {
  owner: string;
  apiKey: string;
  secretKey: string;
  exchangeEmail: string;
}): Promise<UpdateCredentialResult> {
  try {
    const userEmail = await getCurrentUserEmail();

    // Import the getUltimateClient function to get the UltimateClient instance
    const { getUltimateClient } = await import('@/lib/client/ultimate-client');
    const client = await getUltimateClient({ email: userEmail });

    // Get current credentials from the library
    const credentials = await client.getUserCredentials();

    // Find and update the specific credential
    const credentialIndex = credentials.findIndex((cred: any) => cred.name === payload.owner);

    if (credentialIndex === -1) {
      return {
        success: false,
        error: "Credential not found"
      };
    }

    // Update the credential in the array
    credentials[credentialIndex] = {
      ...credentials[credentialIndex],
      api_key: payload.apiKey,
      api_secret: payload.secretKey,
      email: payload.exchangeEmail,
    };

    // Get the user's current password to re-encrypt with the same password
    const user = await client.getUserByEmail();
    if (!user || !user.settings) {
      return {
        success: false,
        error: "User settings not found"
      };
    }

    const settings = typeof user.settings === 'string' ? JSON.parse(user.settings) : user.settings;
    if (!settings.password) {
      return {
        success: false,
        error: "User password not found"
      };
    }

    // Decrypt the current password to use for re-encryption
    const { decryptObject } = await import('@/lib/encryption');
    const serverSalt = process.env.SALT;
    if (!serverSalt) {
      return {
        success: false,
        error: "Server configuration error"
      };
    }

    const passwordResult = decryptObject<string>(settings.password, serverSalt);
    if (!passwordResult.success || !passwordResult.data) {
      return {
        success: false,
        error: "Failed to decrypt user password"
      };
    }

    // Use changeUserPassword to re-encrypt all credentials with the updated array
    // First, we need to temporarily store the updated credentials in the app's internal state
    // Since changeUserPassword gets credentials from getUserCredentials, we need to update them first

    // Unfortunately, there's no direct way to update credentials in the library
    // So we'll use the manual approach but with the library's changeUserPassword for final encryption
    const appDb = client.getAppDatabase();

    // Manually update the credentials in the database temporarily
    const pb = client.getPocketBase();
    const { encryptObject } = await import('@/lib/encryption');

    // Encrypt the updated credentials with the user's password
    const encryptedCredentialsResult = encryptObject(credentials, passwordResult.data);
    if (!encryptedCredentialsResult.success) {
      return {
        success: false,
        error: "Failed to encrypt updated credentials"
      };
    }

    // Update the user's settings with the new credentials
    await pb.collection("users").update(user.id, {
      settings: {
        ...settings,
        credentials: encryptedCredentialsResult.data,
      },
    });

    revalidatePath('/onboarding');
    return {
      success: true,
      message: "Credentials updated successfully"
    };
  } catch (error) {
    console.error("Error updating credential with library:", error);
    return {
      success: false,
      error: "Failed to update credential"
    };
  }
}

/**
 * Skip onboarding and go to dashboard
 */
export async function skipOnboardingAction(): Promise<never> {
  redirect('/dashboard');
}
