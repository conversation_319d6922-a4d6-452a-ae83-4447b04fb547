import { redirect } from "next/navigation";
import { BeginnerOnboardingClient } from "../beginner/BeginnerOnboardingClient";
import { OnboardingClient } from "../OnboardingClient";

interface PageProps {
  params: Promise<{ "user-type": string }>;
}

export default async function UserTypePage({ params }: PageProps) {
  const { "user-type": userType } = await params;

  if (userType !== "beginner" && userType !== "experienced") {
    redirect("/onboarding/user-type");
  }

  const onboardingData = {} as any;

  if (userType === "beginner") {
    return <BeginnerOnboardingClient />;
  }

  return <OnboardingClient data={onboardingData} />;
}
