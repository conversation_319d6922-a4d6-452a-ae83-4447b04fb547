"use server";

import { getLoggedInUser } from "@/app/auth/actions";
import { getDBService } from "@/lib/services/db";

export async function onValidateInviteCode({
  inviteCode,
  userName,
}: {
  inviteCode: string;
  userName?: string;
}) {
  try {
    // Check authentication
    const user = await getLoggedInUser();
    if (!user) {
      return {
        success: false,
        error: "Authentication required",
      };
    }

    // Validate invite code using PocketBase service

    const dbService = await getDBService();
    const inviteAdminOwner = await dbService.getInviteCodeOwner(inviteCode);
    if (!inviteAdminOwner) {
      return {
        success: false,
        error: "Invite code is missing or invalid",
      };
    }

    //Update the user name
    await dbService.updateUserRecord({
      userId: user.id,
      updates: { name: userName, admin: inviteAdminOwner.id },
    });

    return {
      success: true,
      message: "Invite code validated and user name updated successfully",
    };
  } catch (error) {
    console.error("Error submitting invite application:", error);
    return {
      success: false,
      error: "Could not update record",
    };
  }
}
