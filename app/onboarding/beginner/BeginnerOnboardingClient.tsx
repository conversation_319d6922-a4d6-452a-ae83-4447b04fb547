"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Container } from "@/components/container";
import { MobileNav } from "@/components/mobile-nav";
import { BottomNav } from "@/components/bottom-nav";
import {
  ChevronLeft,
  Gift,
  Clock,
  CheckCircle,
  AlertCircle,
  Loader2,
} from "lucide-react";
import { onValidateInviteCode } from "./actions";
import { useUser } from "@/contexts/user-provider";

export function BeginnerOnboardingClient() {
  const [inviteCode, setInviteCode] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitStatus, setSubmitStatus] = useState<
    "idle" | "success" | "error"
  >("idle");
  const [errorMessage, setErrorMessage] = useState("");

  const { user } = useUser();
  const router = useRouter();
  const [fullName, setFullName] = useState(user?.name || "");

  const handleBack = () => {
    router.push("/onboarding/user-type");
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!inviteCode.trim()) {
      setErrorMessage("Please enter an invite code");
      setSubmitStatus("error");
      return;
    }

    setIsSubmitting(true);
    setSubmitStatus("idle");
    setErrorMessage("");

    try {
      const result = await onValidateInviteCode({
        inviteCode: inviteCode.trim(),
        userName: fullName,
      });

      if (result.success) {
        setSubmitStatus("success");
        // Redirect to pending approval page after 2 seconds

        router.push("/onboarding/pending-approval");
      } else {
        setSubmitStatus("error");
        setErrorMessage(
          result.error || "Failed to submit application. Please try again."
        );
        setIsSubmitting(false);
      }
    } catch (error) {
      console.error("Error submitting invite:", error);
      setSubmitStatus("error");
      setErrorMessage(
        "Network error. Please check your connection and try again."
      );
      setIsSubmitting(false);
    }
  };

  return (
    <div className="flex min-h-screen flex-col">
      <header className="h-16 flex items-center border-b">
        <Container className="flex w-full items-center justify-between">
          <div className="flex items-center gap-2">
            <Button
              variant="ghost"
              size="icon"
              onClick={handleBack}
              className="mr-2"
            >
              <ChevronLeft className="h-5 w-5" />
              <span className="sr-only">Back</span>
            </Button>
            <span className="text-xl font-bold">Beginner Setup</span>
          </div>
          <div className="flex items-center gap-4">
            <MobileNav />
          </div>
        </Container>
      </header>

      <main className="flex-1 py-8 pb-20 md:pb-8">
        <Container className="max-w-2xl">
          <div className="mb-8 text-center">
            <h1 className="text-3xl font-bold mb-4">Welcome to TradeSmart</h1>
            <p className="text-gray-600 max-w-lg mx-auto">
              Enter your invite code to get started. Once approved, you'll have
              access to our automated trading platform.
            </p>
          </div>

          <Card className="max-w-lg mx-auto">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Gift className="h-5 w-5 text-blue-600" />
                Enter Invite Code
              </CardTitle>
              <CardDescription>
                You need an invite code from an existing TradeSmart user to join
                our platform.
              </CardDescription>
            </CardHeader>

            <CardContent>
              <form onSubmit={handleSubmit} className="space-y-6">
                <div className="space-y-4">
                  <div>
                    <Label htmlFor="email">Email Address</Label>
                    <Input
                      id="email"
                      type="email"
                      value={user?.email || ""}
                      disabled
                    />
                    <p className="text-xs text-gray-500 mt-1">
                      This is your registered email from your account
                    </p>
                  </div>

                  <div>
                    <Label htmlFor="name">Full Name</Label>
                    <Input
                      id="name"
                      type="text"
                      value={fullName}
                      onChange={(e) => setFullName(e.target.value)}
                      className="bg-gray-50"
                    />
                  </div>

                  <div>
                    <Label htmlFor="inviteCode">Invite Code *</Label>
                    <Input
                      id="inviteCode"
                      type="text"
                      placeholder="Enter your invite code"
                      value={inviteCode}
                      onChange={(e) => setInviteCode(e.target.value)}
                      disabled={isSubmitting}
                      className="font-mono"
                      required
                    />
                    <p className="text-xs text-gray-500 mt-1">
                      Ask an existing TradeSmart user for their invite code
                    </p>
                  </div>
                </div>

                {submitStatus === "error" && (
                  <Alert variant="destructive">
                    <AlertCircle className="h-4 w-4" />
                    <AlertDescription>{errorMessage}</AlertDescription>
                  </Alert>
                )}

                <div className="space-y-4">
                  <Button
                    type="submit"
                    className="w-full"
                    disabled={isSubmitting || !inviteCode.trim()}
                  >
                    {isSubmitting ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Submitting Application...
                      </>
                    ) : (
                      "Submit Application"
                    )}
                  </Button>

                  <div className="text-center">
                    <Button
                      type="button"
                      variant="ghost"
                      onClick={handleBack}
                      disabled={isSubmitting}
                    >
                      Back to User Type Selection
                    </Button>
                  </div>
                </div>
              </form>

              <div className="mt-6 p-4 bg-blue-50 rounded-lg">
                <h4 className="font-medium text-blue-900 mb-2">
                  What happens after submission?
                </h4>
                <ul className="text-sm text-blue-800 space-y-1">
                  <li>• Your invite code will be verified</li>
                  <li>• Your application will be reviewed (24-48 hours)</li>
                  <li>• You'll receive an email notification upon approval</li>
                  <li>• Full trading access will be granted once approved</li>
                </ul>
              </div>
            </CardContent>
          </Card>
        </Container>
      </main>

      <BottomNav />
    </div>
  );
}
