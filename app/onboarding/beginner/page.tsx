import { getLoggedInUser } from "@/app/auth/actions";
import { BeginnerOnboardingClient } from "./BeginnerOnboardingClient";
import { redirect } from "next/navigation";

export default async function BeginnerOnboardingPage() {
  const user = await getLoggedInUser();

  //redirect user with admin role and not approved to wait in pending approval
  if (!user?.approved && user?.expand?.admin?.role === "admin") {
    redirect("/onboarding/pending-approval");
  }

  return <BeginnerOnboardingClient />;
}
