'use client'

import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { MobileNav } from "@/components/mobile-nav";
import { BottomNav } from "@/components/bottom-nav";
import { Container } from "@/components/container";
import { Check, ChevronLeft, Network, LinkIcon, Key, Settings, Zap, Shield, DollarSign } from "lucide-react";
import { OnboardingData } from "./lib/onboarding-data";
import { ProxyStep } from "./components/proxy-step";
import { ExchangeStep } from "./components/exchange-step";
import { SecretKeyStep } from "./components/secret-key-step";
import { TradingSettingsStep } from "./components/trading-settings-step";
import { InvestmentStep } from "./components/investment-step";
import { ActivationStep } from "./components/activation-step";
import { skipOnboardingAction } from "./actions";

interface OnboardingClientProps {
  data: OnboardingData;
}

export function OnboardingClient({ data }: OnboardingClientProps) {
  const [currentStep, setCurrentStep] = useState(data.currentStep || 5);
  const [completedSteps, setCompletedSteps] = useState(data.completedSteps || []);
  const [selectedProxyId, setSelectedProxyId] = useState<string>(
   "" // data.proxies.length > 0 ? data.proxies[0].id : ""
  );
  const [investmentData, setInvestmentData] = useState<{
    subscriptionAmount?: number;
    paymentMethod?: string;
    selectedNetwork?: "TRC20" | "BEP20" | "ERC20";
    cardDetails?: any;
  }>({});

  const totalSteps = 6;
  const progress = Math.round((completedSteps.length / totalSteps) * 100);

  // Check if we're resuming setup (have some completed steps)
  const isResumingSetup = completedSteps.length > 0;
  // const currentAccount = data.exchangeAccounts?.length > 0 ? data?.exchangeAccounts[0] : null;
  const currentAccount = null
  console.log(completedSteps,"completed steps")



  const handleCompleteStep = (step: number) => {
    // Add step to completed steps if not already there
    setCompletedSteps((prev) => {
      if (!prev.includes(step)) {
        return [...prev, step];
      }
      return prev;
    });

    // Move to next incomplete step
    const nextStep = findNextIncompleteStep(step, [...completedSteps, step]);
    if (nextStep) {
      setCurrentStep(nextStep);
    }
  };

  const findNextIncompleteStep = (currentStep: number, completed: number[] = completedSteps) => {
    for (let i = currentStep + 1; i <= totalSteps; i++) {
      if (!completed.includes(i)) {
        return i;
      }
    }
    return null;
  };

  const isStepCompleted = (step: number) => completedSteps.includes(step);

  const handleProxySelect = (proxyId: string) => {
    setSelectedProxyId(proxyId);
  };

  const handleInvestmentComplete = (data: any) => {
    setInvestmentData(data);
    handleCompleteStep(5);
  };

  const handleCancel = async () => {
    await skipOnboardingAction();
  };

  const handleEditStep = (step: number) => {
    setCurrentStep(step);
  };



  return (
    <div className="flex min-h-screen flex-col">
      <header className="h-16 flex items-center border-b">
        <Container className="flex w-full items-center justify-between">
          <div className="flex items-center gap-2">
            <Button variant="ghost" size="icon" onClick={handleCancel} className="mr-2">
              <ChevronLeft className="h-5 w-5" />
              <span className="sr-only">Back</span>
            </Button>
            <span className="text-xl font-bold">
              {isResumingSetup ? `Setup: ${currentAccount?.owner || 'Account'}` : "Complete Your Setup"}
            </span>
          </div>
          <div className="flex items-center gap-4">
            <MobileNav />
          </div>
        </Container>
      </header>
      <main className="flex-1 py-8 pb-20 md:pb-8">
        <Container className="max-w-5xl">
          <div className="mb-8 text-center">
            <h1 className="text-3xl font-bold">
              {isResumingSetup ? `Complete ${currentAccount?.owner || 'Account'} Setup` : "Complete Your Setup"}
            </h1>
            <p className="text-gray-500 mt-2">
              {isResumingSetup
                ? "Complete the setup process for your trading account."
                : "Let's get your trading account ready. Complete these steps to start trading."}
            </p>
          </div>

          <div className="mb-8">
            <Progress value={progress} className="h-2 bg-gray-100" />
            <div className="flex justify-between items-center mt-2">
              <p className="text-sm text-gray-500">
                {completedSteps.length} of {totalSteps} steps completed
              </p>
              <Button variant="ghost" size="sm" onClick={handleCancel}>
                Complete Later
              </Button>
            </div>
          </div>

          <div className="grid gap-6">
            {/* Step 1: Proxy Setup */}
            <Card
              className={`border-2 ${currentStep === 1 ? "border-[#245c1a]" : isStepCompleted(1) ? "border-gray-200 bg-gray-50" : "border-gray-200"}`}
            >
              <CardHeader className="flex flex-col md:flex-row md:items-center gap-4">
                <div
                  className={`flex h-10 w-10 items-center justify-center rounded-full ${isStepCompleted(1) ? "bg-[#245c1a] text-white" : "bg-gray-100"}`}
                >
                  {isStepCompleted(1) ? <Check className="h-5 w-5" /> : <Network className="h-5 w-5" />}
                </div>
                <div>
                  <CardTitle>{isStepCompleted(1) ? "Proxy Configured" : "Setup Proxy Connection"}</CardTitle>
                  <CardDescription>
                    {isStepCompleted(1)
                      ? "Your proxy has been successfully configured"
                      : "Configure a proxy for secure trading connections"}
                  </CardDescription>
                </div>
                {isStepCompleted(1) && (
                  <div className="ml-auto mt-2 md:mt-0">
                    <Button
                      variant="ghost"
                      size="sm"
                      className="text-[#245c1a]"
                      onClick={() => currentStep === 1 ? setCurrentStep(findNextIncompleteStep(1) || 2) : handleEditStep(1)}
                    >
                      {currentStep === 1 ? "Cancel" : "Edit"}
                    </Button>
                  </div>
                )}
              </CardHeader>
              {currentStep === 1 && (
                <CardContent>
                  <ProxyStep
                    proxies={data.proxies}
                    selectedProxyId={selectedProxyId}
                    onProxySelect={handleProxySelect}
                    onStepComplete={() => handleCompleteStep(1)}
                  />
                </CardContent>
              )}
              {isStepCompleted(1) && currentStep !== 1 && (
                <CardContent>
                  <div className="rounded-lg border border-green-100 bg-green-50 p-4">
                    <div className="flex items-center gap-2 mb-2">
                      <Shield className="h-4 w-4 text-[#245c1a]" />
                      <h3 className="font-medium">Proxy Configuration</h3>
                    </div>
                    <div className="space-y-2 text-sm">
                      <div className="flex justify-between">
                        <span className="text-gray-500">Type:</span>
                        <span className="font-medium uppercase">{data.proxies[0]?.type || "HTTP"}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-500">Address:</span>
                        <span className="font-medium">{data.proxies[0]?.ip_address || "Configured"}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-500">Created:</span>
                        <span className="font-medium">
                          {data.proxies[0]?.created ? new Date(data.proxies[0].created).toLocaleDateString() : "Just now"}
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-500">Status:</span>
                        <span className="font-medium text-green-600">Active</span>
                      </div>
                    </div>
                    <p className="text-xs text-gray-500 mt-3">
                      Your proxy has been configured and is ready for secure trading connections.
                    </p>
                  </div>
                </CardContent>
              )}
            </Card>

            {/* Step 2: Connect Binance */}
            <Card
              className={`border-2 ${currentStep === 2 ? "border-[#245c1a]" : isStepCompleted(2) ? "border-gray-200 bg-gray-50" : "border-gray-200"}`}
            >
              <CardHeader className="flex flex-col md:flex-row md:items-center gap-4">
                <div
                  className={`flex h-10 w-10 items-center justify-center rounded-full ${isStepCompleted(2) ? "bg-[#245c1a] text-white" : "bg-gray-100"}`}
                >
                  {isStepCompleted(2) ? <Check className="h-5 w-5" /> : <LinkIcon className="h-5 w-5" />}
                </div>
                <div>
                  <CardTitle>{isStepCompleted(2) ? "Exchange Account Connected" : "Connect Account"}</CardTitle>
                  <CardDescription>
                    {isStepCompleted(2)
                      ? "Your exchange account has been successfully connected"
                      : "Connect your exchange account for trading"}
                  </CardDescription>
                </div>
                {isStepCompleted(2) && (
                  <div className="ml-auto mt-2 md:mt-0">
                    <Button
                      variant="ghost"
                      size="sm"
                      className="text-[#245c1a]"
                      onClick={() => currentStep === 2 ? setCurrentStep(findNextIncompleteStep(2) || 3) : handleEditStep(2)}
                    >
                      {currentStep === 2 ? "Cancel" : "Edit"}
                    </Button>
                  </div>
                )}
              </CardHeader>
              {currentStep === 2 && (
                <CardContent>
                  <ExchangeStep
                    exchangeAccounts={data.exchangeAccounts}
                    credentials={data.credentials}
                    selectedProxyId={selectedProxyId}
                    onStepComplete={() => handleCompleteStep(2)}
                  />
                </CardContent>
              )}
              {isStepCompleted(2) && currentStep !== 2 && (
                <CardContent>
                  <div className="rounded-lg border border-green-100 bg-green-50 p-4">
                    <div className="flex items-center gap-2 mb-2">
                      <Shield className="h-4 w-4 text-[#245c1a]" />
                      <h3 className="font-medium">Connection Details</h3>
                    </div>
                    <div className="space-y-2 text-sm">
                      <div className="flex justify-between">
                        <span className="text-gray-500">Account Email:</span>
                        <span className="font-medium">{data.exchangeAccounts[0]?.email || "Connected"}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-500">Exchange:</span>
                        <span className="font-medium capitalize">{data.exchangeAccounts[0]?.exchange || "Binance"}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-500">Connection Status:</span>
                        <span className="font-medium text-green-600">Active</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-500">Proxy:</span>
                        <span className="font-medium">{selectedProxyId ? "Linked" : "None"}</span>
                      </div>
                    </div>
                    <p className="text-xs text-gray-500 mt-3">
                      Your Binance account is now connected to TradeSmart. You can edit your API keys at any time.
                    </p>
                  </div>
                </CardContent>
              )}
            </Card>

            {/* Step 3: Secret Key */}
            <Card
              className={`border-2 ${currentStep === 3 ? "border-[#245c1a]" : isStepCompleted(3) ? "border-gray-200 bg-gray-50" : "border-gray-200"}`}
            >
              <CardHeader className="flex flex-col md:flex-row md:items-center gap-4">
                <div
                  className={`flex h-10 w-10 items-center justify-center rounded-full ${isStepCompleted(3) ? "bg-[#245c1a] text-white" : "bg-gray-100"}`}
                >
                  {isStepCompleted(3) ? <Check className="h-5 w-5" /> : <Key className="h-5 w-5" />}
                </div>
                <div>
                  <CardTitle>{isStepCompleted(3) ? "Secret Key Created" : "Account Secret Key"}</CardTitle>
                  <CardDescription>
                    {isStepCompleted(3)
                      ? "Your account secret key has been created and secured"
                      : "Create a secure key for account management"}
                  </CardDescription>
                </div>
                {isStepCompleted(3) && (
                  <div className="ml-auto mt-2 md:mt-0">
                    <Button
                      variant="ghost"
                      size="sm"
                      className="text-[#245c1a]"
                      onClick={() => currentStep === 3 ? setCurrentStep(findNextIncompleteStep(3) || 4) : handleEditStep(3)}
                    >
                      {currentStep === 3 ? "Cancel" : "Edit"}
                    </Button>
                  </div>
                )}
              </CardHeader>
              {currentStep === 3 && (
                <CardContent>
                  <SecretKeyStep
                    onStepComplete={() => handleCompleteStep(3)}
                    secretKeyData={data.secretKeyData}
                  />
                </CardContent>
              )}
              {isStepCompleted(3) && currentStep !== 3 && (
                <CardContent>
                  <div className="rounded-lg border border-green-100 bg-green-50 p-4">
                    <div className="flex items-center gap-2 mb-2">
                      <Shield className="h-4 w-4 text-[#245c1a]" />
                      <h3 className="font-medium">Secret Key Configuration</h3>
                    </div>
                    <div className="space-y-2 text-sm">
                      <div className="flex justify-between">
                        <span className="text-gray-500">Status:</span>
                        <span className="font-medium text-green-600">Created & Secured</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-500">Encryption:</span>
                        <span className="font-medium">AES-256-GCM</span>
                      </div>
                    </div>
                    <p className="text-xs text-gray-500 mt-3">
                      Your secret key has been securely created and encrypted for account management.
                    </p>
                  </div>
                </CardContent>
              )}
            </Card>

            {/* Step 4: Trading Settings */}
            <Card
              className={`border-2 ${currentStep === 4 ? "border-[#245c1a]" : isStepCompleted(4) ? "border-gray-200 bg-gray-50" : "border-gray-200"}`}
            >
              <CardHeader className="flex flex-col md:flex-row md:items-center gap-4">
                <div
                  className={`flex h-10 w-10 items-center justify-center rounded-full ${isStepCompleted(4) ? "bg-[#245c1a] text-white" : "bg-gray-100"}`}
                >
                  {isStepCompleted(4) ? <Check className="h-5 w-5" /> : <Settings className="h-5 w-5" />}
                </div>
                <div>
                  <CardTitle>{isStepCompleted(4) ? "Trading Settings Configured" : "Trading Settings"}</CardTitle>
                  <CardDescription>
                    {isStepCompleted(4)
                      ? "Your trading settings have been configured"
                      : "Configure your automated trading preferences"}
                  </CardDescription>
                </div>
                {isStepCompleted(4) && (
                  <div className="ml-auto mt-2 md:mt-0">
                    <Button
                      variant="ghost"
                      size="sm"
                      className="text-[#245c1a]"
                      onClick={() => currentStep === 4 ? setCurrentStep(findNextIncompleteStep(4) || 5) : handleEditStep(4)}
                    >
                      {currentStep === 4 ? "Cancel" : "Edit"}
                    </Button>
                  </div>
                )}
              </CardHeader>
              {currentStep === 4 && (
                <CardContent>
                  <TradingSettingsStep
                    onStepComplete={() => handleCompleteStep(4)}
                  />
                </CardContent>
              )}
              {isStepCompleted(4) && currentStep !== 4 && (
                <CardContent>
                  <div className="rounded-lg border border-green-100 bg-green-50 p-4">
                    <div className="flex items-center gap-2 mb-2">
                      <Shield className="h-4 w-4 text-[#245c1a]" />
                      <h3 className="font-medium">Trading Configuration</h3>
                    </div>
                    <div className="space-y-2 text-sm">
                      <div className="flex justify-between">
                        <span className="text-gray-500">Status:</span>
                        <span className="font-medium text-green-600">Configured</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-500">Risk Management:</span>
                        <span className="font-medium">Enabled</span>
                      </div>
                    </div>
                    <p className="text-xs text-gray-500 mt-3">
                      Your trading preferences and risk management settings have been configured.
                    </p>
                  </div>
                </CardContent>
              )}
            </Card>

            {/* Step 5: Investment Configuration */}
            <Card
              className={`border-2 ${currentStep === 5 ? "border-[#245c1a]" : isStepCompleted(5) ? "border-gray-200 bg-gray-50" : "border-gray-200"}`}
            >
              <CardHeader className="flex flex-col md:flex-row md:items-center gap-4">
                <div
                  className={`flex h-10 w-10 items-center justify-center rounded-full ${isStepCompleted(5) ? "bg-[#245c1a] text-white" : "bg-gray-100"}`}
                >
                  {isStepCompleted(5) ? <Check className="h-5 w-5" /> : <DollarSign className="h-5 w-5" />}
                </div>
                <div>
                  <CardTitle>{isStepCompleted(5) ? "Investment Configured" : "Investment Setup"}</CardTitle>
                  <CardDescription>
                    {isStepCompleted(5)
                      ? "Your investment configuration has been completed"
                      : "Configure your investment amount and payment method"}
                  </CardDescription>
                </div>
                {isStepCompleted(5) && (
                  <div className="ml-auto mt-2 md:mt-0">
                    <Button
                      variant="ghost"
                      size="sm"
                      className="text-[#245c1a]"
                      onClick={() => currentStep === 5 ? setCurrentStep(findNextIncompleteStep(5) || 6) : handleEditStep(5)}
                    >
                      {currentStep === 5 ? "Cancel" : "Edit"}
                    </Button>
                  </div>
                )}
              </CardHeader>
              {currentStep === 5 && (
                <CardContent>
                  <InvestmentStep
                    onStepComplete={handleInvestmentComplete}
                    investmentData={investmentData}
                  />
                </CardContent>
              )}
              {isStepCompleted(5) && currentStep !== 5 && (
                <CardContent>
                  <div className="rounded-lg border border-green-100 bg-green-50 p-4">
                    <div className="flex items-center gap-2 mb-2">
                      <Shield className="h-4 w-4 text-[#245c1a]" />
                      <h3 className="font-medium">Investment Configuration</h3>
                    </div>
                    <div className="space-y-2 text-sm">
                      <div className="flex justify-between">
                        <span className="text-gray-500">Investment Amount:</span>
                        <span className="font-medium">${investmentData.subscriptionAmount || 40}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-500">Payment Method:</span>
                        <span className="font-medium capitalize">{investmentData.paymentMethod || "Not selected"}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-500">Status:</span>
                        <span className="font-medium text-green-600">Configured</span>
                      </div>
                    </div>
                    <p className="text-xs text-gray-500 mt-3">
                      Your investment configuration is ready for the 30-day trading cycle.
                    </p>
                  </div>
                </CardContent>
              )}
            </Card>

            {/* Step 6: Account Activation */}
            <Card
              className={`border-2 ${currentStep === 6 ? "border-[#245c1a]" : isStepCompleted(6) ? "border-gray-200 bg-gray-50" : "border-gray-200"}`}
            >
              <CardHeader className="flex flex-col md:flex-row md:items-center gap-4">
                <div
                  className={`flex h-10 w-10 items-center justify-center rounded-full ${isStepCompleted(6) ? "bg-[#245c1a] text-white" : "bg-gray-100"}`}
                >
                  {isStepCompleted(6) ? <Check className="h-5 w-5" /> : <Zap className="h-5 w-5" />}
                </div>
                <div>
                  <CardTitle>{isStepCompleted(6) ? "Account Activated" : "Account Activation"}</CardTitle>
                  <CardDescription>
                    {isStepCompleted(6)
                      ? "Your trading account has been activated and is ready to use"
                      : "Complete setup and activate your trading account"}
                  </CardDescription>
                </div>
                {isStepCompleted(6) && (
                  <div className="ml-auto mt-2 md:mt-0">
                    <Button variant="ghost" size="sm" className="text-[#245c1a]" onClick={() => setCurrentStep(6)}>
                      View
                    </Button>
                  </div>
                )}
              </CardHeader>
              {currentStep === 6 && (
                <CardContent>
                  <ActivationStep
                    user={data.user}
                    exchangeAccounts={data.exchangeAccounts}
                  />
                </CardContent>
              )}
              {isStepCompleted(6) && currentStep !== 6 && (
                <CardContent>
                  <div className="rounded-lg border border-green-100 bg-green-50 p-4">
                    <div className="flex items-center gap-2 mb-2">
                      <Shield className="h-4 w-4 text-[#245c1a]" />
                      <h3 className="font-medium">Account Status</h3>
                    </div>
                    <div className="space-y-2 text-sm">
                      <div className="flex justify-between">
                        <span className="text-gray-500">Status:</span>
                        <span className="font-medium text-green-600">Active</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-500">Trading:</span>
                        <span className="font-medium text-green-600">Enabled</span>
                      </div>
                    </div>
                    <p className="text-xs text-gray-500 mt-3">
                      Your trading account is fully activated and ready for automated trading.
                    </p>
                  </div>
                </CardContent>
              )}
            </Card>

          </div>
        </Container>
      </main>

      <BottomNav />
    </div>
  );
}
