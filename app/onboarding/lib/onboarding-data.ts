import { currentUser } from "@clerk/nextjs/server";
import { createUltimateService } from "@/lib/services/ultimate-service";
import { checkUserApprovalStatus } from "@/lib/actions/invite-actions";
import { UserSettings } from "@/lib/database/types";
import { getLoggedInUser } from "@/app/auth/actions";

export interface OnboardingUser {
  id: string;
  email: string;
  emailVisibility: boolean;
  verified: boolean;
  name: string;
  avatar: string;
  settings: any;
  createdAt: string;
  updatedAt: string;
}

export interface OnboardingProxy {
  id: string;
  ip_address: string;
  type: "http" | "socks5";
  created: string;
  updated: string;
  user: string;
}

export interface OnboardingExchangeAccount {
  id: string;
  exchange: string;
  owner: string;
  email: string;
  created: string;
  updated: string;
  proxy?: string;
}

export interface OnboardingCredential {
  name: string;
  email: string;
  exchange: string;
  api_key: string;
  api_secret: string;
}

export interface OnboardingSecretKey {
  secretKey?: string;
  hasEncryptedPassword: boolean;
  hasEncryptedCredentials: boolean;
  decryptionError?: string;
}

export interface OnboardingData {
  user: OnboardingUser;
  proxies: OnboardingProxy[];
  exchangeAccounts: OnboardingExchangeAccount[];
  credentials: OnboardingCredential[];
  secretKeyData: OnboardingSecretKey;
  hasCompletedOnboarding: boolean;
  currentStep: number;
  completedSteps: number[];
  userType?: "beginner" | "experienced";
  approvalStatus?: "pending" | "approved" | "rejected";
}

/**
 * Check if user has completed onboarding based on settings
 */
function checkOnboardingStatus(user: OnboardingUser): boolean {
  try {
    let settings: UserSettings;

    if (typeof user.settings === "string") {
      settings = JSON.parse(user.settings || "{}");
    } else if (typeof user.settings === "object" && user.settings !== null) {
      settings = user.settings as UserSettings;
    } else {
      settings = {};
    }

    // First check if hasCompletedOnboarding is explicitly set
    if (settings.hasCompletedOnboarding !== undefined) {
      return settings.hasCompletedOnboarding;
    }

    // Fallback to the old logic if not set
    return !!(settings.password && settings.credentials);
  } catch (error) {
    console.log("Error parsing settings:", error);
    return false;
  }
}

/**
 * Determine current step and completed steps based on user data
 */
function determineOnboardingProgress(
  user: OnboardingUser,
  proxies: OnboardingProxy[],
  exchangeAccounts: OnboardingExchangeAccount[]
): { currentStep: number; completedSteps: number[] } {
  const completedSteps: number[] = [];

  // Step 1: Proxy setup
  if (proxies.length > 0) {
    completedSteps.push(1);
  }

  // Step 2: Exchange account connection
  const binanceAccount = exchangeAccounts.find(
    (acc) => acc.exchange === "binance"
  );
  if (binanceAccount) {
    completedSteps.push(2);
  }

  // Step 3: Secret key (check user settings for both password and credentials)
  try {
    const settings =
      typeof user.settings === "string"
        ? JSON.parse(user.settings || "{}")
        : user.settings || {};

    // Step 3 is complete when both encrypted password and credentials exist
    if (settings.password && settings.credentials) {
      completedSteps.push(3);
    }
  } catch (error) {
    console.log("Error checking secret key:", error);
  }

  // Step 4: Trading settings (assume completed if step 3 is done for now)
  if (completedSteps.includes(3)) {
    completedSteps.push(4);
  }

  // Step 5: Activation (check if fully onboarded using the new hasCompletedOnboarding field)
  try {
    const settings =
      typeof user.settings === "string"
        ? JSON.parse(user.settings || "{}")
        : user.settings || {};

    // Use the explicit hasCompletedOnboarding field if available
    if (settings.hasCompletedOnboarding !== undefined) {
      if (settings.hasCompletedOnboarding) {
        completedSteps.push(5);
      }
    } else {
      // Fallback to old logic if hasCompletedOnboarding is not set
      if (checkOnboardingStatus(user)) {
        completedSteps.push(5);
      }
    }
  } catch (error) {
    console.log("Error checking onboarding completion:", error);
  }

  // Determine current step - find the first incomplete step
  let currentStep = 1;
  for (let step = 1; step <= 5; step++) {
    if (!completedSteps.includes(step)) {
      currentStep = step;
      break;
    }
  }

  // If all steps are completed, set to the last step
  if (completedSteps.length === 5) {
    currentStep = 5;
  }

  return { currentStep, completedSteps };
}

/**
 * Get current user email
 */
export async function getCurrentUserEmail(): Promise<string> {
  const user = await getLoggedInUser();
  if (!user) {
    throw new Error("Not authenticated");
  }
  return user.email || "";
}

/**
 * Get user credentials using gbozee library (replaces manual decryption)
 */
async function getDecryptedCredentials(
  userData: any
): Promise<OnboardingCredential[]> {
  try {
    // Use the Ultimate service to get credentials directly from the library
    // The library handles all decryption internally
    const service = createUltimateService(userData.email);
    const credentialsResult = await service.getUserCredentials(userData.email);

    if (!credentialsResult.success || !credentialsResult.data) {
      console.log(
        "Failed to get credentials from library:",
        credentialsResult.message
      );
      return [];
    }

    // Transform library credentials to OnboardingCredential format if needed
    const credentials: OnboardingCredential[] = credentialsResult.data.map(
      (cred: any) => ({
        name: cred.name,
        email: cred.email,
        exchange: cred.exchange,
        api_key: cred.api_key,
        api_secret: cred.api_secret,
      })
    );

    console.log(
      "Successfully retrieved credentials from library:",
      credentials.length,
      "credentials found"
    );
    return credentials;
  } catch (error) {
    console.log(
      "Error getting user credentials from library, returning empty array:",
      error
    );
    return [];
  }
}

/**
 * Get all onboarding data for the current user
 */
export async function getOnboardingData(): Promise<OnboardingData> {
  const userEmail = await getCurrentUserEmail();
  const service = createUltimateService(userEmail);

  try {
    // Follow the same pattern as user-sync endpoint
    console.log("Attempting to get user by email from database");
    let appUser = await service.getUserByEmailLegacy(userEmail);

    // Handle user creation if doesn't exist (same pattern as user-sync endpoint)
    if (!appUser) {
      console.log("User not found, creating new user for email:", userEmail);
      const user = await getLoggedInUser();
      const userName = user?.name?.trim() || userEmail.split("@")[0] || "User";

      // Create new user using the legacy method (same as user-sync endpoint)
      appUser = await service.createUserLegacy({
        email: userEmail,
        name: userName,
      });

      // Ensure hasCompletedOnboarding is set (should be false for new users)
      appUser = await service.ensureOnboardingStatus(appUser);

      console.log("Successfully created user:", appUser.id);
    } else {
      // User exists, ensure hasCompletedOnboarding is set in settings (same as user-sync endpoint)
      appUser = await service.ensureOnboardingStatus(appUser);
    }

    // Convert AppUser back to the format expected by the rest of the function
    const userData = {
      id: appUser.id,
      email: appUser.email,
      emailVisibility: appUser.emailVisibility,
      verified: appUser.verified,
      name: appUser.name,
      avatar: appUser.avatar,
      settings: appUser.settings,
      created: appUser.createdAt,
      updated: appUser.updatedAt,
    };

    // Now fetch proxies and exchange accounts using the user ID
    const [proxiesResult, exchangeAccountsResult] = await Promise.all([
      service.getProxiesByUser(userData.id),
      service.getExchangeAccountsByUser(userData.id),
    ]);

    // Transform data to our interfaces
    const user: OnboardingUser = {
      id: userData.id,
      email: userData.email,
      emailVisibility: userData.emailVisibility,
      verified: userData.verified,
      name: userData.name,
      avatar: userData.avatar || "",
      settings: userData.settings || {},
      createdAt: userData.created,
      updatedAt: userData.updated,
    };

    const proxies: OnboardingProxy[] = (proxiesResult.data || []).map(
      (proxy: any) => ({
        id: proxy.id,
        ip_address: proxy.ip_address,
        type: proxy.type,
        created: proxy.created,
        updated: proxy.updated,
        user: proxy.user,
      })
    );

    const exchangeAccounts: OnboardingExchangeAccount[] = (
      exchangeAccountsResult.data || []
    ).map((account: any) => ({
      id: account.id,
      exchange: account.exchange,
      owner: account.owner,
      email: account.email,
      created: account.created,
      updated: account.updated,
      proxy: account.proxy,
    }));

    // Fetch decrypted credentials (with safe error handling)
    let credentials: OnboardingCredential[] = [];
    try {
      credentials = await getDecryptedCredentials(userData);
    } catch (error) {
      console.log(
        "Failed to decrypt credentials, continuing with empty array:",
        error
      );
      credentials = [];
    }

    // Fetch secret key data (always include it)
    let secretKeyData: OnboardingSecretKey;
    try {
      const secretKeyStepData = await getSecretKeyStepData(userData);
      secretKeyData = secretKeyStepData.secretKeyData || {
        hasEncryptedPassword: false,
        hasEncryptedCredentials: false,
      };
    } catch (error) {
      console.log("Failed to get secret key data, using default:", error);
      secretKeyData = {
        hasEncryptedPassword: false,
        hasEncryptedCredentials: false,
        decryptionError: "Failed to load secret key data",
      };
    }

    const hasCompletedOnboarding = checkOnboardingStatus(user);
    const { currentStep, completedSteps } = determineOnboardingProgress(
      user,
      proxies,
      exchangeAccounts
    );

    // Check PocketBase approval status for beginners
    let approvalStatus: "pending" | "approved" | "rejected" | undefined;
    let userType: "beginner" | "experienced" | undefined;

    try {
      const settings =
        typeof user.settings === "string"
          ? JSON.parse(user.settings || "{}")
          : user.settings || {};

      userType = settings.userType;

      // If user is a beginner, check their approval status in PocketBase
      if (userType === "beginner") {
        const pocketbaseApproval = await checkUserApprovalStatus();
        if (pocketbaseApproval.success) {
          approvalStatus = pocketbaseApproval.approved ? "approved" : "pending";
        } else {
          // Fallback to settings if PocketBase check fails
          approvalStatus = settings.approvalStatus || "pending";
        }
      } else {
        // For experienced users, they're automatically approved
        approvalStatus = "approved";
      }
    } catch (error) {
      console.log("Error checking approval status:", error);
      approvalStatus = "approved"; // Default to approved for experienced users
    }

    return {
      user,
      proxies,
      exchangeAccounts,
      credentials,
      secretKeyData,
      hasCompletedOnboarding,
      currentStep,
      completedSteps,
      userType,
      approvalStatus,
    };
  } catch (error) {
    console.error("Error fetching onboarding data:", error);
    throw new Error("Failed to fetch onboarding data");
  }
}

/**
 * Get secret key step data with decryption
 */
async function getSecretKeyStepData(
  userData: any
): Promise<Partial<OnboardingData>> {
  try {
    // Parse user settings
    let settings: any = {};
    try {
      settings =
        typeof userData.settings === "string"
          ? JSON.parse(userData.settings || "{}")
          : userData.settings || {};
    } catch (parseError) {
      console.error("Error parsing user settings:", parseError);
      return {
        secretKeyData: {
          hasEncryptedPassword: false,
          hasEncryptedCredentials: false,
          decryptionError: "Invalid settings format",
        },
      };
    }

    const hasEncryptedPassword = !!settings.password;
    const hasEncryptedCredentials = !!settings.credentials;

    // If no encrypted password exists, return empty state
    if (!hasEncryptedPassword) {
      return {
        secretKeyData: {
          hasEncryptedPassword: false,
          hasEncryptedCredentials,
        },
      };
    }

    // Attempt to decrypt the user's secret key using server salt
    const serverSalt = process.env.SALT;
    if (!serverSalt) {
      console.error("Server salt not configured");
      return {
        secretKeyData: {
          hasEncryptedPassword,
          hasEncryptedCredentials,
          decryptionError: "Server configuration error",
        },
      };
    }

    // Import decryption function
    const { decryptObject } = await import("@/lib/encryption");

    // Decrypt the user's secret key
    const decryptionResult = decryptObject<string>(
      settings.password,
      serverSalt
    );

    if (decryptionResult.success && decryptionResult.data) {
      return {
        secretKeyData: {
          secretKey: decryptionResult.data,
          hasEncryptedPassword,
          hasEncryptedCredentials,
        },
      };
    } else {
      console.error("Failed to decrypt secret key:", decryptionResult.error);
      return {
        secretKeyData: {
          hasEncryptedPassword,
          hasEncryptedCredentials,
          decryptionError:
            decryptionResult.error || "Failed to decrypt secret key",
        },
      };
    }
  } catch (error) {
    console.error("Error in getSecretKeyStepData:", error);
    return {
      secretKeyData: {
        hasEncryptedPassword: false,
        hasEncryptedCredentials: false,
        decryptionError:
          error instanceof Error ? error.message : "Unknown error",
      },
    };
  }
}

/**
 * Get data for a specific onboarding step
 */
export async function getStepData(
  step: number
): Promise<Partial<OnboardingData>> {
  const userEmail = await getCurrentUserEmail();
  const service = createUltimateService(userEmail);

  // Get user first to get their ID (using legacy method like user-sync endpoint)
  let appUser = await service.getUserByEmailLegacy(userEmail);
  if (!appUser) {
    throw new Error("User not found");
  }

  // Ensure hasCompletedOnboarding is set in settings (same as user-sync endpoint)
  appUser = await service.ensureOnboardingStatus(appUser);

  switch (step) {
    case 1: // Proxy step
      const proxiesResult = await service.getProxiesByUser(appUser.id);
      return {
        proxies: (proxiesResult.data || []).map((proxy: any) => ({
          id: proxy.id,
          ip_address: proxy.ip_address,
          type: proxy.type,
          created: proxy.created,
          updated: proxy.updated,
          user: proxy.user,
        })),
      };

    case 2: // Exchange step
      const exchangeResult = await service.getExchangeAccountsByUser(
        appUser.id
      );
      return {
        exchangeAccounts: (exchangeResult.data || []).map((account: any) => ({
          id: account.id,
          exchange: account.exchange,
          owner: account.owner,
          email: account.email,
          created: account.created,
          updated: account.updated,
          proxy: account.proxy,
        })),
      };

    default:
      return {};
  }
}
