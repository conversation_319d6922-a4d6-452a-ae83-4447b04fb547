import { OnboardingClient } from "../OnboardingClient";

export default async function ExperiencedOnboardingPage() {
  const onboardingData = {} as any;

  // Get all onboarding data
  // const onboardingData = await getOnboardingData();

  // // If user is a beginner and not approved, redirect to pending approval
  // if (onboardingData.userType === "beginner" && onboardingData.approvalStatus !== "approved") {
  //   redirect('/onboarding/pending-approval');
  // }

  // // If user has completed onboarding, redirect to dashboard
  // if (onboardingData.hasCompletedOnboarding) {
  //   redirect('/dashboard');
  // }

  return <OnboardingClient data={onboardingData} />;
}
