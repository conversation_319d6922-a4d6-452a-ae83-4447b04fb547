'use client'

import { useState, useTransition, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Key, CheckCircle, AlertCircle, Loader2, Eye, EyeOff } from "lucide-react";
import { createSecretKeyAction, type SecretKeyPayload } from "../actions";
import { toast } from "sonner";
import { validatePasswordStrength } from "@/lib/encryption";
import { OnboardingSecretKey } from "../lib/onboarding-data";

interface SecretKeyStepProps {
  onStepComplete: () => void;
  secretKeyData?: OnboardingSecretKey;
}

export function SecretKeyStep({ onStepComplete, secretKeyData }: SecretKeyStepProps) {
  const [isPending, startTransition] = useTransition();
  const [secretKey, setSecretKey] = useState("");
  const [validation, setValidation] = useState<{ success: boolean; message: string } | null>(null);
  const [showPassword, setShowPassword] = useState(false);

  // Initialize with existing secret key if available
  useEffect(() => {
    if (secretKeyData?.secretKey) {
      setSecretKey(secretKeyData.secretKey);
    }
  }, [secretKeyData]);

  // Validate password in real-time
  useEffect(() => {
    if (secretKey) {
      const result = validatePasswordStrength(secretKey);
      setValidation(result);
    } else {
      setValidation(null);
    }
  }, [secretKey]);

  const handleCreateSecretKey = () => {
    // Final validation before submission
    const finalValidation = validatePasswordStrength(secretKey);
    if (!finalValidation.success) {
      toast.error(finalValidation.message);
      return;
    }

    startTransition(async () => {
      const payload: SecretKeyPayload = {
        secretKey,
      };

      const result = await createSecretKeyAction(payload);

      if (result.success) {
        toast.success(result.message);
        setSecretKey("");
        onStepComplete();
      } else {
        toast.error(result.error || "Failed to create secret key");
      }
    });
  };

  // Determine UI state based on secret key data
  const isEditing = secretKeyData?.hasEncryptedPassword;
  const hasError = !!secretKeyData?.decryptionError;

  return (
    <Card className="border-[#245c1a]">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Key className="h-5 w-5" />
          {isEditing ? "Edit Account Secret Key" : "Create Account Secret Key"}
        </CardTitle>
        <CardDescription>
          {isEditing
            ? "Update your secure secret key for managing your trading accounts"
            : "Set up a secure secret key for managing your trading accounts"
          }
        </CardDescription>

        {/* Show decryption error if any */}
        {hasError && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-3 mt-2">
            <p className="text-sm text-red-700">
              <AlertCircle className="inline h-4 w-4 mr-1" />
              Error loading existing secret key: {secretKeyData?.decryptionError}
            </p>
            <p className="text-xs text-red-600 mt-1">
              You can create a new secret key, but this will re-encrypt your existing credentials.
            </p>
          </div>
        )}
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="space-y-2">
          <Label htmlFor="account-key">Account Secret Key</Label>
          <div className="relative">
            <Input
              id="account-key"
              type={showPassword ? "text" : "password"}
              value={secretKey}
              onChange={(e) => setSecretKey(e.target.value)}
              placeholder={isEditing
                ? "Enter your current secret key or create a new one"
                : "Create a secure secret key (min 8 characters)"
              }
              minLength={8}
              className={`pr-20 ${
                validation
                  ? validation.success
                    ? "border-green-500 focus:border-green-500"
                    : "border-red-500 focus:border-red-500"
                  : ""
              }`}
            />

            {/* Password visibility toggle */}
            <button
              type="button"
              onClick={() => setShowPassword(!showPassword)}
              className="absolute right-12 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-700 focus:outline-none"
              tabIndex={-1}
            >
              {showPassword ? (
                <EyeOff className="h-4 w-4" />
              ) : (
                <Eye className="h-4 w-4" />
              )}
            </button>

            {/* Validation icon */}
            {validation && (
              <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                {validation.success ? (
                  <CheckCircle className="h-4 w-4 text-green-500" />
                ) : (
                  <AlertCircle className="h-4 w-4 text-red-500" />
                )}
              </div>
            )}
          </div>

          {/* Password validation feedback */}
          {validation && (
            <p className={`text-sm ${validation.success ? "text-green-600" : "text-red-600"}`}>
              {validation.message}
            </p>
          )}

          <p className="text-sm text-gray-500">
            {isEditing
              ? "This key secures your trading accounts. Changing it will re-encrypt all your credentials."
              : "This key will be used to secure and switch between your trading accounts. Make sure to remember it as you'll need it to access your accounts."
            }
          </p>

          {/* Show status for existing encrypted data */}
          {secretKeyData && (
            <div className="text-xs text-gray-600 space-y-1">
              <p>Status:</p>
              <ul className="ml-4 space-y-1">
                <li className={secretKeyData.hasEncryptedPassword ? "text-green-600" : "text-gray-500"}>
                  {secretKeyData.hasEncryptedPassword ? "✓" : "○"} Encrypted password: {secretKeyData.hasEncryptedPassword ? "Found" : "Not set"}
                </li>
                <li className={secretKeyData.hasEncryptedCredentials ? "text-green-600" : "text-gray-500"}>
                  {secretKeyData.hasEncryptedCredentials ? "✓" : "○"} Encrypted credentials: {secretKeyData.hasEncryptedCredentials ? "Found" : "Not set"}
                </li>
              </ul>
            </div>
          )}
        </div>

        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
          <h4 className="font-medium text-yellow-800 mb-2">Security Tips:</h4>
          <ul className="text-sm text-yellow-700 space-y-1">
            <li>• Use a combination of letters, numbers, and symbols</li>
            <li>• Make it at least 8 characters long</li>
            <li>• Don't use personal information like birthdays or names</li>
            <li>• Store it securely - you'll need it to access your accounts</li>
          </ul>
        </div>

        <div className="flex gap-2">
          <Button variant="outline" className="flex-1">
            Skip for Now
          </Button>
          <Button
            onClick={handleCreateSecretKey}
            className="flex-1 bg-[#245c1a] hover:bg-[#1a4513] disabled:opacity-50"
            disabled={isPending || !validation?.success}
          >
            {isPending ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Encrypting...
              </>
            ) : (
              isEditing ? "Update Secret Key" : "Create Secret Key"
            )}
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}
