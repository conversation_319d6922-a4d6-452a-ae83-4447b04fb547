'use client'

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { 
  DollarSign, 
  CreditCard, 
  Smartphone, 
  Globe, 
  TrendingUp, 
  Clock, 
  Shield,
  CheckCircle,
  Copy,
  ExternalLink
} from "lucide-react";
import { toast } from "sonner";
import { 
  SUBSCRIPTION_AMOUNTS, 
  CRYPTO_NETWORKS, 
  INTEREST_RATES,
  calculateReturns,
  type CryptoNetwork 
} from "@/lib/constants/investment";

interface InvestmentStepProps {
  onStepComplete: (data: {
    subscriptionAmount: number;
    paymentMethod: string;
    selectedNetwork?: CryptoNetwork;
    cardDetails?: any;
  }) => void;
  investmentData?: {
    subscriptionAmount?: number;
    paymentMethod?: string;
    selectedNetwork?: CryptoNetwork;
    cardDetails?: any;
  };
}

export function InvestmentStep({ onStepComplete, investmentData }: InvestmentStepProps) {
  const [subscriptionAmount, setSubscriptionAmount] = useState(investmentData?.subscriptionAmount || 40);
  const [paymentMethod, setPaymentMethod] = useState(investmentData?.paymentMethod || "");
  const [selectedNetwork, setSelectedNetwork] = useState<CryptoNetwork>(
    investmentData?.selectedNetwork || "TRC20"
  );
  const [cardDetails, setCardDetails] = useState(investmentData?.cardDetails || {
    number: "",
    expiry: "",
    cvv: "",
    name: "",
  });
  const [isProcessingPayment, setIsProcessingPayment] = useState(false);
  const [isVerifyingPayment, setIsVerifyingPayment] = useState(false);
  const [verificationTimer, setVerificationTimer] = useState(0);

  const returns = calculateReturns(subscriptionAmount);
  const currentNetwork = CRYPTO_NETWORKS[selectedNetwork];

  const copyToClipboard = (text: string, label: string) => {
    navigator.clipboard.writeText(text);
    toast.success(`${label} copied to clipboard!`);
  };

  const handleCompleteInvestment = () => {
    if (!paymentMethod) {
      toast.error("Please select a payment method");
      return;
    }

    if (paymentMethod === "card" && (!cardDetails.number || !cardDetails.expiry || !cardDetails.cvv || !cardDetails.name)) {
      toast.error("Please fill in all card details");
      return;
    }

    const data = {
      subscriptionAmount,
      paymentMethod,
      selectedNetwork: paymentMethod === "crypto" ? selectedNetwork : undefined,
      cardDetails: paymentMethod === "card" ? cardDetails : undefined,
    };

    toast.success("Investment configuration saved successfully!");
    onStepComplete(data);
  };

  const handleVerifyPayment = () => {
    setIsVerifyingPayment(true);
    setVerificationTimer(30);

    const timer = setInterval(() => {
      setVerificationTimer((prev) => {
        if (prev <= 1) {
          clearInterval(timer);
          setIsVerifyingPayment(false);
          toast.success("Payment verified successfully!");
          handleCompleteInvestment();
          return 0;
        }
        return prev - 1;
      });
    }, 1000);
  };

  return (
    <Card className="border-[#245c1a]">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <DollarSign className="h-5 w-5" />
          Investment Configuration
        </CardTitle>
        <CardDescription>
          Configure your investment amount and payment method for the 30-day trading cycle
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Investment Configuration */}
          <div className="space-y-6">
            {/* Amount Selection */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <DollarSign className="w-5 h-5 text-[#245c1a]" />
                  Investment Amount
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-3 gap-3">
                  {SUBSCRIPTION_AMOUNTS.map((amount) => (
                    <Button
                      key={amount.value}
                      variant={subscriptionAmount === amount.value ? "default" : "outline"}
                      className={`h-16 relative ${
                        subscriptionAmount === amount.value
                          ? "bg-[#245c1a] hover:bg-[#1a4513]"
                          : "hover:border-[#245c1a]"
                      }`}
                      onClick={() => setSubscriptionAmount(amount.value)}
                      disabled={isVerifyingPayment}
                    >
                      {amount.popular && (
                        <Badge className="absolute -top-2 -right-2 bg-orange-500 text-white text-xs px-2 py-1">
                          Popular
                        </Badge>
                      )}
                      <div className="text-center">
                        <div className="font-bold text-lg">{amount.label}</div>
                        <div className="text-xs opacity-75">
                          ${amount.cost.toLocaleString()} cost
                        </div>
                      </div>
                    </Button>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Payment Method Selection */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <CreditCard className="w-5 h-5 text-[#245c1a]" />
                  Payment Method
                </CardTitle>
              </CardHeader>
              <CardContent>
                <RadioGroup value={paymentMethod} onValueChange={setPaymentMethod}>
                  <div className="space-y-3">
                    <div className="flex items-center space-x-3 p-3 border rounded-lg hover:bg-gray-50">
                      <RadioGroupItem value="crypto" id="crypto" />
                      <Label htmlFor="crypto" className="flex items-center gap-2 cursor-pointer flex-1">
                        <Globe className="w-5 h-5 text-[#245c1a]" />
                        <div>
                          <div className="font-medium">Cryptocurrency</div>
                          <div className="text-sm text-gray-500">Pay with USDT (Recommended)</div>
                        </div>
                      </Label>
                      <Badge variant="secondary" className="bg-green-100 text-green-800">
                        Instant
                      </Badge>
                    </div>
                    <div className="flex items-center space-x-3 p-3 border rounded-lg hover:bg-gray-50">
                      <RadioGroupItem value="card" id="card" />
                      <Label htmlFor="card" className="flex items-center gap-2 cursor-pointer flex-1">
                        <CreditCard className="w-5 h-5 text-[#245c1a]" />
                        <div>
                          <div className="font-medium">Credit/Debit Card</div>
                          <div className="text-sm text-gray-500">Visa, Mastercard, etc.</div>
                        </div>
                      </Label>
                      <Badge variant="secondary">
                        2-3 days
                      </Badge>
                    </div>
                  </div>
                </RadioGroup>
              </CardContent>
            </Card>
          </div>

          {/* Returns Projection */}
          <div className="space-y-6">
            <Card className="border-[#245c1a] bg-gradient-to-br from-[#245c1a]/5 to-[#245c1a]/10">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <TrendingUp className="w-5 h-5 text-[#245c1a]" />
                  Projected Returns
                </CardTitle>
                <CardDescription>30-day investment cycle with 30% daily compound returns</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="text-center p-4 bg-white rounded-lg border">
                    <div className="text-2xl font-bold text-[#245c1a]">
                      ${subscriptionAmount}
                    </div>
                    <div className="text-sm text-gray-600">Initial Investment</div>
                  </div>
                  <div className="text-center p-4 bg-white rounded-lg border">
                    <div className="text-2xl font-bold text-green-600">
                      ${returns.projected.toLocaleString(undefined, { maximumFractionDigits: 0 })}
                    </div>
                    <div className="text-sm text-gray-600">Projected Value</div>
                  </div>
                </div>
                <div className="text-center p-4 bg-white rounded-lg border">
                  <div className="text-3xl font-bold text-green-600">
                    +${returns.profit.toLocaleString(undefined, { maximumFractionDigits: 0 })}
                  </div>
                  <div className="text-sm text-gray-600">Total Profit ({returns.period})</div>
                </div>
                <div className="text-center p-3 bg-orange-50 rounded-lg border border-orange-200">
                  <div className="text-lg font-bold text-orange-800">
                    Platform Cost: ${returns.platformCost.toLocaleString()}
                  </div>
                  <div className="text-sm text-orange-600">One-time setup fee</div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Payment Details */}
        {paymentMethod === "crypto" && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Globe className="w-5 h-5 text-[#245c1a]" />
                Crypto Payment Details
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label>Network</Label>
                <Select
                  value={selectedNetwork}
                  onValueChange={(value) => setSelectedNetwork(value as CryptoNetwork)}
                  disabled={isVerifyingPayment}
                >
                  <SelectTrigger className="h-12">
                    <SelectValue placeholder="Select a network" />
                  </SelectTrigger>
                  <SelectContent>
                    {Object.entries(CRYPTO_NETWORKS).map(([networkKey, network]) => (
                      <SelectItem key={networkKey} value={networkKey}>
                        <div className="flex items-center gap-3 py-2">
                          <div className={`w-3 h-3 rounded-full ${network.color}`}></div>
                          <div>
                            <div className="font-medium">{network.name}</div>
                            <div className="text-sm text-gray-500">
                              Fees: {network.fees} • {network.confirmationTime}
                            </div>
                          </div>
                          {network.recommended && (
                            <Badge variant="secondary" className="ml-auto bg-green-100 text-green-800">
                              Recommended
                            </Badge>
                          )}
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="p-4 bg-gray-50 rounded-lg border">
                <div className="flex items-center justify-between mb-2">
                  <span className="font-medium">Wallet Address:</span>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => copyToClipboard(currentNetwork.walletAddress, "Wallet address")}
                  >
                    <Copy className="w-4 h-4" />
                  </Button>
                </div>
                <div className="font-mono text-sm bg-white p-2 rounded border break-all">
                  {currentNetwork.walletAddress}
                </div>
              </div>

              <div className="p-4 bg-blue-50 rounded-lg border border-blue-200">
                <h4 className="font-medium text-blue-800 mb-2">Payment Instructions:</h4>
                <ul className="text-sm text-blue-700 space-y-1">
                  {currentNetwork.instructions.map((instruction, index) => (
                    <li key={index} className="flex items-start gap-2">
                      <CheckCircle className="w-4 h-4 mt-0.5 flex-shrink-0" />
                      {instruction}
                    </li>
                  ))}
                </ul>
              </div>

              {currentNetwork.warnings.length > 0 && (
                <div className="p-4 bg-red-50 rounded-lg border border-red-200">
                  <h4 className="font-medium text-red-800 mb-2">⚠️ Important Warnings:</h4>
                  <ul className="text-sm text-red-700 space-y-1">
                    {currentNetwork.warnings.map((warning, index) => (
                      <li key={index} className="flex items-start gap-2">
                        <span className="text-red-500">•</span>
                        {warning}
                      </li>
                    ))}
                  </ul>
                </div>
              )}
            </CardContent>
          </Card>
        )}

        {paymentMethod === "card" && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <CreditCard className="w-5 h-5 text-[#245c1a]" />
                Card Details
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="md:col-span-2">
                  <Label htmlFor="cardNumber">Card Number</Label>
                  <Input
                    id="cardNumber"
                    placeholder="1234 5678 9012 3456"
                    value={cardDetails.number}
                    onChange={(e) => setCardDetails({ ...cardDetails, number: e.target.value })}
                    disabled={isVerifyingPayment}
                  />
                </div>
                <div>
                  <Label htmlFor="expiry">Expiry Date</Label>
                  <Input
                    id="expiry"
                    placeholder="MM/YY"
                    value={cardDetails.expiry}
                    onChange={(e) => setCardDetails({ ...cardDetails, expiry: e.target.value })}
                    disabled={isVerifyingPayment}
                  />
                </div>
                <div>
                  <Label htmlFor="cvv">CVV</Label>
                  <Input
                    id="cvv"
                    placeholder="123"
                    value={cardDetails.cvv}
                    onChange={(e) => setCardDetails({ ...cardDetails, cvv: e.target.value })}
                    disabled={isVerifyingPayment}
                  />
                </div>
                <div className="md:col-span-2">
                  <Label htmlFor="cardName">Cardholder Name</Label>
                  <Input
                    id="cardName"
                    placeholder="John Doe"
                    value={cardDetails.name}
                    onChange={(e) => setCardDetails({ ...cardDetails, name: e.target.value })}
                    disabled={isVerifyingPayment}
                  />
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Action Buttons */}
        <div className="flex gap-3">
          {paymentMethod === "crypto" && !isVerifyingPayment && (
            <Button
              onClick={handleVerifyPayment}
              className="flex-1 bg-[#245c1a] hover:bg-[#1a4513]"
              disabled={!paymentMethod}
            >
              <Shield className="w-4 h-4 mr-2" />
              Verify Payment & Continue
            </Button>
          )}
          
          {paymentMethod === "card" && (
            <Button
              onClick={handleCompleteInvestment}
              className="flex-1 bg-[#245c1a] hover:bg-[#1a4513]"
              disabled={!paymentMethod || isProcessingPayment}
            >
              {isProcessingPayment ? (
                <>
                  <Clock className="w-4 h-4 mr-2 animate-spin" />
                  Processing...
                </>
              ) : (
                <>
                  <CreditCard className="w-4 h-4 mr-2" />
                  Complete Payment
                </>
              )}
            </Button>
          )}

          {isVerifyingPayment && (
            <Button
              disabled
              className="flex-1 bg-[#245c1a] hover:bg-[#1a4513]"
            >
              <Clock className="w-4 h-4 mr-2 animate-spin" />
              Verifying Payment... ({verificationTimer}s)
            </Button>
          )}

          {!paymentMethod && (
            <Button
              onClick={handleCompleteInvestment}
              variant="outline"
              className="flex-1"
              disabled
            >
              Select Payment Method
            </Button>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
