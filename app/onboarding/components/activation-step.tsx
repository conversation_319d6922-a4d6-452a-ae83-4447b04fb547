'use client'

import { useState, useTransition } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { BarChart2 } from "lucide-react";
import { completeOnboardingAction } from "../actions";
import { OnboardingUser, OnboardingExchangeAccount } from "../lib/onboarding-data";
import { toast } from "sonner";

interface ActivationStepProps {
  user: OnboardingUser;
  exchangeAccounts: OnboardingExchangeAccount[];
  tradingStrategy?: string;
  riskLevel?: string;
}

export function ActivationStep({ 
  user, 
  exchangeAccounts, 
  tradingStrategy = "recommended", 
  riskLevel = "medium" 
}: ActivationStepProps) {
  const [isPending, startTransition] = useTransition();

  // Get the Binance account for activation
  const binanceAccount = exchangeAccounts.find(account => account.exchange === "binance");

  const handleActivateAccount = () => {
    if (!binanceAccount) {
      toast.error("No Binance account found. Please complete the exchange connection step first.");
      return;
    }

    startTransition(async () => {
      try {
        await completeOnboardingAction();
        // If we reach here, something went wrong (redirect should have happened)
        toast.error("Failed to activate account");
      } catch (error) {
        // Check if this is a redirect error (expected behavior)
        if (error && typeof error === 'object' && 'digest' in error) {
          // This is a Next.js redirect, which is expected - show success message
          toast.success("🎉 Account activated successfully! Redirecting to dashboard...");
        } else {
          // This is an actual error
          toast.error("Failed to activate account");
        }
      }
    });
  };

  return (
    <Card className="border-[#245c1a]">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <BarChart2 className="h-5 w-5" />
          Activate Your Account
        </CardTitle>
        <CardDescription>
          Complete your setup and start automated trading
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="rounded-lg border p-4 bg-gray-50">
          <h3 className="font-medium mb-2">Ready to Activate</h3>
          <p className="text-sm text-gray-500 mb-4">
            Your account is ready to be activated. Review your setup below and click activate to start trading.
          </p>

          <div className="rounded-lg border p-4 bg-white mb-4">
            <div className="flex justify-between items-center mb-2">
              <h4 className="font-medium">Account Summary</h4>
            </div>
            <ul className="space-y-2 text-sm">
              <li className="flex justify-between">
                <span className="text-gray-500">Account Email:</span>
                <span className="font-medium">{user.email}</span>
              </li>
              <li className="flex justify-between">
                <span className="text-gray-500">Account Name:</span>
                <span className="font-medium">{user.name}</span>
              </li>
              {binanceAccount && (
                <>
                  <li className="flex justify-between">
                    <span className="text-gray-500">Exchange:</span>
                    <span className="font-medium">Binance ({binanceAccount.owner})</span>
                  </li>
                  <li className="flex justify-between">
                    <span className="text-gray-500">Exchange Email:</span>
                    <span className="font-medium">{binanceAccount.email}</span>
                  </li>
                </>
              )}
              <li className="flex justify-between">
                <span className="text-gray-500">Trading Strategy:</span>
                <span className="font-medium capitalize">{tradingStrategy}</span>
              </li>
              <li className="flex justify-between">
                <span className="text-gray-500">Risk Level:</span>
                <span className="font-medium capitalize">{riskLevel}</span>
              </li>
              <li className="flex justify-between">
                <span className="text-gray-500">Status:</span>
                <span className="font-medium text-orange-600">Ready for Activation</span>
              </li>
            </ul>
          </div>

          {!binanceAccount && (
            <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-4">
              <h4 className="font-medium text-red-800 mb-2">Missing Exchange Account</h4>
              <p className="text-sm text-red-700">
                You need to connect a Binance account before you can activate trading. 
                Please go back to the exchange connection step.
              </p>
            </div>
          )}
        </div>

        <div className="bg-green-50 border border-green-200 rounded-lg p-4">
          <h4 className="font-medium text-green-800 mb-2">What happens after activation?</h4>
          <ul className="text-sm text-green-700 space-y-1">
            <li>• Your trading bot will be configured with your settings</li>
            <li>• You'll have access to the full dashboard</li>
            <li>• You can monitor your trades and adjust settings</li>
            <li>• Automated trading will begin based on your preferences</li>
          </ul>
        </div>

        <div className="flex gap-2">
          <Button variant="outline" className="flex-1">
            Complete Later
          </Button>
          <Button
            onClick={handleActivateAccount}
            className="flex-1 bg-[#245c1a] hover:bg-[#1a4513]"
            disabled={!binanceAccount || isPending}
          >
            {isPending ? "Activating..." : "🚀 Activate Account"}
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}
