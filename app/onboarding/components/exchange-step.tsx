'use client'

import { useState, useTransition } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";

import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { LinkIcon, Edit, Eye, EyeOff } from "lucide-react";
import {
  connectExchangeAccountAction,
  testExchangeConnectionAction,
  updateCredentialAction,
  type ExchangeConnectionPayload,
  type ExchangeTestPayload
} from "../actions";
import { OnboardingExchangeAccount, OnboardingCredential } from "../lib/onboarding-data";
import { toast } from "sonner";

interface ExchangeStepProps {
  exchangeAccounts: OnboardingExchangeAccount[];
  credentials: OnboardingCredential[];
  selectedProxyId?: string;
  onStepComplete: () => void;
}

interface TestResult {
  success: boolean;
  symbolCount?: number;
  timestamp: Date;
}

export function ExchangeStep({
  exchangeAccounts,
  credentials,
  selectedProxyId,
  onStepComplete
}: ExchangeStepProps) {
  const [isPending, startTransition] = useTransition();
  const [isTestingConnection, setIsTestingConnection] = useState(false);
  const [lastTestResult, setLastTestResult] = useState<TestResult | null>(null);

  // Form state for new connection
  const [selectedExchange, setSelectedExchange] = useState<'binance' | 'bybit'>('binance');
  const [accountName, setAccountName] = useState("");
  const [apiKey, setApiKey] = useState("");
  const [secretKey, setSecretKey] = useState("");
  const [exchangeEmail, setExchangeEmail] = useState("");
  const [showNewAccountForm, setShowNewAccountForm] = useState(false);
  const [isEditingCard, setIsEditingCard] = useState(false);
  const [isEditingExisting, setIsEditingExisting] = useState(false);

  // Password visibility states
  const [showApiKey, setShowApiKey] = useState(false);
  const [showSecretKey, setShowSecretKey] = useState(false);

  // Get existing accounts for the selected exchange
  const existingAccount = exchangeAccounts.find(account => account.exchange === selectedExchange);

  // Load decrypted credentials for editing
  const loadCredentialsForEditing = () => {
    console.log("got here")
    if (!existingAccount) return;

    try {
      const credential = credentials.find(cred => cred.name === existingAccount.owner);
      console.log(credentials, "credentials");
      if (credential) {
        setApiKey(credential.api_key);
        setSecretKey(credential.api_secret);
        setExchangeEmail(existingAccount.email);
        setAccountName(existingAccount.owner);
        setSelectedExchange(existingAccount.exchange as 'binance' | 'bybit');
      } else {
        toast.error("Credentials not found for this account");
      }
    } catch (error) {
      toast.error("Failed to load credentials for editing");
    }
  };

  const handleConnectExchange = () => {
    if (!apiKey || !secretKey || !exchangeEmail || !accountName) {
      toast.error("All fields are required");
      return;
    }

    startTransition(async () => {
      if (isEditingExisting && existingAccount) {
        // Update existing credential
        const updateResult = await updateCredentialAction({
          owner: existingAccount.owner,
          apiKey,
          secretKey,
          exchangeEmail,
        });

        if (updateResult.success) {
          toast.success(updateResult.message || "Account updated successfully");
          // Clear form and exit edit mode
          setApiKey("");
          setSecretKey("");
          setExchangeEmail("");
          setAccountName("");
          setIsEditingExisting(false);
          setIsEditingCard(false);
          setShowNewAccountForm(false);
          // Complete the step
          onStepComplete();
        } else {
          toast.error(updateResult.error || "Failed to update account");
        }
      } else {
        // Create new connection
        const payload: ExchangeConnectionPayload = {
          apiKey,
          secretKey,
          exchangeEmail,
          exchange: selectedExchange,
          name: accountName,
          proxyId: selectedProxyId,
        };

        const result = await connectExchangeAccountAction(payload);

        if (result.success) {
          toast.success(result.message);
          // Clear form
          setApiKey("");
          setSecretKey("");
          setExchangeEmail("");
          setAccountName("");
          setIsEditingExisting(false);
          setIsEditingCard(false);
          setShowNewAccountForm(false);
          // Complete the step
          onStepComplete();
        } else {
          toast.error(result.error || `Failed to connect ${selectedExchange} account`);
        }
      }
    });
  };

  const handleTestConnection = async () => {
    if (!existingAccount) return;

    setIsTestingConnection(true);

    try {
      const payload: ExchangeTestPayload = {
        owner: existingAccount.owner,
        exchange: existingAccount.exchange,
      };

      const result = await testExchangeConnectionAction(payload);

      if (result.success && result.data?.connected) {
        setLastTestResult({
          success: true,
          symbolCount: result.data.symbolCount || 0,
          timestamp: new Date(),
        });

        toast.success(`🎉 Connection Successful! Found ${result.data.symbolCount || 0} trading pairs`);
      } else {
        setLastTestResult({
          success: false,
          timestamp: new Date(),
        });

        toast.error(result.error || "Connection test failed");
      }
    } catch (error) {
      setLastTestResult({
        success: false,
        timestamp: new Date(),
      });

      toast.error("Failed to test connection");
    } finally {
      setIsTestingConnection(false);
    }
  };

  const handleContinueWithExisting = () => {
    onStepComplete();
  };

  return (
    <div className="space-y-4">
      {existingAccount ? (
        // Show existing account with integrated edit functionality
        <div className="space-y-4">
          <Card className="border-blue-100 bg-blue-50">
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <LinkIcon className="h-4 w-4 text-blue-600" />
                  <CardTitle className="text-blue-800">
                    {existingAccount.exchange.charAt(0).toUpperCase() + existingAccount.exchange.slice(1)} Account
                  </CardTitle>
                </div>
                {!isEditingCard && (
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => {
                      setIsEditingCard(true);
                      setIsEditingExisting(true);
                      loadCredentialsForEditing();
                    }}
                    className="text-blue-600 hover:text-blue-800"
                  >
                    <Edit className="h-4 w-4 mr-1" />
                    Edit
                  </Button>
                )}
              </div>
            </CardHeader>
            <CardContent>
              {!isEditingCard ? (
                // Summary view
                <div className="space-y-3">
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span className="text-gray-600">Account Names:</span>
                      <span className="font-medium">{existingAccount.owner}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Email:</span>
                      <span className="font-medium">{existingAccount.email}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Exchange:</span>
                      <span className="font-medium capitalize">{existingAccount.exchange}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Connected:</span>
                      <span className="font-medium">
                        {new Date(existingAccount.created).toLocaleDateString()}
                      </span>
                    </div>
                    {lastTestResult && (
                      <div className="flex justify-between">
                        <span className="text-gray-600">Last Test:</span>
                        <span className={`font-medium ${lastTestResult.success ? 'text-green-600' : 'text-red-600'}`}>
                          {lastTestResult.success
                            ? `✅ Success (${lastTestResult.symbolCount} pairs)`
                            : '❌ Failed'
                          } - {lastTestResult.timestamp.toLocaleTimeString()}
                        </span>
                      </div>
                    )}
                  </div>

                  <div className="flex gap-2 pt-2">
                    <Button
                      variant="outline"
                      onClick={handleTestConnection}
                      disabled={isTestingConnection}
                      className="flex-1"
                    >
                      {isTestingConnection ? "Testing..." : "Test Connection"}
                    </Button>
                    <Button
                      onClick={handleContinueWithExisting}
                      className="flex-1 bg-[#245c1a] hover:bg-[#1a4513]"
                    >
                      Continue with This Account
                    </Button>
                  </div>
                </div>
              ) : (
                // Edit view - show form fields
                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="edit-account-name">Account Name</Label>
                    <Input
                      id="edit-account-name"
                      value={accountName}
                      onChange={(e) => setAccountName(e.target.value)}
                      placeholder="Enter a name for this account"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="edit-exchange-email">{selectedExchange.charAt(0).toUpperCase() + selectedExchange.slice(1)} Account Email</Label>
                    <Input
                      id="edit-exchange-email"
                      type="email"
                      value={exchangeEmail}
                      onChange={(e) => setExchangeEmail(e.target.value)}
                      placeholder={`Enter your ${selectedExchange} account email`}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="edit-api-key">{selectedExchange.charAt(0).toUpperCase() + selectedExchange.slice(1)} API Key</Label>
                    <div className="relative">
                      <Input
                        id="edit-api-key"
                        type={showApiKey ? "text" : "password"}
                        value={apiKey}
                        onChange={(e) => setApiKey(e.target.value)}
                        placeholder={`Enter your ${selectedExchange} API key`}
                      />
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                        onClick={() => setShowApiKey(!showApiKey)}
                      >
                        {showApiKey ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                      </Button>
                    </div>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="edit-secret-key">{selectedExchange.charAt(0).toUpperCase() + selectedExchange.slice(1)} Secret Key</Label>
                    <div className="relative">
                      <Input
                        id="edit-secret-key"
                        type={showSecretKey ? "text" : "password"}
                        value={secretKey}
                        onChange={(e) => setSecretKey(e.target.value)}
                        placeholder={`Enter your ${selectedExchange} secret key`}
                      />
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                        onClick={() => setShowSecretKey(!showSecretKey)}
                      >
                        {showSecretKey ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                      </Button>
                    </div>
                  </div>

                  <div className="flex gap-2 pt-2">
                    <Button
                      variant="outline"
                      onClick={() => {
                        setIsEditingCard(false);
                        setIsEditingExisting(false);
                        // Clear form
                        setApiKey("");
                        setSecretKey("");
                        setExchangeEmail("");
                        setAccountName("");
                      }}
                      className="flex-1"
                    >
                      Cancel
                    </Button>
                    <Button
                      onClick={handleConnectExchange}
                      className="flex-1 bg-[#245c1a] hover:bg-[#1a4513]"
                      disabled={!apiKey || !secretKey || !exchangeEmail || !accountName || isPending}
                    >
                      {isPending ? "Updating..." : "Save Changes"}
                    </Button>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>


          <div className="flex justify-center pt-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => {
                // Clear form for new account
                setSelectedExchange('binance');
                setExchangeEmail("");
                setApiKey("");
                setSecretKey("");
                setAccountName("");
                setIsEditingExisting(false);
                setIsEditingCard(false);
                setShowNewAccountForm(true);
              }}
              className="text-[#245c1a]"
            >
              Connect Different Account
            </Button>
          </div>
        </div>
      ) : (
        // Show message that no account exists
        <div className="text-center py-4">
          <p className="text-gray-600 mb-4">No exchange account connected yet.</p>
          <Button
            onClick={() => {
              setIsEditingExisting(false);
              setShowNewAccountForm(true);
            }}
            className="bg-[#245c1a] hover:bg-[#1a4513]"
          >
            Connect Exchange Account
          </Button>
        </div>
      )}

      {/* New Account Form */}
      {(showNewAccountForm || (!existingAccount && !isEditingCard)) && (
        <Card className="border-[#245c1a]">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <LinkIcon className="h-5 w-5" />
              Connect Account
            </CardTitle>
            <CardDescription>
              Choose an exchange and enter your API credentials to enable trading
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <Tabs value={selectedExchange} onValueChange={(value) => setSelectedExchange(value as 'binance' | 'bybit')}>
              <TabsList className="grid w-full grid-cols-2">
                <TabsTrigger value="binance">Binance</TabsTrigger>
                <TabsTrigger value="bybit">Bybit</TabsTrigger>
              </TabsList>

              <TabsContent value="binance" className="space-y-4 mt-4">
                <div className="space-y-2">
                  <Label htmlFor="account-name">Account Name</Label>
                  <Input
                    id="account-name"
                    value={accountName}
                    onChange={(e) => setAccountName(e.target.value)}
                    placeholder="Enter a name for this account (e.g., 'Main Trading')"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="exchange-email">Binance Account Email</Label>
                  <Input
                    id="exchange-email"
                    type="email"
                    value={exchangeEmail}
                    onChange={(e) => setExchangeEmail(e.target.value)}
                    placeholder="Enter your Binance account email"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="api-key">Binance API Key</Label>
                  <div className="relative">
                    <Input
                      id="api-key"
                      type={showApiKey ? "text" : "password"}
                      value={apiKey}
                      onChange={(e) => setApiKey(e.target.value)}
                      placeholder="Enter your Binance API key"
                    />
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                      onClick={() => setShowApiKey(!showApiKey)}
                    >
                      {showApiKey ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                    </Button>
                  </div>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="secret-key">Binance Secret Key</Label>
                  <div className="relative">
                    <Input
                      id="secret-key"
                      type={showSecretKey ? "text" : "password"}
                      value={secretKey}
                      onChange={(e) => setSecretKey(e.target.value)}
                      placeholder="Enter your Binance Secret key"
                    />
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                      onClick={() => setShowSecretKey(!showSecretKey)}
                    >
                      {showSecretKey ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                    </Button>
                  </div>
                </div>
                <div className="text-sm text-gray-500">
                  <p>
                    Don't have API keys?{" "}
                    <a
                      href="https://www.binance.com/en/support/faq/how-to-create-api-keys-on-binance-************"
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-[#245c1a] hover:underline"
                    >
                      Learn how to create them
                    </a>
                  </p>
                </div>
              </TabsContent>

              <TabsContent value="bybit" className="space-y-4 mt-4">
                <div className="space-y-2">
                  <Label htmlFor="account-name-bybit">Account Name</Label>
                  <Input
                    id="account-name-bybit"
                    value={accountName}
                    onChange={(e) => setAccountName(e.target.value)}
                    placeholder="Enter a name for this account (e.g., 'Main Trading')"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="exchange-email-bybit">Bybit Account Email</Label>
                  <Input
                    id="exchange-email-bybit"
                    type="email"
                    value={exchangeEmail}
                    onChange={(e) => setExchangeEmail(e.target.value)}
                    placeholder="Enter your Bybit account email"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="api-key-bybit">Bybit API Key</Label>
                  <div className="relative">
                    <Input
                      id="api-key-bybit"
                      type={showApiKey ? "text" : "password"}
                      value={apiKey}
                      onChange={(e) => setApiKey(e.target.value)}
                      placeholder="Enter your Bybit API key"
                    />
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                      onClick={() => setShowApiKey(!showApiKey)}
                    >
                      {showApiKey ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                    </Button>
                  </div>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="secret-key-bybit">Bybit Secret Key</Label>
                  <div className="relative">
                    <Input
                      id="secret-key-bybit"
                      type={showSecretKey ? "text" : "password"}
                      value={secretKey}
                      onChange={(e) => setSecretKey(e.target.value)}
                      placeholder="Enter your Bybit Secret key"
                    />
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                      onClick={() => setShowSecretKey(!showSecretKey)}
                    >
                      {showSecretKey ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                    </Button>
                  </div>
                </div>
                <div className="text-sm text-gray-500">
                  <p>
                    Don't have API keys?{" "}
                    <a
                      href="https://www.bybit.com/en-US/help-center/bybitHC_Article?language=en_US&id=*********"
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-[#245c1a] hover:underline"
                    >
                      Learn how to create them
                    </a>
                  </p>
                </div>
              </TabsContent>
            </Tabs>

            <div className="flex gap-2">
              <Button
                variant="outline"
                onClick={() => {
                  setShowNewAccountForm(false);
                  // Clear form
                  setApiKey("");
                  setSecretKey("");
                  setExchangeEmail("");
                  setAccountName("");
                  setIsEditingExisting(false);
                }}
                className="flex-1"
                disabled={!existingAccount}
              >
                {existingAccount ? "Cancel" : "Skip for Now"}
              </Button>
              <Button
                onClick={handleConnectExchange}
                className="flex-1 bg-[#245c1a] hover:bg-[#1a4513]"
                disabled={!apiKey || !secretKey || !exchangeEmail || !accountName || isPending}
              >
                {isPending
                  ? "Connecting..."
                  : `Connect ${selectedExchange.charAt(0).toUpperCase() + selectedExchange.slice(1)} Account`
                }
              </Button>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
