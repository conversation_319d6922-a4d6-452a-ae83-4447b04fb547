'use client'

import { useState, useTransition } from "react";
import { useRouter } from "next/navigation";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { Plus, Network, Check, Globe, Shield, ExternalLink, ShoppingCart } from "lucide-react";
import { createProxyAction, type ProxySetupPayload } from "../actions";
import { OnboardingProxy } from "../lib/onboarding-data";
import { toast } from "sonner";

interface ProxyStepProps {
  proxies: OnboardingProxy[];
  selectedProxyId?: string;
  onProxySelect: (proxyId: string) => void;
  onStepComplete: () => void;
}

export function ProxyStep({
  proxies,
  selectedProxyId,
  onProxySelect,
  onStepComplete
}: ProxyStepProps) {
  const router = useRouter();
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [isPending, startTransition] = useTransition();

  // Create proxy form state
  const [proxyUsername, setProxyUsername] = useState("");
  const [proxyPassword, setProxyPassword] = useState("");
  const [proxyHost, setProxyHost] = useState("");
  const [proxyPort, setProxyPort] = useState("");
  const [proxyType, setProxyType] = useState<"http" | "socks5">("http");

  const handleCreateProxy = () => {
    if (!proxyHost || !proxyPort) {
      toast.error("Host and port are required");
      return;
    }

    startTransition(async () => {
      const payload: ProxySetupPayload = {
        username: proxyUsername || undefined,
        password: proxyPassword || undefined,
        host: proxyHost,
        port: parseInt(proxyPort),
        type: proxyType,
      };

      const result = await createProxyAction(payload);

      if (result.success) {
        toast.success(result.message);
        // Select the newly created proxy
        if (result.data?.proxyId) {
          onProxySelect(result.data.proxyId);
        }
        // Clear form
        setProxyUsername("");
        setProxyPassword("");
        setProxyHost("");
        setProxyPort("");
        setProxyType("http");
        // Hide create form
        setShowCreateForm(false);
        // Complete the step
        onStepComplete();
      } else {
        toast.error(result.error || "Failed to create proxy");
      }
    });
  };

  const handleContinueWithSelected = () => {
    if (selectedProxyId) {
      onStepComplete();
    }
  };

  return (
    <div className="space-y-4">
      {/* Existing Proxies */}
      {proxies.length > 0 && (
        <div className="space-y-3">
          <Label className="text-sm font-medium">Select an existing proxy:</Label>
          <RadioGroup value={selectedProxyId} onValueChange={onProxySelect}>
            {proxies.map((proxy) => (
              <div key={proxy.id} className="flex items-center space-x-3">
                <RadioGroupItem value={proxy.id} id={proxy.id} />
                <Label htmlFor={proxy.id} className="flex-1 cursor-pointer">
                  <Card className={`transition-colors ${selectedProxyId === proxy.id ? 'border-[#245c1a] bg-green-50' : 'hover:bg-gray-50'}`}>
                    <CardContent className="p-4">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-3">
                          <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                            <Network className="h-5 w-5 text-blue-600" />
                          </div>
                          <div>
                            <div className="flex items-center gap-2">
                              <span className="font-medium">{proxy.ip_address}</span>
                              <Badge variant="outline" className="text-xs">
                                {proxy.type.toUpperCase()}
                              </Badge>
                            </div>
                            <p className="text-sm text-muted-foreground">
                              Created {new Date(proxy.created).toLocaleDateString()}
                            </p>
                          </div>
                        </div>
                        {selectedProxyId === proxy.id && (
                          <div className="flex items-center gap-2">
                            <Check className="h-4 w-4 text-[#245c1a]" />
                            <Badge className="bg-green-100 text-green-800">Selected</Badge>
                          </div>
                        )}
                      </div>
                    </CardContent>
                  </Card>
                </Label>
              </div>
            ))}
          </RadioGroup>
        </div>
      )}

      {/* Separator */}
      {proxies.length > 0 && (
        <div className="flex items-center gap-4">
          <Separator className="flex-1" />
          <span className="text-sm text-muted-foreground">or</span>
          <Separator className="flex-1" />
        </div>
      )}

      {/* Create New Proxy */}
      <div className="space-y-3">
        {!showCreateForm ? (
          <Button
            variant="outline"
            onClick={() => setShowCreateForm(true)}
            className="w-full border-dashed border-2 h-auto p-4 flex flex-col items-center gap-2 hover:border-[#245c1a] hover:bg-green-50"
          >
            <Plus className="h-5 w-5" />
            <span>Create New Proxy</span>
            <span className="text-xs text-muted-foreground">
              {proxies.length === 0 ? "No existing proxies found" : "Add another proxy configuration"}
            </span>
          </Button>
        ) : (
          <Card className="border-[#245c1a]">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Plus className="h-5 w-5" />
                Create New Proxy
              </CardTitle>
              <CardDescription>
                Configure a new proxy for secure trading connections
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="new-proxy-username">Username (Optional)</Label>
                  <Input
                    id="new-proxy-username"
                    value={proxyUsername}
                    onChange={(e) => setProxyUsername(e.target.value)}
                    placeholder="Enter proxy username"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="new-proxy-password">Password (Optional)</Label>
                  <Input
                    id="new-proxy-password"
                    type="password"
                    value={proxyPassword}
                    onChange={(e) => setProxyPassword(e.target.value)}
                    placeholder="Enter proxy password"
                  />
                </div>
              </div>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="space-y-2 md:col-span-2">
                  <Label htmlFor="new-proxy-host">Host/IP Address *</Label>
                  <Input
                    id="new-proxy-host"
                    value={proxyHost}
                    onChange={(e) => setProxyHost(e.target.value)}
                    placeholder="e.g., backup.beeola.me"
                    required
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="new-proxy-port">Port *</Label>
                  <Input
                    id="new-proxy-port"
                    type="number"
                    value={proxyPort}
                    onChange={(e) => setProxyPort(e.target.value)}
                    placeholder="e.g., 8888"
                    required
                  />
                </div>
              </div>
              <div className="space-y-2">
                <Label htmlFor="new-proxy-type">Proxy Type</Label>
                <Select value={proxyType} onValueChange={(value: "http" | "socks5") => setProxyType(value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select proxy type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="http">HTTP</SelectItem>
                    <SelectItem value="socks5">SOCKS5</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="text-sm text-gray-500 bg-gray-50 p-3 rounded-lg">
                <div className="flex items-center gap-2 mb-1">
                  <Globe className="h-4 w-4" />
                  <span className="font-medium">Preview:</span>
                </div>
                <p className="font-mono text-xs">
                  {proxyType}:{proxyUsername && proxyPassword ? `${proxyUsername}:${proxyPassword}@` : ""}{proxyHost || "host"}:{proxyPort || "port"}
                </p>
              </div>
              <div className="flex gap-2">
                <Button variant="outline" onClick={() => setShowCreateForm(false)} className="flex-1">
                  Cancel
                </Button>
                <Button
                  onClick={handleCreateProxy}
                  className="flex-1 bg-[#245c1a] hover:bg-[#1a4513]"
                  disabled={!proxyHost || !proxyPort || isPending}
                >
                  {isPending ? "Creating..." : "Create Proxy"}
                </Button>
              </div>
            </CardContent>
          </Card>
        )}
      </div>

      {/* Proxy Purchase Option */}
      {proxies.length === 0 && !showCreateForm && (
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="font-medium text-blue-800">Need a Proxy?</h3>
              <p className="text-sm text-blue-700">
                Get a reliable proxy service for secure trading connections
              </p>
            </div>
            <Button
              variant="outline"
              size="sm"
              className="border-blue-300 text-blue-700 hover:bg-blue-100"
              onClick={() => router.push('/proxy/purchase?returnTo=/onboarding')}
            >
              <ShoppingCart className="h-4 w-4 mr-2" />
              Get Proxy
            </Button>
          </div>
        </div>
      )}

      {/* Action Buttons */}
      {!showCreateForm && (
        <div className="flex gap-2 mt-4">
          <Button variant="outline" className="flex-1">
            Skip for Now
          </Button>
          <Button
            onClick={handleContinueWithSelected}
            className="flex-1 bg-[#245c1a] hover:bg-[#1a4513]"
            disabled={!selectedProxyId}
          >
            Continue with Selected Proxy
          </Button>
        </div>
      )}

      {/* Info Section */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <div className="flex items-center gap-2 mb-2">
          <Shield className="h-4 w-4 text-blue-600" />
          <span className="text-sm font-medium text-blue-800">About Proxies</span>
        </div>
        <p className="text-xs text-blue-700">
          Proxies help secure your trading connections and may be required for certain regions. 
          You can reuse existing proxies across multiple trading accounts or create new ones as needed.
        </p>
      </div>
    </div>
  );
}
