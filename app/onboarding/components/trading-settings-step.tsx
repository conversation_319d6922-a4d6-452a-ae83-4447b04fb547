'use client'

import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Settings } from "lucide-react";
// Removed backend action import since we're not saving to backend
import { toast } from "sonner";

interface TradingSettingsStepProps {
  onStepComplete: () => void;
}

export function TradingSettingsStep({ onStepComplete }: TradingSettingsStepProps) {
  const [tradingStrategy, setTradingStrategy] = useState("recommended");
  const [riskLevel, setRiskLevel] = useState("medium");
  const [takeProfit, setTakeProfit] = useState("3.5");
  const [stopLoss, setStopLoss] = useState("2.5");
  const [maxTradeSize, setMaxTradeSize] = useState("10");

  const handleSaveSettings = () => {
    // Just complete the step without sending to backend
    toast.success("Trading settings configured successfully");
    onStepComplete();
  };

  const handleCustomTradingChange = (field: string, value: string) => {
    switch (field) {
      case "riskLevel":
        setRiskLevel(value);
        break;
      case "takeProfit":
        setTakeProfit(value);
        break;
      case "stopLoss":
        setStopLoss(value);
        break;
      case "maxTradeSize":
        setMaxTradeSize(value);
        break;
      default:
        break;
    }
  };

  return (
    <Card className="border-[#245c1a]">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Settings className="h-5 w-5" />
          Set Trading Instructions
        </CardTitle>
        <CardDescription>
          Configure how your trading bot will operate
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <Tabs value={tradingStrategy} onValueChange={setTradingStrategy}>
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="recommended" className="text-xs sm:text-sm">
              System Recommended
            </TabsTrigger>
            <TabsTrigger value="custom" className="text-xs sm:text-sm">
              Custom Settings
            </TabsTrigger>
          </TabsList>
          
          <TabsContent value="recommended" className="space-y-4 pt-4">
            <div className="rounded-lg border p-4">
              <h3 className="font-medium mb-2">Recommended Trading Strategy</h3>
              <p className="text-sm text-gray-500 mb-4">
                Our algorithm has analyzed market conditions and recommends the following settings for
                BTC/USDT trading:
              </p>
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span>Risk Level:</span>
                  <span className="font-medium">Medium</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span>Take Profit:</span>
                  <span className="font-medium">3.5%</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span>Stop Loss:</span>
                  <span className="font-medium">2.5%</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span>Max Trade Size:</span>
                  <span className="font-medium">10% of available funds</span>
                </div>
              </div>
            </div>
          </TabsContent>
          
          <TabsContent value="custom" className="space-y-4 pt-4">
            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="risk-level">Risk Level</Label>
                <RadioGroup
                  value={riskLevel}
                  onValueChange={(value) => handleCustomTradingChange("riskLevel", value)}
                >
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="low" id="risk-low" />
                    <Label htmlFor="risk-low">Low - Conservative approach with smaller gains</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="medium" id="risk-medium" />
                    <Label htmlFor="risk-medium">Medium - Balanced risk and reward</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="high" id="risk-high" />
                    <Label htmlFor="risk-high">High - Aggressive trading for higher returns</Label>
                  </div>
                </RadioGroup>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="take-profit">Take Profit (%)</Label>
                  <Input
                    id="take-profit"
                    type="number"
                    value={takeProfit}
                    onChange={(e) => handleCustomTradingChange("takeProfit", e.target.value)}
                    min="0.1"
                    step="0.1"
                    placeholder="e.g., 3.5"
                  />
                  <p className="text-xs text-gray-500">
                    Percentage gain at which to sell and take profit
                  </p>
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="stop-loss">Stop Loss (%)</Label>
                  <Input
                    id="stop-loss"
                    type="number"
                    value={stopLoss}
                    onChange={(e) => handleCustomTradingChange("stopLoss", e.target.value)}
                    min="0.1"
                    step="0.1"
                    placeholder="e.g., 2.5"
                  />
                  <p className="text-xs text-gray-500">
                    Percentage loss at which to sell and limit losses
                  </p>
                </div>
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="max-trade">Max Trade Size (% of funds)</Label>
                <Input
                  id="max-trade"
                  type="number"
                  value={maxTradeSize}
                  onChange={(e) => handleCustomTradingChange("maxTradeSize", e.target.value)}
                  min="1"
                  max="100"
                  placeholder="e.g., 10"
                />
                <p className="text-xs text-gray-500">
                  Maximum percentage of available funds to use per trade
                </p>
              </div>
            </div>
          </TabsContent>
        </Tabs>

        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <h4 className="font-medium text-blue-800 mb-2">Trading Strategy Info:</h4>
          <p className="text-sm text-blue-700">
            These settings control how your automated trading bot will operate. You can always
            adjust these settings later from your dashboard. Start with recommended settings
            if you're new to automated trading.
          </p>
        </div>

        <div className="flex gap-2">
          <Button variant="outline" className="flex-1">
            Skip for Now
          </Button>
          <Button
            onClick={handleSaveSettings}
            className="flex-1 bg-[#245c1a] hover:bg-[#1a4513]"
          >
            Save Trading Settings
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}
