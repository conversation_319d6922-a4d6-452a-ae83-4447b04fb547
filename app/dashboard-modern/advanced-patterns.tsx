/**
 * Advanced "Fetch as you Render" Patterns
 * 
 * This file demonstrates advanced patterns for integrating
 * Server Components with your existing React Query setup
 */

import { Suspense } from "react";
import { HydrationBoundary, dehydrate, QueryClient } from "@tanstack/react-query";

// ==========================================
// Pattern 1: Server Component + Client Hydration
// ==========================================

/**
 * Server Component that prefetches data and hydrates React Query
 */
export async function HydratedDashboard({ userEmail }: { userEmail: string }) {
  const queryClient = new QueryClient();

  // Prefetch data on the server
  await queryClient.prefetchQuery({
    queryKey: ['user', userEmail],
    queryFn: () => fetchUserData(userEmail),
  });

  await queryClient.prefetchQuery({
    queryKey: ['trading-accounts', userEmail],
    queryFn: () => fetchTradingAccounts(userEmail),
  });

  return (
    <HydrationBoundary state={dehydrate(queryClient)}>
      <ClientDashboard userEmail={userEmail} />
    </HydrationBoundary>
  );
}

/**
 * Client Component that uses the prefetched data
 */
"use client";
function ClientDashboard({ userEmail }: { userEmail: string }) {
  // This data is already available from server prefetch
  const { data: user } = useQuery({
    queryKey: ['user', userEmail],
    queryFn: () => fetchUserData(userEmail),
  });

  const { data: accounts } = useQuery({
    queryKey: ['trading-accounts', userEmail], 
    queryFn: () => fetchTradingAccounts(userEmail),
  });

  return (
    <div>
      <UserProfile user={user} />
      <TradingAccountsList accounts={accounts} />
    </div>
  );
}

// ==========================================
// Pattern 2: Streaming with React Query
// ==========================================

/**
 * Combines Server Components for initial load with React Query for updates
 */
export async function StreamingDashboard({ userEmail }: { userEmail: string }) {
  return (
    <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
      {/* Fast loading server component */}
      <Suspense fallback={<UserSkeleton />}>
        <ServerUserProfile userEmail={userEmail} />
      </Suspense>

      {/* Slow loading server component that streams in */}
      <Suspense fallback={<AccountsSkeleton />}>
        <ServerTradingAccounts userEmail={userEmail} />
      </Suspense>

      {/* Real-time client component with React Query */}
      <Suspense fallback={<MarketSkeleton />}>
        <ClientMarketData />
      </Suspense>
    </div>
  );
}

// Server Components for initial data
async function ServerUserProfile({ userEmail }: { userEmail: string }) {
  const user = await fetchUserData(userEmail);
  return <UserProfileCard user={user} />;
}

async function ServerTradingAccounts({ userEmail }: { userEmail: string }) {
  // Simulate slow API
  await new Promise(resolve => setTimeout(resolve, 2000));
  const accounts = await fetchTradingAccounts(userEmail);
  return <TradingAccountsList accounts={accounts} />;
}

// Client Component for real-time data
"use client";
function ClientMarketData() {
  const { data: marketData } = useQuery({
    queryKey: ['market-data'],
    queryFn: fetchMarketData,
    refetchInterval: 5000, // Update every 5 seconds
  });

  return <MarketOverview data={marketData} />;
}

// ==========================================
// Pattern 3: Progressive Enhancement
// ==========================================

/**
 * Server Component provides initial data, client enhances with real-time updates
 */
export async function ProgressiveEnhancementExample({ userEmail }: { userEmail: string }) {
  // Get initial data on server
  const initialData = await fetchDashboardData(userEmail);

  return (
    <div>
      {/* Server-rendered content */}
      <StaticContent data={initialData} />
      
      {/* Client-enhanced content */}
      <Suspense fallback={<div>Loading interactive features...</div>}>
        <EnhancedContent initialData={initialData} userEmail={userEmail} />
      </Suspense>
    </div>
  );
}

function StaticContent({ data }: { data: any }) {
  return (
    <div>
      <h1>Welcome back, {data.user.name}</h1>
      <p>Account Balance: {data.totalBalance}</p>
    </div>
  );
}

"use client";
function EnhancedContent({ initialData, userEmail }: { initialData: any; userEmail: string }) {
  // Use server data as initial data for React Query
  const { data: liveData } = useQuery({
    queryKey: ['dashboard-live', userEmail],
    queryFn: () => fetchLiveDashboardData(userEmail),
    initialData,
    refetchInterval: 10000,
  });

  return (
    <div>
      <LiveChart data={liveData.chartData} />
      <RealtimeNotifications data={liveData.notifications} />
    </div>
  );
}

// ==========================================
// Pattern 4: Error Boundaries with Fallbacks
// ==========================================

export async function RobustDashboard({ userEmail }: { userEmail: string }) {
  return (
    <div>
      <Suspense 
        fallback={<UserSkeleton />}
      >
        <ErrorBoundaryWrapper fallback={<UserErrorFallback />}>
          <UserSection userEmail={userEmail} />
        </ErrorBoundaryWrapper>
      </Suspense>

      <Suspense 
        fallback={<AccountsSkeleton />}
      >
        <ErrorBoundaryWrapper fallback={<AccountsErrorFallback />}>
          <AccountsSection userEmail={userEmail} />
        </ErrorBoundaryWrapper>
      </Suspense>
    </div>
  );
}

async function UserSection({ userEmail }: { userEmail: string }) {
  try {
    const user = await fetchUserData(userEmail);
    return <UserProfile user={user} />;
  } catch (error) {
    // Server-side error handling
    console.error('Failed to fetch user:', error);
    throw error; // Let error boundary handle it
  }
}

// ==========================================
// Pattern 5: Conditional Rendering with Data
// ==========================================

export async function ConditionalDashboard({ userEmail }: { userEmail: string }) {
  const user = await fetchUserData(userEmail);
  
  // Conditional rendering based on server data
  if (!user.hasCompletedOnboarding) {
    return <OnboardingFlow user={user} />;
  }

  if (!user.hasActiveSubscription) {
    return <SubscriptionRequired user={user} />;
  }

  // Full dashboard for verified users
  return (
    <div>
      <Suspense fallback={<DashboardSkeleton />}>
        <FullDashboard user={user} />
      </Suspense>
    </div>
  );
}

// ==========================================
// Utility Functions (would be in separate files)
// ==========================================

async function fetchUserData(userEmail: string) {
  // Your existing API call
  const response = await fetch(`/api/user?email=${userEmail}`);
  return response.json();
}

async function fetchTradingAccounts(userEmail: string) {
  const response = await fetch(`/api/trading-accounts?email=${userEmail}`);
  return response.json();
}

async function fetchMarketData() {
  const response = await fetch('/api/market-data');
  return response.json();
}

async function fetchDashboardData(userEmail: string) {
  const response = await fetch(`/api/dashboard?email=${userEmail}`);
  return response.json();
}

async function fetchLiveDashboardData(userEmail: string) {
  const response = await fetch(`/api/dashboard/live?email=${userEmail}`);
  return response.json();
}

/**
 * Key Takeaways:
 * 
 * 1. **Hybrid Approach**: Use Server Components for initial data, React Query for updates
 * 2. **Progressive Enhancement**: Start with server-rendered content, enhance with client features
 * 3. **Error Resilience**: Each section can fail independently without breaking the whole page
 * 4. **Performance**: Users see content immediately, then get enhanced features
 * 5. **SEO Benefits**: Initial content is server-rendered and indexable
 */
