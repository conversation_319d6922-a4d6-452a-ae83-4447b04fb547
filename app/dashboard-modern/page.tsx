import { Suspense } from "react";
import { currentUser } from "@clerk/nextjs/server";
import { redirect } from "next/navigation";
import { createUltimateService } from "@/lib/services/ultimate-service";

// Server Components for data fetching
import { UserProfileCard } from "./components/user-profile-card";
import { TradingAccountsList } from "./components/trading-accounts-list";
import { RecentActivity } from "./components/recent-activity";
import { MarketOverview } from "./components/market-overview";

// Loading components
import { UserProfileSkeleton } from "./components/skeletons/user-profile-skeleton";
import { TradingAccountsSkeleton } from "./components/skeletons/trading-accounts-skeleton";
import { RecentActivitySkeleton } from "./components/skeletons/recent-activity-skeleton";
import { MarketOverviewSkeleton } from "./components/skeletons/market-overview-skeleton";

// Client components for interactive features
import { DashboardActions } from "./components/dashboard-actions";

/**
 * Modern Dashboard Page using "Fetch as you Render" pattern
 * 
 * Key improvements:
 * 1. Server Components fetch data immediately when rendering starts
 * 2. Suspense boundaries allow progressive loading
 * 3. Each section can load independently
 * 4. Better user experience with streaming
 */
export default async function ModernDashboardPage() {
  // Server-side authentication check - happens immediately
  let clerkUser;
  let userEmail;

  try {
    clerkUser = await currentUser();

    if (!clerkUser) {
      redirect("/auth/login");
    }

    userEmail = clerkUser.emailAddresses[0]?.emailAddress;

    if (!userEmail) {
      redirect("/auth/login");
    }
  } catch (error) {
    console.error("Authentication error:", error);
    redirect("/auth/login");
  }

  return (
    <div className="min-h-screen bg-background">

      <main className="container mx-auto px-4 py-8">
        <div className="mb-6">
          <h1 className="text-3xl font-bold">Modern Dashboard</h1>
          <p className="text-muted-foreground">
            Demonstrating "Fetch as you Render" pattern with progressive loading
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* User Profile Section - Fast loading */}
          <div className="lg:col-span-1">
            <Suspense fallback={<UserProfileSkeleton />}>
              <UserProfileCard
                userEmail={userEmail}
                userName={(clerkUser.firstName || "") + " " + (clerkUser.lastName || "")}
              />
            </Suspense>
          </div>

          {/* Trading Accounts Section - Medium loading */}
          <div className="lg:col-span-2">
            <Suspense fallback={<TradingAccountsSkeleton />}>
              <TradingAccountsList
                userEmail={userEmail}
              />
            </Suspense>
          </div>

          {/* Market Overview - Fast loading (cached data) */}
          <div className="lg:col-span-2">
            <Suspense fallback={<MarketOverviewSkeleton />}>
              <MarketOverview />
            </Suspense>
          </div>

          {/* Recent Activity - Slow loading */}
          <div className="lg:col-span-1">
            <Suspense fallback={<RecentActivitySkeleton />}>
              <RecentActivity
                userEmail={userEmail}
              />
            </Suspense>
          </div>
        </div>

        {/* Client-side interactive actions */}
        <DashboardActions />
      </main>
    </div>
  );
}

/**
 * This page demonstrates several key concepts:
 * 
 * 1. **Server Components**: The main page component is async and fetches data on the server
 * 2. **Suspense Boundaries**: Each section has its own loading state
 * 3. **Progressive Loading**: Fast sections load first, slow sections stream in
 * 4. **Separation of Concerns**: Server components for data, client components for interactivity
 * 5. **Better UX**: Users see content as soon as it's available, not all at once
 */
