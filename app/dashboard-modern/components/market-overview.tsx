import { Card, CardContent, CardDescription, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { TrendingUp, TrendingDown, BarChart3 } from "lucide-react";

/**
 * Server Component that fetches market data
 * This demonstrates caching and fast-loading data
 */
export async function MarketOverview() {
  // Simulate fetching market data (in real app, this might come from an API)
  // This could be cached for better performance
  const marketData = await getMarketData();

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <BarChart3 className="h-5 w-5" />
          Market Overview
        </CardTitle>
        <CardDescription>Current market trends and popular pairs</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {marketData.map((coin) => (
            <div key={coin.symbol} className="border rounded-lg p-3">
              <div className="flex items-center justify-between mb-2">
                <div className="flex items-center gap-2">
                  <div className="w-8 h-8 bg-orange-100 rounded-full flex items-center justify-center">
                    <span className="text-orange-600 font-semibold text-xs">
                      {coin.symbol.slice(0, 2)}
                    </span>
                  </div>
                  <span className="font-semibold">{coin.symbol}</span>
                </div>
                <Badge variant={coin.change >= 0 ? "default" : "destructive"} className={
                  coin.change >= 0 ? "bg-green-100 text-green-800" : ""
                }>
                  {coin.change >= 0 ? (
                    <TrendingUp className="h-3 w-3 mr-1" />
                  ) : (
                    <TrendingDown className="h-3 w-3 mr-1" />
                  )}
                  {coin.change >= 0 ? '+' : ''}{coin.change.toFixed(2)}%
                </Badge>
              </div>
              <div className="text-right">
                <p className="font-semibold">${coin.price.toLocaleString()}</p>
                <p className="text-sm text-muted-foreground">
                  Vol: ${coin.volume.toLocaleString()}
                </p>
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
}

/**
 * Simulated market data fetching
 * In a real app, this would fetch from an external API
 * and could be cached using Next.js caching mechanisms
 */
async function getMarketData() {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 100));
  
  return [
    {
      symbol: "BTC/USDT",
      price: 43250.67,
      change: 2.45,
      volume: 1234567890
    },
    {
      symbol: "ETH/USDT", 
      price: 2567.89,
      change: -1.23,
      volume: 987654321
    },
    {
      symbol: "BNB/USDT",
      price: 315.45,
      change: 0.87,
      volume: 456789123
    },
    {
      symbol: "ADA/USDT",
      price: 0.4567,
      change: -3.21,
      volume: 234567890
    },
    {
      symbol: "SOL/USDT",
      price: 98.76,
      change: 5.43,
      volume: 345678901
    },
    {
      symbol: "DOT/USDT",
      price: 7.89,
      change: 1.65,
      volume: 123456789
    }
  ];
}

/**
 * This component demonstrates:
 * 
 * 1. **Fast Server-Side Data**: Quick loading market data
 * 2. **Caching Potential**: Data that could be cached for performance
 * 3. **Rich UI**: Complex layouts with real-time-looking data
 * 4. **Responsive Design**: Grid layout that adapts to screen size
 */
