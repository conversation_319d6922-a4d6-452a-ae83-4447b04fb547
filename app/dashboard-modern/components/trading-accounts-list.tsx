import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { TrendingUp, TrendingDown, DollarSign, Settings, Plus } from "lucide-react";
import Link from "next/link";

interface TradingAccountsListProps {
  userEmail: string;
}

/**
 * Server Component that fetches trading accounts data
 * Demonstrates fetching related data and handling empty states
 */
export async function TradingAccountsList({ userEmail }: TradingAccountsListProps) {
  // Fetch trading accounts for this user
  const accounts = await fetchTradingAccounts(userEmail);

  if (!accounts) {
    return <TradingAccountsError />;
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <DollarSign className="h-5 w-5" />
              Trading Accounts
            </CardTitle>
            <CardDescription>
              {accounts.length === 0 
                ? "No trading accounts configured" 
                : `${accounts.length} account${accounts.length === 1 ? '' : 's'} configured`
              }
            </CardDescription>
          </div>
          <Link href="/onboarding">
            <Button size="sm" variant="outline">
              <Plus className="h-4 w-4 mr-2" />
              Add Account
            </Button>
          </Link>
        </div>
      </CardHeader>
      <CardContent>
        {accounts.length === 0 ? (
          <EmptyAccountsState />
        ) : (
          <div className="space-y-4">
            {accounts.map((account: any) => (
              <AccountCard key={account.id} account={account} />
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  );
}

function AccountCard({ account }: { account: any }) {
  const isActive = true; // You can determine this based on your business logic
  const balance = `$${(account.usdt + account.usdc).toLocaleString()}`;
  
  return (
    <div className="border rounded-lg p-4 space-y-3">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <div className="w-10 h-10 bg-orange-100 rounded-full flex items-center justify-center">
            <span className="text-orange-600 font-semibold text-sm">
              {account.exchange.charAt(0).toUpperCase()}
            </span>
          </div>
          <div>
            <h3 className="font-semibold">{account.owner}</h3>
            <p className="text-sm text-muted-foreground capitalize">
              {account.exchange} • {account.email}
            </p>
          </div>
        </div>
        <div className="text-right">
          <p className="font-semibold">{balance}</p>
          <div className="flex items-center gap-2">
            {isActive ? (
              <Badge variant="default" className="bg-green-100 text-green-800">
                Active
              </Badge>
            ) : (
              <Badge variant="secondary">Inactive</Badge>
            )}
          </div>
        </div>
      </div>

      <div className="grid grid-cols-2 gap-4 text-sm">
        <div className="flex items-center gap-2">
          <TrendingUp className="h-4 w-4 text-green-600" />
          <span>Bullish: {account.bullish ? 'Yes' : 'No'}</span>
        </div>
        <div className="flex items-center gap-2">
          <TrendingDown className="h-4 w-4 text-red-600" />
          <span>Bearish: {account.bearish ? 'Yes' : 'No'}</span>
        </div>
        <div>
          <span>Risk: {account.totalRisk}%</span>
        </div>
        <div>
          <span>Profit Target: {account.profit_percent}%</span>
        </div>
      </div>

      <div className="flex justify-end">
        <Button size="sm" variant="outline">
          <Settings className="h-4 w-4 mr-2" />
          Configure
        </Button>
      </div>
    </div>
  );
}

function EmptyAccountsState() {
  return (
    <div className="text-center py-8">
      <div className="w-16 h-16 bg-muted rounded-full flex items-center justify-center mx-auto mb-4">
        <DollarSign className="h-8 w-8 text-muted-foreground" />
      </div>
      <h3 className="font-semibold mb-2">No Trading Accounts</h3>
      <p className="text-muted-foreground mb-4">
        Get started by connecting your first exchange account
      </p>
      <Link href="/onboarding">
        <Button>
          <Plus className="h-4 w-4 mr-2" />
          Add Your First Account
        </Button>
      </Link>
    </div>
  );
}

function TradingAccountsError() {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-destructive">Error Loading Accounts</CardTitle>
        <CardDescription>
          Unable to load your trading accounts. Please try again later.
        </CardDescription>
      </CardHeader>
    </Card>
  );
}

/**
 * Simulated trading accounts data fetching for demo purposes
 */
async function fetchTradingAccounts(userEmail: string) {
  // Simulate medium-speed API call
  await new Promise(resolve => setTimeout(resolve, 500));

  // Return demo trading accounts
  return [
    {
      id: "account-1",
      exchange: "binance",
      owner: "main_account",
      email: userEmail,
      usdt: 5000,
      usdc: 3000,
      proxy: "proxy-1",
      bullish: true,
      bearish: false,
      totalRisk: 15,
      movePercent: 5,
      max_non_essential: 20,
      profit_percent: 10,
      risk_reward: 2.5,
      exclude_coins: ["DOGE", "SHIB"],
      include_delisted: false,
      user: "user-1",
      created: new Date().toISOString(),
      updated: new Date().toISOString()
    },
    {
      id: "account-2",
      exchange: "binance",
      owner: "secondary_account",
      email: userEmail,
      usdt: 2000,
      usdc: 1500,
      proxy: "proxy-2",
      bullish: false,
      bearish: true,
      totalRisk: 10,
      movePercent: 3,
      max_non_essential: 15,
      profit_percent: 8,
      risk_reward: 2.0,
      exclude_coins: ["LUNA"],
      include_delisted: false,
      user: "user-1",
      created: new Date().toISOString(),
      updated: new Date().toISOString()
    }
  ];
}

/**
 * This component demonstrates:
 *
 * 1. **Server-Side Data Fetching**: Immediate data loading when rendering starts
 * 2. **Error Boundaries**: Graceful handling of fetch failures
 * 3. **Empty States**: Proper handling when no data exists
 * 4. **Rich Data Display**: Showing complex nested data structures
 * 5. **Action Integration**: Links to other parts of the app
 */
