import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { <PERSON><PERSON>ircle, XCircle, User } from "lucide-react";

interface UserProfileCardProps {
  userEmail: string;
  userName: string;
}

/**
 * Server Component that fetches user data immediately when rendering starts
 * This demonstrates "fetch as you render" - the fetch happens as soon as
 * React starts rendering this component, not after it mounts
 */
export async function UserProfileCard({ userEmail, userName }: UserProfileCardProps) {
  // This fetch starts immediately when <PERSON>act begins rendering this component
  // No useEffect, no loading state management - it just fetches
  const user = await fetchUserData(userEmail, userName);

  if (!user) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <XCircle className="h-5 w-5 text-destructive" />
            User Profile
          </CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-muted-foreground">Failed to load user profile</p>
        </CardContent>
      </Card>
    );
  }
  
  // Parse settings to check onboarding status
  let settings = {};
  try {
    settings = JSON.parse(user.settings || '{}');
  } catch (e) {
    // Invalid JSON, treat as empty settings
  }

  const hasCompletedOnboarding = !!(settings as any).password && !!(settings as any).credentials;
  const initials = user.name ? user.name.split(' ').map((n: string) => n[0]).join('').toUpperCase() : 'U';

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <User className="h-5 w-5" />
          User Profile
        </CardTitle>
        <CardDescription>Your account information</CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="flex items-center gap-3">
          <Avatar className="h-12 w-12">
            <AvatarImage src={user.avatar} alt={user.name} />
            <AvatarFallback>{initials}</AvatarFallback>
          </Avatar>
          <div>
            <h3 className="font-semibold">{user.name}</h3>
            <p className="text-sm text-muted-foreground">{user.email}</p>
          </div>
        </div>

        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <span className="text-sm">Email Verified</span>
            {user.verified ? (
              <Badge variant="default" className="bg-green-100 text-green-800">
                <CheckCircle className="h-3 w-3 mr-1" />
                Verified
              </Badge>
            ) : (
              <Badge variant="destructive">
                <XCircle className="h-3 w-3 mr-1" />
                Unverified
              </Badge>
            )}
          </div>

          <div className="flex items-center justify-between">
            <span className="text-sm">Onboarding</span>
            {hasCompletedOnboarding ? (
              <Badge variant="default" className="bg-green-100 text-green-800">
                <CheckCircle className="h-3 w-3 mr-1" />
                Complete
              </Badge>
            ) : (
              <Badge variant="secondary">
                <XCircle className="h-3 w-3 mr-1" />
                Incomplete
              </Badge>
            )}
          </div>
        </div>

        <div className="pt-2 border-t">
          <p className="text-xs text-muted-foreground">
            Member since {new Date(user.created).toLocaleDateString()}
          </p>
        </div>
      </CardContent>
    </Card>
  );
}

/**
 * Simulated user data fetching for demo purposes
 * In a real app, this would call your API or database
 */
async function fetchUserData(userEmail: string, userName: string) {
  // Simulate fast API call
  await new Promise(resolve => setTimeout(resolve, 100));

  return {
    id: "demo-user-1",
    email: userEmail,
    name: userName || "Demo User",
    avatar: "",
    verified: true,
    settings: JSON.stringify({
      password: "encrypted_password",
      credentials: "encrypted_credentials"
    }),
    created: new Date().toISOString(),
    updated: new Date().toISOString()
  };
}

/**
 * Key benefits of this Server Component approach:
 *
 * 1. **Immediate Fetching**: Data fetching starts as soon as React begins rendering
 * 2. **No Loading States**: The component either renders with data or shows an error
 * 3. **Server-Side Rendering**: The HTML includes the actual data, not loading spinners
 * 4. **Better SEO**: Search engines see the actual content
 * 5. **Faster Perceived Performance**: Users see content immediately
 * 6. **Automatic Error Handling**: Failed fetches are handled gracefully
 */
