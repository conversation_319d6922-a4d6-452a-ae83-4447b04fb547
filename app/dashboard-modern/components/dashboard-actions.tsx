"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { RefreshCw, Plus, Settings, TrendingUp } from "lucide-react";
import { useRouter } from "next/navigation";
import { toast } from "sonner";

/**
 * Client Component for interactive dashboard actions
 * This demonstrates the separation between server and client components
 */
export function DashboardActions() {
  const router = useRouter();
  const [isRefreshing, setIsRefreshing] = useState(false);

  const handleRefresh = async () => {
    setIsRefreshing(true);
    
    // Simulate refresh action
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // In a real app, you might invalidate React Query cache or trigger a router refresh
    router.refresh();
    
    setIsRefreshing(false);
    toast.success("Dashboard refreshed!");
  };

  const handleAddAccount = () => {
    router.push("/onboarding");
  };

  const handleViewSettings = () => {
    router.push("/settings");
  };

  const handleViewTradingHistory = () => {
    router.push("/trading-history");
  };

  return (
    <Card className="mt-6">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Settings className="h-5 w-5" />
          Quick Actions
        </CardTitle>
        <CardDescription>
          Manage your trading accounts and view performance
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Button 
            onClick={handleRefresh} 
            disabled={isRefreshing}
            variant="outline"
            className="h-auto p-4 flex flex-col items-center gap-2"
          >
            <RefreshCw className={`h-6 w-6 ${isRefreshing ? 'animate-spin' : ''}`} />
            <span>Refresh Data</span>
          </Button>

          <Button 
            onClick={handleAddAccount}
            variant="outline"
            className="h-auto p-4 flex flex-col items-center gap-2"
          >
            <Plus className="h-6 w-6" />
            <span>Add Account</span>
          </Button>

          <Button 
            onClick={handleViewSettings}
            variant="outline"
            className="h-auto p-4 flex flex-col items-center gap-2"
          >
            <Settings className="h-6 w-6" />
            <span>Settings</span>
          </Button>

          <Button 
            onClick={handleViewTradingHistory}
            variant="outline"
            className="h-auto p-4 flex flex-col items-center gap-2"
          >
            <TrendingUp className="h-6 w-6" />
            <span>Trading History</span>
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}

/**
 * This client component demonstrates:
 * 
 * 1. **Client-Side Interactivity**: Buttons, state management, event handlers
 * 2. **Router Integration**: Navigation to different pages
 * 3. **Toast Notifications**: User feedback for actions
 * 4. **Loading States**: Visual feedback during async operations
 * 5. **Separation of Concerns**: Interactive logic separate from data fetching
 */
