import { Card, CardContent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Activity, ArrowUpRight, ArrowDownLeft, Clock } from "lucide-react";

interface RecentActivityProps {
  userEmail: string;
}

/**
 * Server Component that fetches recent trading activity
 * This demonstrates slower-loading data that streams in
 */
export async function RecentActivity({ userEmail }: RecentActivityProps) {
  // Simulate fetching recent activity (this might be slow)
  const activities = await getRecentActivity(userEmail);

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Activity className="h-5 w-5" />
          Recent Activity
        </CardTitle>
        <CardDescription>Your latest trading actions</CardDescription>
      </CardHeader>
      <CardContent>
        {activities.length === 0 ? (
          <EmptyActivityState />
        ) : (
          <div className="space-y-3">
            {activities.map((activity) => (
              <ActivityItem key={activity.id} activity={activity} />
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  );
}

function ActivityItem({ activity }: { activity: any }) {
  const isPositive = activity.type === 'buy' || activity.type === 'profit';
  
  return (
    <div className="flex items-center gap-3 p-3 border rounded-lg">
      <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
        isPositive ? 'bg-green-100' : 'bg-red-100'
      }`}>
        {isPositive ? (
          <ArrowUpRight className="h-4 w-4 text-green-600" />
        ) : (
          <ArrowDownLeft className="h-4 w-4 text-red-600" />
        )}
      </div>
      
      <div className="flex-1">
        <div className="flex items-center justify-between">
          <p className="font-medium">{activity.description}</p>
          <Badge variant={isPositive ? "default" : "destructive"} className={
            isPositive ? "bg-green-100 text-green-800" : ""
          }>
            {activity.amount}
          </Badge>
        </div>
        <div className="flex items-center gap-2 text-sm text-muted-foreground">
          <Clock className="h-3 w-3" />
          <span>{activity.timestamp}</span>
          <span>•</span>
          <span>{activity.pair}</span>
        </div>
      </div>
    </div>
  );
}

function EmptyActivityState() {
  return (
    <div className="text-center py-6">
      <div className="w-12 h-12 bg-muted rounded-full flex items-center justify-center mx-auto mb-3">
        <Activity className="h-6 w-6 text-muted-foreground" />
      </div>
      <h3 className="font-medium mb-1">No Recent Activity</h3>
      <p className="text-sm text-muted-foreground">
        Your trading activity will appear here
      </p>
    </div>
  );
}

function ActivityError() {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-destructive">Error Loading Activity</CardTitle>
        <CardDescription>
          Unable to load recent activity. Please try again later.
        </CardDescription>
      </CardHeader>
    </Card>
  );
}

/**
 * Simulated activity data fetching
 * This includes a longer delay to demonstrate streaming
 */
async function getRecentActivity(userEmail: string) {
  // Simulate API call (reduced delay for better demo experience)
  await new Promise(resolve => setTimeout(resolve, 300));
  
  return [
    {
      id: "1",
      type: "buy",
      description: "Bought BTC",
      amount: "+0.0234 BTC",
      pair: "BTC/USDT",
      timestamp: "2 hours ago"
    },
    {
      id: "2", 
      type: "sell",
      description: "Sold ETH",
      amount: "-1.5 ETH",
      pair: "ETH/USDT",
      timestamp: "5 hours ago"
    },
    {
      id: "3",
      type: "profit",
      description: "Profit from BNB trade",
      amount: "+$45.67",
      pair: "BNB/USDT", 
      timestamp: "1 day ago"
    },
    {
      id: "4",
      type: "buy",
      description: "Bought ADA",
      amount: "+1000 ADA",
      pair: "ADA/USDT",
      timestamp: "2 days ago"
    }
  ];
}

/**
 * This component demonstrates:
 * 
 * 1. **Slow Loading Data**: Simulates a slower API call
 * 2. **Streaming**: This will load after faster components
 * 3. **Complex Data Structures**: Rich activity data with multiple fields
 * 4. **Empty States**: Handling when no activity exists
 * 5. **Error Handling**: Graceful degradation on fetch failure
 */
