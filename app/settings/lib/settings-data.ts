import { currentUser } from "@clerk/nextjs/server";
import { createUltimateService } from "@/lib/services/ultimate-service";
import { UserSettings } from "@/lib/database/types";

export interface SettingsUser {
  id: string;
  email: string;
  emailVisibility: boolean;
  verified: boolean;
  name: string;
  avatar: string;
  settings: UserSettings;
  createdAt: string;
  updatedAt: string;
}

export interface SettingsProxy {
  id: string;
  ip_address: string;
  type: "http" | "socks5";
  created: string;
  updated: string;
  user: string;
}

export interface SettingsExchangeAccount {
  id: string;
  exchange: string;
  owner: string;
  email: string;
  created: string;
  updated: string;
  proxy?: string;
}

export interface SettingsSecretKey {
  secretKey?: string;
  hasEncryptedPassword: boolean;
  hasEncryptedCredentials: boolean;
  decryptionError?: string;
}

export interface SettingsData {
  user: SettingsUser;
  proxies: SettingsProxy[];
  exchangeAccounts: SettingsExchangeAccount[];
  secretKeyData?: SettingsSecretKey;
}

/**
 * Get current user email from Clerk
 */
export async function getCurrentUserEmail(): Promise<string> {
  const user = await currentUser();
  if (!user) {
    throw new Error("Not authenticated");
  }
  return user.emailAddresses[0]?.emailAddress || "";
}

/**
 * Get secret key data with decryption
 */
async function getSecretKeyData(userData: any): Promise<SettingsSecretKey> {
  try {
    // Parse user settings
    let settings: any = {};
    try {
      settings = typeof userData.settings === 'string'
        ? JSON.parse(userData.settings || '{}')
        : userData.settings || {};
    } catch (parseError) {
      console.error("Error parsing user settings:", parseError);
      return {
        hasEncryptedPassword: false,
        hasEncryptedCredentials: false,
        decryptionError: "Invalid settings format"
      };
    }

    const hasEncryptedPassword = !!settings.password;
    const hasEncryptedCredentials = !!settings.credentials;

    // If no encrypted password exists, return empty state
    if (!hasEncryptedPassword) {
      return {
        hasEncryptedPassword: false,
        hasEncryptedCredentials,
      };
    }

    // Attempt to decrypt the user's secret key using server salt
    const serverSalt = process.env.SALT;
    if (!serverSalt) {
      console.error("Server salt not configured");
      return {
        hasEncryptedPassword,
        hasEncryptedCredentials,
        decryptionError: "Server configuration error"
      };
    }

    // Import decryption function
    const { decryptObject } = await import('@/lib/encryption');

    // Decrypt the user's secret key
    const decryptionResult = decryptObject<string>(settings.password, serverSalt);

    if (decryptionResult.success && decryptionResult.data) {
      return {
        secretKey: decryptionResult.data,
        hasEncryptedPassword,
        hasEncryptedCredentials,
      };
    } else {
      console.error("Failed to decrypt secret key:", decryptionResult.error);
      return {
        hasEncryptedPassword,
        hasEncryptedCredentials,
        decryptionError: decryptionResult.error || "Failed to decrypt secret key"
      };
    }
  } catch (error) {
    console.error("Error in getSecretKeyData:", error);
    return {
      hasEncryptedPassword: false,
      hasEncryptedCredentials: false,
      decryptionError: error instanceof Error ? error.message : "Unknown error"
    };
  }
}

/**
 * Get all settings data for the current user
 */
export async function getSettingsData(): Promise<SettingsData> {
  const userEmail = await getCurrentUserEmail();
  const service = createUltimateService(userEmail);

  try {
    // First get user data
    const userResult = await service.getUserByEmail(userEmail);

    // Handle user creation if doesn't exist
    let userData = userResult.data;
    if (!userResult.success || !userData) {
      const clerkUser = await currentUser();
      const createResult = await service.createUser({
        email: userEmail,
        name: `${clerkUser?.firstName || ""} ${clerkUser?.lastName || ""}`.trim() || userEmail.split('@')[0] || "User",
      });

      if (!createResult.success || !createResult.data) {
        throw new Error("Failed to create user");
      }
      userData = createResult.data;
    }

    // Now fetch proxies and exchange accounts using the user ID
    const [proxiesResult, exchangeAccountsResult] = await Promise.all([
      service.getProxiesByUser(userData.id),
      service.getExchangeAccountsByUser(userData.id)
    ]);

    // Parse user settings
    let settings: UserSettings = {};
    try {
      settings = typeof userData.settings === 'string'
        ? JSON.parse(userData.settings || '{}')
        : userData.settings || {};
    } catch (error) {
      console.log("Error parsing settings:", error);
      settings = {};
    }

    // Transform data to our interfaces
    const user: SettingsUser = {
      id: userData.id,
      email: userData.email,
      emailVisibility: userData.emailVisibility,
      verified: userData.verified,
      name: userData.name,
      avatar: userData.avatar || "",
      settings,
      createdAt: userData.created,
      updatedAt: userData.updated,
    };

    const proxies: SettingsProxy[] = (proxiesResult.data || []).map((proxy: any) => ({
      id: proxy.id,
      ip_address: proxy.ip_address,
      type: proxy.type,
      created: proxy.created,
      updated: proxy.updated,
      user: proxy.user,
    }));

    const exchangeAccounts: SettingsExchangeAccount[] = (exchangeAccountsResult.data || []).map((account: any) => ({
      id: account.id,
      exchange: account.exchange,
      owner: account.owner,
      email: account.email,
      created: account.created,
      updated: account.updated,
      proxy: account.proxy,
    }));

    // Get secret key data
    const secretKeyData = await getSecretKeyData(userData);

    return {
      user,
      proxies,
      exchangeAccounts,
      secretKeyData,
    };
  } catch (error) {
    console.error("Error fetching settings data:", error);
    throw new Error("Failed to fetch settings data");
  }
}
