'use client'

import { useState, useTransition } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { 
  LinkIcon, 
  Edit, 
  Trash2, 
  Plus, 
  Eye, 
  EyeOff, 
  TestTube,
  CheckCircle,
  AlertCircle,
  Loader2
} from "lucide-react";
import { toast } from "sonner";
import { SettingsExchangeAccount, SettingsProxy } from "../lib/settings-data";
import { 
  connectExchangeSettingsAction, 
  deleteExchangeSettingsAction,
  testExchangeConnectionSettingsAction
} from "../actions";

interface ExchangeSettingsSectionProps {
  exchangeAccounts: SettingsExchangeAccount[];
  proxies: SettingsProxy[];
}

interface TestResult {
  success: boolean;
  symbolCount?: number;
  timestamp: Date;
}

export function ExchangeSettingsSection({ exchangeAccounts, proxies }: ExchangeSettingsSectionProps) {
  const [isPending, startTransition] = useTransition();
  const [isTestingConnection, setIsTestingConnection] = useState(false);
  const [lastTestResult, setLastTestResult] = useState<TestResult | null>(null);
  const [showNewAccountForm, setShowNewAccountForm] = useState(false);
  const [editingAccountId, setEditingAccountId] = useState<string | null>(null);

  // Form state for new/edit connection
  const [selectedExchange, setSelectedExchange] = useState<'binance' | 'bybit'>('binance');
  const [accountName, setAccountName] = useState("");
  const [apiKey, setApiKey] = useState("");
  const [secretKey, setSecretKey] = useState("");
  const [exchangeEmail, setExchangeEmail] = useState("");
  const [selectedProxyId, setSelectedProxyId] = useState<string>("");

  // Password visibility states
  const [showApiKey, setShowApiKey] = useState(false);
  const [showSecretKey, setShowSecretKey] = useState(false);

  const handleConnectExchange = () => {
    if (!apiKey || !secretKey || !exchangeEmail || !accountName) {
      toast.error("All fields are required");
      return;
    }

    startTransition(async () => {
      const result = await connectExchangeSettingsAction({
        apiKey,
        secretKey,
        exchangeEmail,
        exchange: selectedExchange,
        name: accountName,
        proxyId: selectedProxyId && selectedProxyId !== "none" ? selectedProxyId : undefined,
      });

      if (result.success) {
        toast.success(result.message);
        // Clear form
        setApiKey("");
        setSecretKey("");
        setExchangeEmail("");
        setAccountName("");
        setSelectedProxyId("");
        setShowNewAccountForm(false);
        setEditingAccountId(null);
      } else {
        toast.error(result.error || `Failed to connect ${selectedExchange} account`);
      }
    });
  };

  const handleDeleteAccount = (accountId: string) => {
    if (!confirm("Are you sure you want to delete this exchange account?")) {
      return;
    }

    startTransition(async () => {
      const result = await deleteExchangeSettingsAction(accountId);

      if (result.success) {
        toast.success(result.message);
      } else {
        toast.error(result.error || "Failed to delete exchange account");
      }
    });
  };

  const handleTestConnection = async (accountId: string) => {
    setIsTestingConnection(true);

    try {
      const result = await testExchangeConnectionSettingsAction({
        accountId,
        proxyId: selectedProxyId || undefined,
      });

      if (result.success) {
        setLastTestResult({
          success: true,
          symbolCount: result.data?.symbolCount,
          timestamp: new Date(),
        });
        toast.success("Connection test successful!");
      } else {
        setLastTestResult({
          success: false,
          timestamp: new Date(),
        });
        toast.error(result.error || "Connection test failed");
      }
    } catch (error) {
      setLastTestResult({
        success: false,
        timestamp: new Date(),
      });
      toast.error("Connection test failed");
    } finally {
      setIsTestingConnection(false);
    }
  };

  const resetForm = () => {
    setApiKey("");
    setSecretKey("");
    setExchangeEmail("");
    setAccountName("");
    setSelectedProxyId("");
    setSelectedExchange('binance');
    setShowNewAccountForm(false);
    setEditingAccountId(null);
  };

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between">
        <div>
          <CardTitle className="flex items-center gap-2">
            <LinkIcon className="h-5 w-5" />
            Exchange Accounts
          </CardTitle>
          <CardDescription>
            Manage your connected exchange accounts for trading.
          </CardDescription>
        </div>
        <Button 
          size="sm" 
          onClick={() => setShowNewAccountForm(true)}
          disabled={showNewAccountForm || editingAccountId !== null}
        >
          <Plus className="h-4 w-4 mr-2" />
          Connect Exchange
        </Button>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Existing Accounts */}
        {exchangeAccounts.length === 0 && !showNewAccountForm ? (
          <div className="text-center py-8">
            <LinkIcon className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
            <h3 className="text-lg font-medium mb-2">No exchanges connected</h3>
            <p className="text-muted-foreground mb-4">
              Connect your exchange accounts to start trading.
            </p>
            <Button onClick={() => setShowNewAccountForm(true)}>
              Connect Your First Exchange
            </Button>
          </div>
        ) : (
          <div className="space-y-4">
            {exchangeAccounts.map((account) => (
              <div key={account.id} className="flex items-center justify-between p-4 border rounded-lg">
                <div className="flex-1">
                  <div className="flex items-center gap-2">
                    <Badge variant="outline">{account.exchange.toUpperCase()}</Badge>
                    <span className="font-medium">{account.owner}</span>
                  </div>
                  <p className="text-sm text-muted-foreground mt-1">
                    {account.email}
                  </p>
                  <p className="text-xs text-muted-foreground">
                    Created {new Date(account.created).toLocaleDateString()}
                  </p>
                </div>
                <div className="flex items-center gap-2">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handleTestConnection(account.id)}
                    disabled={isTestingConnection}
                  >
                    {isTestingConnection ? (
                      <Loader2 className="h-4 w-4 animate-spin" />
                    ) : (
                      <TestTube className="h-4 w-4" />
                    )}
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => {
                      setEditingAccountId(account.id);
                      setAccountName(account.owner);
                      setExchangeEmail(account.email);
                      setSelectedExchange(account.exchange as 'binance' | 'bybit');
                    }}
                    disabled={editingAccountId !== null || showNewAccountForm}
                  >
                    <Edit className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handleDeleteAccount(account.id)}
                    disabled={isPending}
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            ))}
          </div>
        )}

        {/* Test Result Display */}
        {lastTestResult && (
          <div className={`p-3 rounded-lg border ${
            lastTestResult.success 
              ? 'bg-green-50 border-green-200' 
              : 'bg-red-50 border-red-200'
          }`}>
            <div className="flex items-center gap-2">
              {lastTestResult.success ? (
                <CheckCircle className="h-4 w-4 text-green-600" />
              ) : (
                <AlertCircle className="h-4 w-4 text-red-600" />
              )}
              <span className={`text-sm font-medium ${
                lastTestResult.success ? 'text-green-800' : 'text-red-800'
              }`}>
                {lastTestResult.success ? 'Connection Successful' : 'Connection Failed'}
              </span>
            </div>
            {lastTestResult.success && lastTestResult.symbolCount && (
              <p className="text-xs text-green-700 mt-1">
                Found {lastTestResult.symbolCount} trading symbols
              </p>
            )}
            <p className="text-xs text-muted-foreground mt-1">
              Tested at {lastTestResult.timestamp.toLocaleTimeString()}
            </p>
          </div>
        )}

        {/* New Account Form */}
        {(showNewAccountForm || editingAccountId) && (
          <Card className="border-[#245c1a]">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <LinkIcon className="h-5 w-5" />
                {editingAccountId ? 'Edit Exchange Account' : 'Connect New Exchange Account'}
              </CardTitle>
              <CardDescription>
                {editingAccountId 
                  ? 'Update your exchange account credentials'
                  : 'Choose an exchange and enter your API credentials to enable trading'
                }
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <Tabs value={selectedExchange} onValueChange={(value) => setSelectedExchange(value as 'binance' | 'bybit')}>
                <TabsList className="grid w-full grid-cols-2">
                  <TabsTrigger value="binance">Binance</TabsTrigger>
                  <TabsTrigger value="bybit">Bybit</TabsTrigger>
                </TabsList>
              </Tabs>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="account-name">Account Name</Label>
                  <Input
                    id="account-name"
                    value={accountName}
                    onChange={(e) => setAccountName(e.target.value)}
                    placeholder="My Trading Account"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="exchange-email">Exchange Email</Label>
                  <Input
                    id="exchange-email"
                    type="email"
                    value={exchangeEmail}
                    onChange={(e) => setExchangeEmail(e.target.value)}
                    placeholder="<EMAIL>"
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="api-key">API Key</Label>
                <div className="relative">
                  <Input
                    id="api-key"
                    type={showApiKey ? "text" : "password"}
                    value={apiKey}
                    onChange={(e) => setApiKey(e.target.value)}
                    placeholder="Enter your API key"
                    className="pr-10"
                  />
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                    onClick={() => setShowApiKey(!showApiKey)}
                  >
                    {showApiKey ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                  </Button>
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="secret-key">Secret Key</Label>
                <div className="relative">
                  <Input
                    id="secret-key"
                    type={showSecretKey ? "text" : "password"}
                    value={secretKey}
                    onChange={(e) => setSecretKey(e.target.value)}
                    placeholder="Enter your secret key"
                    className="pr-10"
                  />
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                    onClick={() => setShowSecretKey(!showSecretKey)}
                  >
                    {showSecretKey ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                  </Button>
                </div>
              </div>

              {proxies.length > 0 && (
                <div className="space-y-2">
                  <Label htmlFor="proxy-select">Proxy (Optional)</Label>
                  <Select value={selectedProxyId} onValueChange={setSelectedProxyId}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select a proxy" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="none">No proxy</SelectItem>
                      {proxies.map((proxy) => (
                        <SelectItem key={proxy.id} value={proxy.id}>
                          {proxy.type.toUpperCase()} - {proxy.ip_address}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              )}

              <div className="flex gap-2">
                <Button
                  variant="outline"
                  onClick={resetForm}
                  className="flex-1"
                >
                  Cancel
                </Button>
                <Button
                  onClick={handleConnectExchange}
                  className="flex-1 bg-[#245c1a] hover:bg-[#1a4513]"
                  disabled={!apiKey || !secretKey || !exchangeEmail || !accountName || isPending}
                >
                  {isPending
                    ? "Saving..."
                    : editingAccountId
                    ? "Update Account"
                    : `Connect ${selectedExchange.charAt(0).toUpperCase() + selectedExchange.slice(1)} Account`
                  }
                </Button>
              </div>
            </CardContent>
          </Card>
        )}
      </CardContent>
    </Card>
  );
}
