'use client'

import { useState, useTransition } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { 
  Key, 
  Eye, 
  EyeOff, 
  Edit, 
  Shield, 
  CheckCircle, 
  AlertCircle, 
  Loader2,
  Lock
} from "lucide-react";
import { toast } from "sonner";
import { validatePasswordStrength } from "@/lib/encryption";
import { SettingsSecretKey } from "../lib/settings-data";
import { updateSecretKeySettingsAction } from "../actions";

interface SecretKeySettingsSectionProps {
  secretKeyData?: SettingsSecretKey;
}

export function SecretKeySettingsSection({ secretKeyData }: SecretKeySettingsSectionProps) {
  const [isPending, startTransition] = useTransition();
  const [showCurrentKey, setShowCurrentKey] = useState(false);
  const [showNewKey, setShowNewKey] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const [newSecretKey, setNewSecretKey] = useState("");

  // Validate new secret key
  const validation = newSecretKey ? validatePasswordStrength(newSecretKey) : null;

  const handleUpdateSecretKey = () => {
    // Final validation before submission
    const finalValidation = validatePasswordStrength(newSecretKey);
    if (!finalValidation.success) {
      toast.error(finalValidation.message);
      return;
    }

    startTransition(async () => {
      const result = await updateSecretKeySettingsAction({
        secretKey: newSecretKey,
      });

      if (result.success) {
        toast.success(result.message);
        setNewSecretKey("");
        setIsEditing(false);
      } else {
        toast.error(result.error || "Failed to update secret key");
      }
    });
  };

  const handleCancelEdit = () => {
    setNewSecretKey("");
    setIsEditing(false);
    setShowNewKey(false);
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Key className="h-5 w-5" />
          Security Settings
        </CardTitle>
        <CardDescription>
          Manage your encryption keys and security preferences.
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {secretKeyData ? (
          <div className="space-y-4">
            {/* Current Secret Key Status */}
            <div className="flex items-center justify-between">
              <div>
                <h3 className="font-medium">Secret Key</h3>
                <p className="text-sm text-muted-foreground">
                  Your encryption key for securing credentials
                </p>
              </div>
              <Badge variant={secretKeyData.hasEncryptedPassword ? "default" : "secondary"}>
                {secretKeyData.hasEncryptedPassword ? "Configured" : "Not Set"}
              </Badge>
            </div>

            {/* Current Secret Key Display */}
            {secretKeyData.secretKey && !isEditing && (
              <div className="space-y-2">
                <Label>Current Secret Key</Label>
                <div className="flex items-center gap-2">
                  <code className="flex-1 p-2 bg-muted rounded text-sm font-mono">
                    {showCurrentKey ? secretKeyData.secretKey : "••••••••••••••••"}
                  </code>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setShowCurrentKey(!showCurrentKey)}
                  >
                    {showCurrentKey ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                  </Button>
                </div>
              </div>
            )}

            {/* Decryption Error */}
            {secretKeyData.decryptionError && (
              <div className="p-4 bg-destructive/10 border border-destructive/20 rounded-lg">
                <div className="flex items-center gap-2">
                  <Shield className="h-4 w-4 text-destructive" />
                  <span className="font-medium text-destructive">Decryption Error</span>
                </div>
                <p className="text-sm text-destructive mt-1">
                  {secretKeyData.decryptionError}
                </p>
              </div>
            )}

            {/* Security Status Grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="flex items-center justify-between p-3 border rounded">
                <span className="text-sm">Encrypted Password</span>
                <Badge variant={secretKeyData.hasEncryptedPassword ? "default" : "secondary"}>
                  {secretKeyData.hasEncryptedPassword ? "Yes" : "No"}
                </Badge>
              </div>
              <div className="flex items-center justify-between p-3 border rounded">
                <span className="text-sm">Encrypted Credentials</span>
                <Badge variant={secretKeyData.hasEncryptedCredentials ? "default" : "secondary"}>
                  {secretKeyData.hasEncryptedCredentials ? "Yes" : "No"}
                </Badge>
              </div>
            </div>

            {/* Edit Secret Key Form */}
            {isEditing ? (
              <Card className="border-[#245c1a]">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Lock className="h-5 w-5" />
                    {secretKeyData.hasEncryptedPassword ? 'Update Secret Key' : 'Create Secret Key'}
                  </CardTitle>
                  <CardDescription>
                    {secretKeyData.hasEncryptedPassword 
                      ? 'Enter a new secret key to update your encryption. This will re-encrypt all your credentials.'
                      : 'Create a secure secret key to encrypt your trading credentials.'
                    }
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="new-secret-key">
                      {secretKeyData.hasEncryptedPassword ? 'New Secret Key' : 'Secret Key'}
                    </Label>
                    <div className="relative">
                      <Input
                        id="new-secret-key"
                        type={showNewKey ? "text" : "password"}
                        value={newSecretKey}
                        onChange={(e) => setNewSecretKey(e.target.value)}
                        placeholder="Enter a secure secret key (min 8 characters)"
                        minLength={8}
                        className={`pr-20 ${
                          validation
                            ? validation.success
                              ? "border-green-500 focus:border-green-500"
                              : "border-red-500 focus:border-red-500"
                            : ""
                        }`}
                      />
                      <div className="absolute right-0 top-0 h-full flex items-center">
                        <Button
                          type="button"
                          variant="ghost"
                          size="sm"
                          className="px-3 py-2 hover:bg-transparent"
                          onClick={() => setShowNewKey(!showNewKey)}
                        >
                          {showNewKey ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                        </Button>
                      </div>
                    </div>
                  </div>

                  {/* Password Strength Indicator */}
                  {validation && (
                    <div className={`flex items-center gap-2 text-sm ${
                      validation.success ? "text-green-600" : "text-red-600"
                    }`}>
                      {validation.success ? (
                        <CheckCircle className="h-4 w-4" />
                      ) : (
                        <AlertCircle className="h-4 w-4" />
                      )}
                      <span>{validation.message}</span>
                    </div>
                  )}

                  {/* Security Requirements */}
                  <div className="p-3 bg-blue-50 border border-blue-200 rounded-lg">
                    <h4 className="text-sm font-medium text-blue-800 mb-2">Security Requirements:</h4>
                    <ul className="text-xs text-blue-700 space-y-1">
                      <li>• At least 8 characters long</li>
                      <li>• Contains uppercase and lowercase letters</li>
                      <li>• Contains at least one number</li>
                      <li>• Contains at least one special character</li>
                    </ul>
                  </div>

                  <div className="flex gap-2">
                    <Button
                      variant="outline"
                      onClick={handleCancelEdit}
                      className="flex-1"
                    >
                      Cancel
                    </Button>
                    <Button
                      onClick={handleUpdateSecretKey}
                      className="flex-1 bg-[#245c1a] hover:bg-[#1a4513] disabled:opacity-50"
                      disabled={isPending || !validation?.success}
                    >
                      {isPending ? (
                        <>
                          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                          {secretKeyData.hasEncryptedPassword ? 'Updating...' : 'Creating...'}
                        </>
                      ) : (
                        secretKeyData.hasEncryptedPassword ? 'Update Secret Key' : 'Create Secret Key'
                      )}
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ) : (
              /* Action Buttons */
              <div className="flex gap-2">
                <Button
                  variant="outline"
                  onClick={() => setIsEditing(true)}
                  className="flex-1"
                >
                  <Edit className="h-4 w-4 mr-2" />
                  {secretKeyData.hasEncryptedPassword ? 'Change Secret Key' : 'Create Secret Key'}
                </Button>
              </div>
            )}
          </div>
        ) : (
          /* No Secret Key Data */
          <div className="text-center py-8">
            <Key className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
            <h3 className="text-lg font-medium mb-2">No security settings found</h3>
            <p className="text-muted-foreground mb-4">
              Create a secret key to secure your trading credentials.
            </p>
            <Button onClick={() => setIsEditing(true)}>
              Create Secret Key
            </Button>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
