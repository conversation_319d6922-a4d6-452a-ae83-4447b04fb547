'use client'

import { useState, useTransition } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { 
  Network, 
  Edit, 
  Trash2, 
  Plus, 
  Globe,
  Eye,
  EyeOff
} from "lucide-react";
import { toast } from "sonner";
import { SettingsProxy } from "../lib/settings-data";
import { createProxySettingsAction, deleteProxySettingsAction } from "../actions";

interface ProxySettingsSectionProps {
  proxies: SettingsProxy[];
}

export function ProxySettingsSection({ proxies }: ProxySettingsSectionProps) {
  const [isPending, startTransition] = useTransition();
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [editingProxyId, setEditingProxyId] = useState<string | null>(null);

  // Create proxy form state
  const [proxyUsername, setProxyUsername] = useState("");
  const [proxyPassword, setProxyPassword] = useState("");
  const [proxyHost, setProxyHost] = useState("");
  const [proxyPort, setProxyPort] = useState("");
  const [proxyType, setProxyType] = useState<"http" | "socks5">("http");
  const [showPassword, setShowPassword] = useState(false);

  const handleCreateProxy = () => {
    if (!proxyHost || !proxyPort) {
      toast.error("Host and port are required");
      return;
    }

    startTransition(async () => {
      const result = await createProxySettingsAction({
        username: proxyUsername || undefined,
        password: proxyPassword || undefined,
        host: proxyHost,
        port: parseInt(proxyPort),
        type: proxyType,
      });

      if (result.success) {
        toast.success(result.message);
        // Clear form
        setProxyUsername("");
        setProxyPassword("");
        setProxyHost("");
        setProxyPort("");
        setProxyType("http");
        setShowCreateForm(false);
        setEditingProxyId(null);
      } else {
        toast.error(result.error || "Failed to create proxy");
      }
    });
  };

  const handleDeleteProxy = (proxyId: string) => {
    if (!confirm("Are you sure you want to delete this proxy?")) {
      return;
    }

    startTransition(async () => {
      const result = await deleteProxySettingsAction(proxyId);

      if (result.success) {
        toast.success(result.message);
      } else {
        toast.error(result.error || "Failed to delete proxy");
      }
    });
  };

  const resetForm = () => {
    setProxyUsername("");
    setProxyPassword("");
    setProxyHost("");
    setProxyPort("");
    setProxyType("http");
    setShowCreateForm(false);
    setEditingProxyId(null);
  };

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between">
        <div>
          <CardTitle className="flex items-center gap-2">
            <Network className="h-5 w-5" />
            Proxy Connections
          </CardTitle>
          <CardDescription>
            Manage your proxy configurations for secure trading.
          </CardDescription>
        </div>
        <Button 
          size="sm" 
          onClick={() => setShowCreateForm(true)}
          disabled={showCreateForm || editingProxyId !== null}
        >
          <Plus className="h-4 w-4 mr-2" />
          Add Proxy
        </Button>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Existing Proxies */}
        {proxies.length === 0 && !showCreateForm ? (
          <div className="text-center py-8">
            <Network className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
            <h3 className="text-lg font-medium mb-2">No proxies configured</h3>
            <p className="text-muted-foreground mb-4">
              Add a proxy to secure your trading connections.
            </p>
            <Button onClick={() => setShowCreateForm(true)}>
              Add Your First Proxy
            </Button>
          </div>
        ) : (
          <div className="space-y-4">
            {proxies.map((proxy) => (
              <div key={proxy.id} className="flex items-center justify-between p-4 border rounded-lg">
                <div className="flex-1">
                  <div className="flex items-center gap-2">
                    <Badge variant="outline">{proxy.type.toUpperCase()}</Badge>
                    <span className="font-medium font-mono text-sm">{proxy.ip_address}</span>
                  </div>
                  <p className="text-sm text-muted-foreground mt-1">
                    Created {new Date(proxy.created).toLocaleDateString()}
                  </p>
                </div>
                <div className="flex items-center gap-2">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => {
                      setEditingProxyId(proxy.id);
                      // Parse proxy string to populate form
                      const parts = proxy.ip_address.split('@');
                      if (parts.length === 2) {
                        const [credentials, hostPort] = parts;
                        const [username, password] = credentials.split(':');
                        const [host, port] = hostPort.split(':');
                        setProxyUsername(username || "");
                        setProxyPassword(password || "");
                        setProxyHost(host || "");
                        setProxyPort(port || "");
                      } else {
                        const [host, port] = proxy.ip_address.split(':');
                        setProxyHost(host || "");
                        setProxyPort(port || "");
                      }
                      setProxyType(proxy.type);
                    }}
                    disabled={editingProxyId !== null || showCreateForm}
                  >
                    <Edit className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handleDeleteProxy(proxy.id)}
                    disabled={isPending}
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            ))}
          </div>
        )}

        {/* Create/Edit Proxy Form */}
        {(showCreateForm || editingProxyId) && (
          <Card className="border-[#245c1a]">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Network className="h-5 w-5" />
                {editingProxyId ? 'Edit Proxy' : 'Add New Proxy'}
              </CardTitle>
              <CardDescription>
                {editingProxyId 
                  ? 'Update your proxy configuration'
                  : 'Configure a new proxy for secure trading connections'
                }
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="proxy-type">Proxy Type</Label>
                <Select value={proxyType} onValueChange={(value) => setProxyType(value as "http" | "socks5")}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="http">HTTP</SelectItem>
                    <SelectItem value="socks5">SOCKS5</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="proxy-host">Host/IP Address</Label>
                  <Input
                    id="proxy-host"
                    value={proxyHost}
                    onChange={(e) => setProxyHost(e.target.value)}
                    placeholder="*********** or proxy.example.com"
                    required
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="proxy-port">Port</Label>
                  <Input
                    id="proxy-port"
                    type="number"
                    value={proxyPort}
                    onChange={(e) => setProxyPort(e.target.value)}
                    placeholder="8080"
                    min="1"
                    max="65535"
                    required
                  />
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="proxy-username">Username (Optional)</Label>
                  <Input
                    id="proxy-username"
                    value={proxyUsername}
                    onChange={(e) => setProxyUsername(e.target.value)}
                    placeholder="proxy_user"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="proxy-password">Password (Optional)</Label>
                  <div className="relative">
                    <Input
                      id="proxy-password"
                      type={showPassword ? "text" : "password"}
                      value={proxyPassword}
                      onChange={(e) => setProxyPassword(e.target.value)}
                      placeholder="proxy_password"
                      className="pr-10"
                    />
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                      onClick={() => setShowPassword(!showPassword)}
                    >
                      {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                    </Button>
                  </div>
                </div>
              </div>

              {/* Proxy Preview */}
              {(proxyHost || proxyPort) && (
                <div className="text-sm text-gray-500 bg-gray-50 p-3 rounded-lg">
                  <div className="flex items-center gap-2 mb-1">
                    <Globe className="h-4 w-4" />
                    <span className="font-medium">Preview:</span>
                  </div>
                  <p className="font-mono text-xs">
                    {proxyType}:{proxyUsername && proxyPassword ? `${proxyUsername}:${proxyPassword}@` : ""}{proxyHost || "host"}:{proxyPort || "port"}
                  </p>
                </div>
              )}

              <div className="flex gap-2">
                <Button
                  variant="outline"
                  onClick={resetForm}
                  className="flex-1"
                >
                  Cancel
                </Button>
                <Button
                  onClick={handleCreateProxy}
                  className="flex-1 bg-[#245c1a] hover:bg-[#1a4513]"
                  disabled={!proxyHost || !proxyPort || isPending}
                >
                  {isPending 
                    ? "Saving..." 
                    : editingProxyId 
                    ? "Update Proxy" 
                    : "Create Proxy"
                  }
                </Button>
              </div>
            </CardContent>
          </Card>
        )}
      </CardContent>
    </Card>
  );
}
