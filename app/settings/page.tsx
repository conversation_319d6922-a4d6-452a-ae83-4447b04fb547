import { redirect } from "next/navigation";
import { currentUser } from "@clerk/nextjs/server";
import { getSettingsData } from "./lib/settings-data";
import { SettingsClient } from "./settings-client";

export default async function SettingsPage() {
  // Check authentication
  const user = await currentUser();
  if (!user) {
    redirect('/sign-in');
  }

  // Get all settings data
  const settingsData = await getSettingsData();

  return <SettingsClient data={settingsData} />;
}
