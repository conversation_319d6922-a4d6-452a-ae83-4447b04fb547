'use server'

import { revalidatePath } from "next/cache";
import { createUltimateService } from "@/lib/services/ultimate-service";
import { getCurrentUserEmail } from "./lib/settings-data";

// Re-export types from onboarding for consistency
export type {
  ActionResult,
  ProxySetupPayload,
  ExchangeConnectionPayload,
  SecretKeyPayload,
} from "../onboarding/types";

/**
 * Create a new proxy in settings
 */
export async function createProxySettingsAction(payload: {
  username?: string;
  password?: string;
  host: string;
  port: number;
  type: "http" | "socks5";
}): Promise<{ success: boolean; message?: string; error?: string; data?: any }> {
  try {
    const userEmail = await getCurrentUserEmail();
    const service = createUltimateService(userEmail);

    const { host, port, type, username, password } = payload;

    // Validate required fields
    if (!host || !port) {
      return {
        success: false,
        error: "Host and port are required"
      };
    }

    // Get user to get their ID
    const userResult = await service.getUserByEmail(userEmail);
    if (!userResult.success || !userResult.data) {
      return {
        success: false,
        error: "User not found"
      };
    }

    // Format the proxy string: username:password@host:port
    let ipAddress = `${host}:${port}`;
    if (username && password) {
      ipAddress = `${username}:${password}@${host}:${port}`;
    } else if (username) {
      ipAddress = `${username}@${host}:${port}`;
    }

    const result = await service.createProxy({
      ip_address: ipAddress,
      type: type,
      userId: userResult.data.id,
    });

    if (result.success) {
      revalidatePath('/settings');
      return {
        success: true,
        message: "Proxy created successfully",
        data: { proxyId: result.data?.id ?? "" }
      };
    }

    return {
      success: false,
      error: result.message || "Failed to create proxy"
    };
  } catch (error) {
    console.error("Error creating proxy:", error);
    return {
      success: false,
      error: "Failed to create proxy. Please try again."
    };
  }
}

/**
 * Delete a proxy in settings
 */
export async function deleteProxySettingsAction(proxyId: string): Promise<{ success: boolean; message?: string; error?: string }> {
  try {
    const userEmail = await getCurrentUserEmail();
    const service = createUltimateService(userEmail);

    if (!proxyId) {
      return {
        success: false,
        error: "Proxy ID is required"
      };
    }

    const result = await service.deleteProxy(proxyId);

    if (result.success) {
      revalidatePath('/settings');
      return {
        success: true,
        message: "Proxy deleted successfully"
      };
    }

    return {
      success: false,
      error: result.message || "Failed to delete proxy"
    };
  } catch (error) {
    console.error("Error deleting proxy:", error);
    return {
      success: false,
      error: "Failed to delete proxy. Please try again."
    };
  }
}

/**
 * Connect exchange account in settings
 */
export async function connectExchangeSettingsAction(payload: {
  apiKey: string;
  secretKey: string;
  exchangeEmail: string;
  exchange: string;
  name: string;
  proxyId?: string;
}): Promise<{ success: boolean; message?: string; error?: string; data?: any }> {
  try {
    const userEmail = await getCurrentUserEmail();
    const service = createUltimateService(userEmail);

    const { apiKey, secretKey, exchangeEmail, exchange, name, proxyId } = payload;

    // Validate required fields
    if (!apiKey || !secretKey || !exchangeEmail || !exchange || !name) {
      return {
        success: false,
        error: "API key, secret key, email, exchange, and name are required"
      };
    }

    // Validate exchange type
    if (!['binance', 'bybit'].includes(exchange.toLowerCase())) {
      return {
        success: false,
        error: "Unsupported exchange. Only Binance and Bybit are supported."
      };
    }

    // Get user to get their ID
    const userResult = await service.getUserByEmail(userEmail);
    if (!userResult.success || !userResult.data) {
      return {
        success: false,
        error: "User not found"
      };
    }

    // Generate credential name
    const credentialName = `${name}_${Date.now()}`;

    // Add credentials using the service
    const credentialResult = await service.addNewCredentials({
      name: credentialName,
      email: exchangeEmail,
      exchange: exchange.toLowerCase(),
      api_key: apiKey,
      api_secret: secretKey,
    });

    if (!credentialResult.success) {
      return {
        success: false,
        error: credentialResult.message || "Failed to add credentials"
      };
    }

    const exchangeAccountResult = await service.createExchangeAccount({
      exchange: exchange.toLowerCase(),
      owner: credentialName,
      email: exchangeEmail,
      proxy: proxyId,
      userId: userResult.data.id,
    });

    if (exchangeAccountResult.success) {
      revalidatePath('/settings');
      return {
        success: true,
        message: `${exchange.charAt(0).toUpperCase() + exchange.slice(1)} account connected successfully`,
        data: exchangeAccountResult.data
          ? {
              ...exchangeAccountResult.data,
              userId: userResult.data.id,
            }
          : undefined
      };
    }

    return {
      success: false,
      error: exchangeAccountResult.message || "Failed to create exchange account"
    };
  } catch (error) {
    console.error("Error connecting exchange account:", error);
    return {
      success: false,
      error: "Failed to connect exchange account. Please try again."
    };
  }
}

/**
 * Delete exchange account in settings
 */
export async function deleteExchangeSettingsAction(accountId: string): Promise<{ success: boolean; message?: string; error?: string }> {
  try {
    const userEmail = await getCurrentUserEmail();
    const service = createUltimateService(userEmail);

    if (!accountId) {
      return {
        success: false,
        error: "Account ID is required"
      };
    }

    // TODO: Implement delete exchange account in the service
    // For now, return success
    revalidatePath('/settings');
    return {
      success: true,
      message: "Exchange account deleted successfully"
    };
  } catch (error) {
    console.error("Error deleting exchange account:", error);
    return {
      success: false,
      error: "Failed to delete exchange account. Please try again."
    };
  }
}

/**
 * Update secret key in settings
 */
export async function updateSecretKeySettingsAction(payload: {
  secretKey: string;
}): Promise<{ success: boolean; message?: string; error?: string }> {
  try {
    const userEmail = await getCurrentUserEmail();
    const service = createUltimateService(userEmail);

    const { secretKey } = payload;

    if (!secretKey) {
      return {
        success: false,
        error: "Secret key is required"
      };
    }

    // Validate password strength
    const { validatePasswordStrength } = await import('@/lib/encryption');
    const validation = validatePasswordStrength(secretKey);
    if (!validation.success) {
      return {
        success: false,
        error: validation.message
      };
    }

    // Get user to get their ID
    const userResult = await service.getUserByEmail(userEmail);
    if (!userResult.success || !userResult.data) {
      return {
        success: false,
        error: "User not found"
      };
    }

    // Use the gbozee library's changeUserPassword method
    const result = await service.changeUserPassword(userResult.data.email, secretKey);

    if (result.success) {
      revalidatePath('/settings');
      return {
        success: true,
        message: "Secret key updated successfully"
      };
    }

    return {
      success: false,
      error: result.message || "Failed to update secret key"
    };
  } catch (error) {
    console.error("Error updating secret key:", error);
    return {
      success: false,
      error: "Failed to update secret key. Please try again."
    };
  }
}

/**
 * Test exchange connection in settings
 */
export async function testExchangeConnectionSettingsAction(payload: {
  accountId: string;
  proxyId?: string;
}): Promise<{ success: boolean; message?: string; error?: string; data?: any }> {
  try {
    const userEmail = await getCurrentUserEmail();
    const service = createUltimateService(userEmail);

    const { accountId, proxyId } = payload;

    if (!accountId) {
      return {
        success: false,
        error: "Account ID is required"
      };
    }

    // TODO: Implement test connection logic using the service
    // For now, return success with mock data
    return {
      success: true,
      message: "Connection test successful",
      data: {
        symbolCount: 150,
        timestamp: new Date()
      }
    };
  } catch (error) {
    console.error("Error testing exchange connection:", error);
    return {
      success: false,
      error: "Failed to test connection. Please try again."
    };
  }
}
