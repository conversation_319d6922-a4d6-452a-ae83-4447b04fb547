'use client'

import { useState } from "react";
import { useRouter } from "next/navigation";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { MobileNav } from "@/components/mobile-nav";
import { BottomNav } from "@/components/bottom-nav";
import { Container } from "@/components/container";
import {
  User,
  Network,
  LinkIcon,
  Key,
  Shield,
  Eye,
  EyeOff,
  Edit,
  Plus,
  Trash2,
  ChevronLeft
} from "lucide-react";
import { SettingsData } from "./lib/settings-data";
import { ExchangeSettingsSection } from "./components/exchange-settings-section";
import { SecretKeySettingsSection } from "./components/secret-key-settings-section";
import { ProxySettingsSection } from "./components/proxy-settings-section";

interface SettingsClientProps {
  data: SettingsData;
}

export function SettingsClient({ data }: SettingsClientProps) {
  const router = useRouter();
  const [showSecretKey, setShowSecretKey] = useState(false);

  const handleBack = () => {
    router.push('/dashboard');
  };

  return (
    <div className="flex min-h-screen flex-col">
      <header className="h-16 flex items-center border-b">
        <Container className="flex w-full items-center justify-between">
          <div className="flex items-center gap-2">
            <Button variant="ghost" size="icon" onClick={handleBack} className="mr-2">
              <ChevronLeft className="h-5 w-5" />
              <span className="sr-only">Back to Dashboard</span>
            </Button>
            <span className="text-xl font-bold">Settings</span>
          </div>
          <div className="flex items-center gap-4">
            <MobileNav />
          </div>
        </Container>
      </header>
      
      <main className="flex-1 py-8 pb-20 md:pb-8">
        <Container className="max-w-4xl">
          <div className="mb-8">
            <h1 className="text-3xl font-bold">Account Settings</h1>
            <p className="text-muted-foreground mt-2">
              Manage your account, proxies, exchange connections, and security settings.
            </p>
          </div>

          <Tabs defaultValue="account" className="space-y-6">
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="account">Account</TabsTrigger>
              <TabsTrigger value="proxies">Proxies</TabsTrigger>
              <TabsTrigger value="exchanges">Exchanges</TabsTrigger>
              <TabsTrigger value="security">Security</TabsTrigger>
            </TabsList>

            {/* Account Tab */}
            <TabsContent value="account" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <User className="h-5 w-5" />
                    Profile Information
                  </CardTitle>
                  <CardDescription>
                    Your basic account information and preferences.
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="text-sm font-medium">Name</label>
                      <p className="text-sm text-muted-foreground">{data.user.name}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium">Email</label>
                      <p className="text-sm text-muted-foreground">{data.user.email}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium">Account Status</label>
                      <div className="flex items-center gap-2">
                        <Badge variant={data.user.verified ? "default" : "secondary"}>
                          {data.user.verified ? "Verified" : "Unverified"}
                        </Badge>
                      </div>
                    </div>
                    <div>
                      <label className="text-sm font-medium">Member Since</label>
                      <p className="text-sm text-muted-foreground">
                        {new Date(data.user.createdAt).toLocaleDateString()}
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            {/* Proxies Tab */}
            <TabsContent value="proxies" className="space-y-6">
              <ProxySettingsSection proxies={data.proxies} />
            </TabsContent>

            {/* Exchanges Tab */}
            <TabsContent value="exchanges" className="space-y-6">
              <ExchangeSettingsSection
                exchangeAccounts={data.exchangeAccounts}
                proxies={data.proxies}
              />
            </TabsContent>

            {/* Security Tab */}
            <TabsContent value="security" className="space-y-6">
              <SecretKeySettingsSection secretKeyData={data.secretKeyData} />
            </TabsContent>
          </Tabs>
        </Container>
      </main>
      
      <BottomNav />
    </div>
  );
}
