"use client";

import { AccountsCard } from "../components/accountCard";
import TotalInvestmentSummary from "../components/investments/DashboardTotalInvestmentSummary";
import { PerformanceSummary } from "../components/performanceSummary";
export const OldDashboardView = () => {
  const accounts = [
    {
      id: "1",
      name: "Primary Binance Account",
      exchange: "binance",
      currentValue: 12500.75,
      initialInvestment: 10000,
      daysElapsed: 120,
      totalDays: 180, // ✅ new
      totalProfit: 2500.75,
      startDate: "2025-01-01", // ✅ new
      status: "active",
    },
    {
      id: "2",
      name: "Coinbase Savings",
      exchange: "coinbase",
      currentValue: 8300.5,
      initialInvestment: 9000,
      daysElapsed: 90,
      totalDays: 150, // ✅ new
      totalProfit: -699.5,
      startDate: "2025-02-15", // ✅ new
      status: "inactive",
    },
    {
      id: "3",
      name: "Kraken Futures",
      exchange: "kraken",
      currentValue: 4200.0,
      initialInvestment: 3500,
      daysElapsed: 45,
      totalDays: 90, // ✅ new
      totalProfit: 700,
      startDate: "2025-03-10", // ✅ new
      status: "active",
    },
  ];

  const mainAccount = accounts[0];

  return (
    <>
      <div className="flex flex-col space-y-4 mb-6 md:mb-8 md:flex-row md:items-center md:justify-between md:space-y-0">
        <div>
          <h1 className="text-2xl md:text-3xl font-bold text-gray-900 dark:text-white">
            Investment Manager
          </h1>
          <p className="text-sm md:text-base text-gray-600 dark:text-gray-300">
            Manage your USDT investments and track compound growth
          </p>
        </div>
      </div>
      <div className="space-y-6">
        <TotalInvestmentSummary
          summary={{
            startingValue: mainAccount.initialInvestment,
            currentValue: mainAccount.currentValue,
            daysElapsed: mainAccount.daysElapsed,
            totalDays: mainAccount.totalDays,
            totalProfit: mainAccount.totalProfit,
            startDate: mainAccount.startDate,
          }}
        />

        {/* Account Overview */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 items-start">
          <AccountsCard
            accounts={accounts}
            onViewDetails={(id) => console.log("View details clicked:", id)}
            onSettingsClick={(id) => console.log("Settings clicked:", id)}
            onAddAccount={() => console.log("Add new account clicked")}
          />

          <PerformanceSummary
            totalReturn={
              (mainAccount.currentValue / mainAccount.initialInvestment - 1) *
              100
            }
            daysActive={mainAccount.daysElapsed}
            avgDailyProfit={
              mainAccount.totalProfit / Math.max(mainAccount.daysElapsed, 1)
            }
          />
        </div>
      </div>
    </>
  );
};



"use client";

import { Button } from "@/components/ui/button";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Activity,
  Hourglass,
  Plus,
  Users,
  Wallet,
  XCircle,
} from "lucide-react";
import { useRef, useState } from "react";

import CircleMembersList from "./CircleMembersList";

import { Investment } from "@/app/data/investmentModel";
import { useAdmin } from "@/contexts/admin-provider";
import { useInvestment } from "@/contexts/investment-context";
import { useUser } from "@/contexts/user-provider";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { AppDialogRef, AppDrawerRef, ApplicationDialog } from "../../dialog";
import { InvestmentCard } from "../InvestmentCard";
import { InvestmentCreateDialog } from "../InvestmentCreateDialog";
import { InvestmentSummary } from "./InvestmentSummary";

type InvestmentListProps = {};

export const InvestmentList = ({ }: InvestmentListProps) => {
  const router = useRouter();
  const { circleMembers, stats, onOpenWalletDialog } = useAdmin();
  const { wallet } = useUser();
  const { investments } = useInvestment();
  const [selectedInvestmentId, setSelectedInvestmentId] = useState("");
  const [tabStatus, setTabStatus] = useState<string>("active");

  const statuses: string[] = ["active", "closed", "pending", "all"];

  const onCreateInvestment = () => {
    onDrawerOpen();
  };

  const onDrawerOpen = () => {
    appDrawerRef.current?.open();
  };
  const appDrawerRef = useRef<AppDrawerRef>(null);

  const onEditInvestment = (investmentToEdit: Investment) => {
    setSelectedInvestmentId(investmentToEdit.id);
    onDrawerOpen();
  };

  const onClose = () => {
    setSelectedInvestmentId("");
    appDrawerRef.current?.close();
  };

  const appDialogRef = useRef<AppDialogRef>(null);

  return (
    <>
      {/* Header */}
      <div className="flex flex-col space-y-4 mb-6 md:mb-8 md:flex-row md:items-center md:justify-between md:space-y-0">
        <div>
          <h1 className="text-2xl md:text-3xl font-bold text-gray-900 dark:text-white">
            Investment Manager
          </h1>
          <p className="text-sm md:text-base text-gray-600 dark:text-gray-300">
            Manage your USDT investments and track compound growth
          </p>
        </div>
        <div className="flex flex-col space-y-1 md:flex-row md:items-center md:space-y-0 md:space-x-3">
          <Link
            href="/admin/invitations"
            className="relative inline-block w-full sm:w-auto"
          >
            <Button
              variant="outline"
              className="relative w-full sm:w-auto justify-center"
            >
              Invitations
              {stats.pending > 0 && (
                <span
                  className="absolute -top-2 -right-2 flex h-5 w-5 items-center justify-center 
          rounded-full bg-[#245c1a] text-xs font-bold text-white"
                >
                  {stats.pending}
                </span>
              )}
            </Button>
          </Link>

          <Button
            onClick={() => appDialogRef.current?.open()}
            variant="outline"
            className="flex items-center justify-center gap-2 h-11 md:h-10"
            size="default"
          >
            <Users className="w-4 h-4" />
            <span className="md:inline">
              My Circle ({circleMembers.length})
            </span>
          </Button>
          <Button
            onClick={onCreateInvestment}
            className="bg-[#245c1a] hover:bg-[#1a4513] dark:bg-green-600 dark:hover:bg-green-700 flex items-center justify-center gap-2 h-11 md:h-10"
            size="default"
          >
            <Plus className="w-4 h-4" />
            New Investment
          </Button>
        </div>
      </div>
      {wallet.blacklisted && (
        <div className="mb-4 rounded-lg border border-red-300 bg-red-50 p-4 dark:bg-red-900 dark:border-red-700 flex items-center justify-between">
          <span className="text-sm text-red-700 dark:text-red-200">
            Your wallet account is invalid. Please update it to continue using
            investments.
          </span>
          <Button
            onClick={onOpenWalletDialog}
            className="ml-4 bg-red-600 hover:bg-red-700 text-white"
          >
            Fix Wallet
          </Button>
        </div>
      )}

      {/* Overview Cards */}
      <InvestmentSummary investments={investments} tabStatus={tabStatus} />
      <Tabs value={tabStatus} onValueChange={setTabStatus} className="mb-6">
        <TabsList className="grid w-full grid-cols-4 h-12 md:h-10">
          {statuses.map((status) => (
            <TabsTrigger
              key={status}
              value={status}
              onClick={() => setTabStatus(status)}
              className="text-xs md:text-sm"
            >
              {status.charAt(0).toUpperCase() + status.slice(1)}
            </TabsTrigger>
          ))}
        </TabsList>

        {statuses.map((status) => {
          const filteredInvestments = investments.filter((investment) => {
            // derive display status
            const displayStatus =
              (investment.status === "initialize" &&
                investment.verifyingPayment) ||
                (investment.status === "matured" && investment.verifyingPayment)
                ? "pending"
                : investment.status;

            if (status === "all") return true;
            if (status === "closed")
              return displayStatus === "matured" || displayStatus === "closed";
            return displayStatus === status;
          });

          return (
            <TabsContent key={status} value={status} className="mt-4 md:mt-6">
              <InvestmentTabContent
                status={
                  status as "active" | "matured" | "closed" | "pending" | "all"
                }
                onClick={onCreateInvestment}
                isEmpty={filteredInvestments.length === 0}
              >
                {filteredInvestments.map((investment) => (
                  <InvestmentCard
                    key={investment.id}
                    investment={investment}
                    onEdit={() => onEditInvestment(investment)}
                    onClick={() =>
                      router.push(`/admin/investments/${investment.id}`)
                    }
                    onTabStatus={setTabStatus}
                  />
                ))}
              </InvestmentTabContent>
            </TabsContent>
          );
        })}
      </Tabs>
      {/* Create Investment Drawer  onClose, - Fixed Scrolling for All Screen Sizes */}
      <InvestmentCreateDialog
        onClose={onClose}
        ref={appDrawerRef}
        selectedInvestmentId={selectedInvestmentId}
        key={selectedInvestmentId || "newInvestment"}
        onTabChange={setTabStatus}
      />
      <ApplicationDialog
        componentType="dialog"
        title="Add Circle Member"
        description="Add someone to your trading circle to create investments for them."
        ref={appDialogRef}
        icon="user"
      >
        <CircleMembersList />
      </ApplicationDialog>
    </>
  );
};

type InvestmentTabContentProps = {
  children: React.ReactNode;
  isEmpty?: boolean;
  onClick?: () => void;
  status: "active" | "matured" | "closed" | "pending" | "all";
};

const InvestmentTabContent = ({
  children,
  isEmpty,
  onClick,
  status,
}: InvestmentTabContentProps) => {
  const content = {
    active: {
      title: "No Active Investments",
      description: `You don't have any active investments yet. Start your first investment
          to begin earning compound returns.`,
      icon: {
        component: Activity,
        className: "text-blue-600 dark:text-blue-400",
        bgClass: "bg-blue-100 dark:bg-blue-900/20",
      },
    },
    matured: {
      title: "No Matured Investments",
      description: `You don't have any matured investments ready for withdrawal yet.
          Your active investments will appear here when they complete.`,
      icon: {
        component: Wallet,
        className: "text-green-600 dark:text-green-400",
        bgClass: "bg-green-100 dark:bg-green-900/20",
      },
    },
    pending: {
      title: "No Pending Investments",
      description: `You don't have any pending investments yet.Create an investment to get started.`,
      icon: {
        component: Hourglass,
        className: "text-yellow-500 dark:text-yellow-400",
        bgClass: "bg-yellow-100 dark:bg-yellow-900/20",
      },
    },
    closed: {
      title: "No Closed Investments",
      description: `You don't have any withdrawn investments yet. Completed investments
          that have been withdrawn will appear here.`,
      icon: {
        component: XCircle,
        className: "text-gray-600 dark:text-gray-400",
        bgClass: "bg-gray-100 dark:bg-gray-800",
      },
    },
    all: {
      title: "No Investments Yet",
      description: `Start your investment journey today. Create your first USDT
          investment and watch your money grow with compound interest.`,
      icon: {
        component: Wallet,
        className: "text-blue-600 dark:text-blue-400",
        bgClass: "bg-blue-100 dark:bg-blue-900/20",
      },
    },
  };

  const item = content[status];
  const Icon = item.icon.component;

  const onCreateInvestment = status === "closed" || status === "all";

  return (
    <div className="grid gap-4 md:gap-6">
      {isEmpty ? (
        <div className="text-center py-8 md:py-12">
          <div
            className={`mx-auto w-16 h-16 md:w-24 md:h-24 ${item.icon.bgClass} rounded-full flex items-center justify-center mb-4`}
          >
            <Icon
              className={`w-8 h-8 md:w-12 md:h-12 ${item.icon.className}`}
            />
          </div>
          <h3 className="text-base md:text-lg font-semibold text-gray-900 dark:text-white mb-2">
            {item.title}
          </h3>
          <p className="text-sm md:text-base text-gray-500 dark:text-gray-400 mb-4 md:mb-6 max-w-md mx-auto px-4">
            {item.description}
          </p>
          {onCreateInvestment && (
            <>
              <Button
                onClick={onClick}
                className="bg-[#245c1a] hover:bg-[#1a4513] dark:bg-green-600 dark:hover:bg-green-700 h-11 md:h-10"
              >
                <Plus className="w-4 h-4 mr-2" />
                Create Your First Investment
              </Button>
            </>
          )}
        </div>
      ) : (
        <>{children}</>
      )}
    </div>
  );
};
