import { redirect } from "next/navigation";
import { getLoggedInUser } from "../auth/actions";
import { DashboardView } from "./DashboardView";
import { InvestmentProvider } from "@/contexts/investment-context";
import { getUserInvestments } from "./actions";


export default async function BeneficiaryDashboardPage() {
  const user = await getLoggedInUser();
  const isAdmin = user?.role! === "admin";
  if (isAdmin) {
    redirect("/admin");
  }
  const investments = await getUserInvestments();
  return (
    <InvestmentProvider investments={investments} >
      <DashboardView investments={investments} />
    </InvestmentProvider>
  )


}
