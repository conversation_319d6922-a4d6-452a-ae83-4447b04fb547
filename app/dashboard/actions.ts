// lib/actions/adminActions.ts

import { getDBService } from "@/lib/services/db";
import { getLoggedInUser } from "../auth/actions";

const investments = [
  {
    id: "1",
    name: "Investment 1",
    admin: {
      id: "1",
      name: "<PERSON>",
    },
    currentAmount: 1000,
    duration: 12,
    interestRate: 5,
    startDate: "2025-08-20",
    status: "closed",
    createdAt: "2025-08-20",
    updateAt: "2025-08-20",
    verifyingPayment: false,
    paymentsCount: 0,
    withdrawn_at: null,
    withdrawal_request: null,
  },
  {
    id: "2",
    name: "Investment 2",
    admin: {
      id: "10",
      name: "<PERSON><PERSON>",
    },
    currentAmount: 500,
    duration: 12,
    interestRate: 5,
    startDate: "2025-08-20",
    status: "active",
    createdAt: "2025-08-20",
    updateAt: "2025-08-20",
    verifyingPayment: false,
    paymentsCount: 0,
    withdrawn_at: null,
    withdrawal_request: null,
    walletAddress: "0x1234567890",
    network: "Ethereum",
  },
];

export async function fetchUserAccounts() {
  return investments;
}

export async function getUserInvestments() {
  // const user = await getLoggedInUser();
  // const dbService = await getDBService();
  // const investments = await dbService.getMergedInvestments(user?.id!);
  return investments;
}
