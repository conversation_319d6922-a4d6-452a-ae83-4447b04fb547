"use client";

import { Container } from "@/components/container";
import { Button } from "@/components/ui/button";
import { useState } from "react";
import { ExtendedInvestmentStatusType } from "../components/investments/InvestmentList";
import { InvestmentSummary } from "../components/investments/InvestmentList/InvestmentSummary";
import { UserInvestmentTabs } from "../components/investments/investmentTabs/UserInvestmentTabs";
import { useUser } from "@/contexts/user-provider";


export const DashboardView = ({ investments }: { investments: any[] }) => {
    const { wallet } = useUser();

    function onOpenWalletDialog() {
        console.log("Open wallet dialog");
    }



    const [tabStatus, setTabStatus] = useState<ExtendedInvestmentStatusType>("active");
    return (
        <>

            <div className="min-h-screen bg-gray-50 dark:bg-gray-900 pb-20 md:pb-0">
                <Container className="py-4 md:py-8">
                    <div className="max-w-7xl mx-auto">
                        {/* Header */}
                        <div className="flex flex-col space-y-4 mb-6 md:mb-8 md:flex-row md:items-center md:justify-between md:space-y-0">
                            <div>
                                <h1 className="text-2xl md:text-3xl font-bold text-gray-900 dark:text-white">
                                    My investment progress
                                </h1>
                                <p className="text-sm md:text-base text-gray-600 dark:text-gray-300">
                                    Manage your USDT investments and track compound growth
                                </p>
                            </div>

                        </div>
                        {wallet.blacklisted && (
                            <div className="mb-4 rounded-lg border border-red-300 bg-red-50 p-4 dark:bg-red-900 dark:border-red-700 flex items-center justify-between">
                                <span className="text-sm text-red-700 dark:text-red-200">
                                    Your wallet account is invalid. Please update it to continue using
                                    investments.
                                </span>
                                <Button
                                    onClick={onOpenWalletDialog}
                                    className="ml-4 bg-red-600 hover:bg-red-700 text-white"
                                >
                                    Fix Wallet
                                </Button>
                            </div>
                        )}
                        {/* Overview Cards */}
                        <InvestmentSummary investments={investments} tabStatus={tabStatus} />
                        {/* <Tabs
                            value={tabStatus}
                            onValueChange={(value) => setTabStatus(value as InvestmentStatusType)}
                            className="mb-6"
                        >
                            <TabsList className="grid w-full grid-cols-4 h-12 md:h-10">
                                {statuses.map((status) => (
                                    <TabsTrigger
                                        key={status}
                                        value={status}
                                        onClick={() => setTabStatus(status as ExtendedInvestmentStatusType)}
                                        className="text-xs md:text-sm"
                                    >
                                        {status.charAt(0).toUpperCase() + status.slice(1)}
                                    </TabsTrigger>
                                ))}
                            </TabsList>

                            {statuses.map((status) => {
                                const filteredInvestments = investments.filter((investment) => {
                                    // derive display status
                                    const displayStatus =
                                        (investment.status === "initialize" &&
                                            investment.verifyingPayment)
                                            ? "pending"
                                            : investment.status;

                                    if (status === "all") return true;
                                    return displayStatus === status;
                                });

                                return (
                                    <TabsContent key={status} value={status} className="mt-4 md:mt-6">
                                        <InvestmentTabContent
                                            status={
                                                status as "active" | "matured" | "closed" | "pending" | "all"
                                            }
                                            isEmpty={filteredInvestments.length === 0}
                                        >
                                            {filteredInvestments.map((investment) => (
                                                <DashboardInvestmentCard
                                                    investment={investment}
                                                />
                                            ))}
                                        </InvestmentTabContent>
                                    </TabsContent>
                                );
                            })}
                        </Tabs> */}
                        <UserInvestmentTabs
                            investments={investments}
                            tabStatus={tabStatus}
                            setTabStatus={setTabStatus}
                        />
                    </div>
                </Container>
            </div>
        </>
    )
}
