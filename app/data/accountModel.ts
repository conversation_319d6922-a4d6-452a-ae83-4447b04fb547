// data/accountModel.ts
import { RegularUserAccount } from "@/lib/constants/investment";
function generateSlug(id: string, startDate: string) {
  const datePart = new Date(startDate).toISOString().split("T")[0]; // "2025-08-23"
  return `${id}-${datePart}`;
}

export const accountModel = {
  // Initial mock accounts
  mockAccounts: () => [
    {
      id: "1",
      slug: generateSlug("1", "2025-08-23 08:01:57.858Z"),
      status: "matured",
      profitFrequency: "monthly",
      interestRate: 30.0,
      users: [
        {
          userId: "o88rxditusrypjr",
          investedAmount: 20,
          currentValue: 22,
          lastUpdate: "2025-08-23 08:01:57.858Z",
          status: "inactive",
        },
        {
          userId: "2fjsy09ind1779p",
          investedAmount: 20,
          currentValue: 23,
          lastUpdate: "2025-08-21T08:01:57.858Z",
          status: "active",
        },
      ],
      startDate: "2025-06-30T08:01:57.858Z",
      investmentPeriod: 180,
      interestType: "simple",
      noOfUsers: 2,
      stats: {
        totalInvestmentAmount: 20,
        totalCurrentValue: 70,
      },
      name: "3-Month USDT Investment",
      initialAmount: 1000,
      currentAmount: 1331, // After 3 months: 1000 * 1.1^3
      duration: 3,
      beneficiaryRate: 10,
      endDate: "2024-04-01",
      monthsElapsed: 3,
      progressPercentage: 100,
      paymentMethod: "crypto",
      network: "TRC20",
      createdAt: "2024-01-01T00:00:00Z",
      isForSelf: true,
      canWithdraw: true,
    },

    {
      id: "2",
      slug: generateSlug("2", "2025-08-15T09:21:00.000Z"),
      status: "active",
      profitFrequency: "monthly",
      interestRate: 15.0,
      users: [
        {
          userId: "o88rxditusrypjr",
          investedAmount: 50,
          currentValue: 55,
          lastUpdate: "2025-08-10T12:00:00.000Z",
          status: "active",
        },
        {
          userId: "2fjsy09ind1779p",
          investedAmount: 50,
          currentValue: 57,
          lastUpdate: "2025-08-12T08:00:00.000Z",
          status: "active",
        },
      ],
      startDate: "2025-07-01T08:01:57.858Z",
      investmentPeriod: 180,
      interestType: "simple",
      noOfUsers: 2,
      stats: {
        totalInvestmentAmount: 20,
        totalCurrentValue: 30,
      },
      name: "6-Month USDT Investment for Kyle",
      initialAmount: 2500,
      currentAmount: 2420, // After 2 months: 2500 * 1.08^2 (8% for Kyle)
      duration: 6,
      beneficiaryRate: 8, // Kyle sees 8%
      endDate: "2024-08-01",
      monthsElapsed: 2,
      progressPercentage: 33.33,
      paymentMethod: "card",
      createdAt: "2024-02-01T00:00:00Z",
      createdBy: "current_user",
      createdByName: "John Doe",
      beneficiaryId: "member_1",
      beneficiaryName: "Kyle Johnson",
      isForSelf: false,
      canWithdraw: true,
    },
    {
      id: "3",
      slug: generateSlug("3", "2025-08-10T14:00:00.000Z"),
      status: "closed",
      profitFrequency: "monthly",
      interestRate: 12.5,
      users: [
        {
          userId: "o88r",
          investedAmount: 200,
          currentValue: 210,
          lastUpdate: "2025-08-18T14:00:00.000Z",
          status: "active",
        },
      ],
      startDate: "2025-07-15T08:01:57.858Z",
      investmentPeriod: 120,
      interestType: "compound",
      noOfUsers: 2,
      stats: {
        totalInvestmentAmount: 20,
        totalCurrentValue: 30,
      },
      name: "12-Month USDT Investment",
      initialAmount: 5000,
      currentAmount: 5500, // After 1 month: 5000 * 1.1^1
      duration: 12,
      beneficiaryRate: 10,
      endDate: "2025-03-01",
      monthsElapsed: 1,
      progressPercentage: 8.33,
      paymentMethod: "crypto",
      network: "ERC20",
      createdAt: "2024-03-01T00:00:00Z",
      isForSelf: true,
      canWithdraw: true,
    },
    {
      id: "4",
      slug: generateSlug("4", "2025-08-05T10:15:00.000Z"),
      status: "active",
      profitFrequency: "monthly",
      interestRate: 20.0,
      users: [
        {
          userId: "o88rxdijr",
          investedAmount: 150,
          currentValue: 150,
          lastUpdate: "2025-08-05T10:15:00.000Z",
          status: "active",
        },
      ],
      startDate: "2025-08-05T08:01:57.858Z",
      investmentPeriod: 45,
      interestType: "simple",
      noOfUsers: 2,
      stats: {
        totalInvestmentAmount: 20,
        totalCurrentValue: 30,
      },

      name: "Investment from Sarah Williams",
      initialAmount: 1500,
      currentAmount: 1575, // After 1 month: 1500 * 1.05^1 (5% for current user)
      duration: 6,
      beneficiaryRate: 5,
      endDate: "2024-09-01",
      monthsElapsed: 1,
      progressPercentage: 16.67,
      paymentMethod: "crypto",
      network: "TRC20",
      createdAt: "2024-03-01T00:00:00Z",
      createdBy: "member_2",
      createdByName: "Sarah Williams",
      beneficiaryId: "current_user",
      beneficiaryName: "Current User",
      isForSelf: false,
      canWithdraw: false,
    },
    // {
    //   id: "5",
    //   slug: generateSlug("5", "2025-07-20T16:00:00.000Z"),
    //   status: "completed",
    //   profitFrequency: "monthly",
    //   interestRate: 25.0,
    //   users: [
    //     {
    //       userId: "o88rxditusrypjr",
    //       investedAmount: 300,
    //       currentValue: 360,
    //       lastUpdate: "2025-08-20T16:00:00.000Z",
    //       status: "active",
    //     },
    //   ],
    //   startDate: "2025-06-01T08:01:57.858Z",
    //   investmentPeriod: 60,
    //   interestType: "simple",
    //   noOfUsers: 2,
    //   stats: {
    //     totalInvestmentAmount: 20,
    //     totalCurrentValue: 30,
    //   },
    // },
    // {
    //   id: "6",
    //   slug: generateSlug("6", "2025-07-10T09:00:00.000Z"),
    //   status: "completed",
    //   profitFrequency: "monthly",
    //   interestRate: 10.0,
    //   users: [
    //     {
    //       userId: "o88rxditusrypjr",
    //       investedAmount: 300,
    //       currentValue: 300,
    //       lastUpdate: "2025-08-15T09:00:00.000Z",
    //       status: "active",
    //     },
    //     {
    //       userId: "2fjsy09inffd1779p",
    //       investedAmount: 200,
    //       currentValue: 300,
    //       lastUpdate: "2025-08-18T11:00:00.000Z",
    //       status: "active",
    //     },
    //   ],
    //   startDate: "2025-05-01T08:01:57.858Z",
    //   investmentPeriod: 100,
    //   interestType: "simple",
    //   noOfUsers: 2,
    //   stats: {
    //     totalInvestmentAmount: 20,
    //     totalCurrentValue: 30,
    //   },
    // },
    // {
    //   id: "7",
    //   slug: generateSlug("7", "2025-08-01T11:30:00.000Z"),
    //   status: "active",
    //   profitFrequency: "monthly",
    //   interestRate: 10.0,
    //   users: [
    //     {
    //       userId: "oypjr",
    //       investedAmount: 255,
    //       currentValue: 255,
    //       lastUpdate: "2025-08-21T11:30:00.000Z",
    //       status: "active",
    //     },
    //   ],
    //   startDate: "2025-07-25T08:01:57.858Z",
    //   investmentPeriod: 30,
    //   interestType: "compound",
    //   noOfUsers: 2,
    //   stats: {
    //     totalInvestmentAmount: 20,
    //     totalCurrentValue: 30,
    //   },
    // },
  ],

  // Regular user account view
  mockRegularUserAccounts: (): RegularUserAccount[] => [
    {
      id: "1",
      slug: generateSlug("1", "2025-08-23T08:01:57.858Z"),
      status: "inactive", // user’s own status
      profitFrequency: "monthly",
      interestRate: 30.0,
      investedAmount: 20,
      currentValue: 22,
      lastUpdate: "2025-08-23T08:01:57.858Z",
      startDate: "2025-06-30T08:01:57.858Z",
      investmentPeriod: 60,
      interestType: "simple",
      exchange: "binance",
    },
    {
      id: "2",
      slug: generateSlug("2", "2025-08-15T09:21:00.000Z"),
      status: "active",
      profitFrequency: "monthly",
      interestRate: 15.0,
      investedAmount: 50,
      currentValue: 55,
      lastUpdate: "2025-08-10T12:00:00.000Z",
      startDate: "2025-07-01T08:01:57.858Z",
      investmentPeriod: 90,
      interestType: "simple",
    },
    {
      id: "3",
      slug: generateSlug("3", "2025-08-10T14:00:00.000Z"),
      status: "active",
      profitFrequency: "monthly",
      interestRate: 12.5,
      investedAmount: 200,
      currentValue: 210,
      lastUpdate: "2025-08-18T14:00:00.000Z",
      startDate: "2025-07-15T08:01:57.858Z",
      investmentPeriod: 120,
      interestType: "compound",
    },
    {
      id: "5",
      slug: generateSlug("5", "2025-07-20T16:00:00.000Z"),
      status: "active",
      profitFrequency: "monthly",
      interestRate: 25.0,
      investedAmount: 300,
      currentValue: 360,
      lastUpdate: "2025-08-20T16:00:00.000Z",
      startDate: "2025-06-01T08:01:57.858Z",
      investmentPeriod: 60,
      interestType: "simple",
    },
    {
      id: "6",
      slug: generateSlug("6", "2025-07-10T09:00:00.000Z"),
      status: "active",
      profitFrequency: "monthly",
      interestRate: 10.0,
      investedAmount: 300,
      currentValue: 300,
      lastUpdate: "2025-08-15T09:00:00.000Z",
      startDate: "2025-05-01T08:01:57.858Z",
      investmentPeriod: 100,
      interestType: "simple",
    },
  ],

  // Factory for creating new accounts
  create: (
    id: number,
    initialInvestment = 40,
    currentValue = 40
  ): InvestorProfile => ({
    id: id.toString(),
    slug: generateSlug(id.toString(), new Date().toISOString()),
    status: "closed",
    profitFrequency: "monthly",
    interestRate: 30.0,
    users: [],
    startDate: new Date().toISOString(),
    investmentPeriod: 60,
    interestType: "simple",
  }),
};
