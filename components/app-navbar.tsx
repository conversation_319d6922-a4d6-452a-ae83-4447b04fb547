"use client";

import { Container } from "@/components/container";
import { Button } from "@/components/ui/button";
import { useUser } from "@/contexts/user-provider";
import { BarChart2, Settings } from "lucide-react";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { MobileNav } from "./mobileNav";
import { ThemeToggle } from "./theme-toggle";
import { UserDropdown } from "./userDropdown";

export default function AppNavbar() {
  const pathname = usePathname();
  const { user, isAuthenticated, isAdmin } = useUser();

  const getUserInitials = () => {
    if (!user?.name) {
      if (isAdmin) return "A";
      return "U";
    }
    return user.name
      .split(" ")
      .map((n) => n[0])
      .join("")
      .toUpperCase()
      .slice(0, 2);
  };

  return (
    <header className="h-16 flex items-center border-b">
      <Container className="flex w-full items-center justify-between">
        <div className="flex items-center gap-2">
          <BarChart2 className="h-6 w-6 text-[#245c1a]" />
          <span className="text-xl font-bold">TradeSmart</span>
        </div>

        <div className="flex items-center gap-4">
          {/* Desktop View */}
          <div className="hidden md:flex items-center gap-4">
            <ThemeToggle />
            {isAuthenticated ? (
              <>
                <Link href="/settings">
                  <Button variant="ghost" size="icon">
                    <Settings className="h-5 w-5" />
                    <span className="sr-only">Settings</span>
                  </Button>
                </Link>
                <UserDropdown initials={getUserInitials()} />
              </>
            ) : (
              <>
                <Link href="/auth/login">
                  <Button
                    variant={pathname === "/auth/login" ? "primary" : "outline"}
                  >
                    Log In
                  </Button>
                </Link>

                <Link href="/auth/signup">
                  <Button
                    variant={
                      pathname === "/auth/signup" ? "primary" : "outline"
                    }
                  >
                    Sign Up
                  </Button>
                </Link>
              </>
            )}
          </div>

          {/* Mobile View */}
          <div className="flex items-center gap-3 md:hidden">
            <ThemeToggle />
            <MobileNav
              isAuthenticated={isAuthenticated}
              isAdmin={isAdmin}
              initials={getUserInitials()}
            />
          </div>
        </div>
      </Container>
    </header>
  );
}
