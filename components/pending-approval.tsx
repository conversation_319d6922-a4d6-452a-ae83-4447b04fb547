"use client";

import { useApprovalStatus } from "@/lib/hooks/use-approval-status";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Loader2, Clock, CheckCircle, AlertCircle } from "lucide-react";
import { useRouter } from "next/navigation";
import { useEffect } from "react";
import { useUser } from "@/contexts/user-provider";

interface PendingApprovalProps {
  inviteCode?: string;
}

export function PendingApproval({ inviteCode }: PendingApprovalProps) {
  const { user: currentUser } = useUser();
  const router = useRouter();

  const { approved, loading, error, user, refetch } = useApprovalStatus(
    currentUser?.email
  );

  // Redirect to dashboard if approved
  useEffect(() => {
    if (approved === true) {
      router.push("/dashboard");
    }
  }, [approved, router]);

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <Card className="w-full max-w-md">
          <CardContent className="pt-6">
            <div className="flex flex-col items-center space-y-4">
              <Loader2 className="h-8 w-8 animate-spin text-blue-600" />
              <p className="text-sm text-gray-600">
                Checking approval status...
              </p>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <Card className="w-full max-w-md">
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <AlertCircle className="h-5 w-5 text-red-600" />
              <span>Error</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-gray-600 mb-4">{error}</p>
            <Button onClick={refetch} className="w-full">
              Try Again
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (approved === true) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <Card className="w-full max-w-md">
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <CheckCircle className="h-5 w-5 text-green-600" />
              <span>Approved!</span>
            </CardTitle>
            <CardDescription>
              Your account has been approved. Redirecting to dashboard...
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex items-center justify-center">
              <Loader2 className="h-6 w-6 animate-spin text-blue-600" />
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Show pending approval screen
  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-yellow-100">
            <Clock className="h-6 w-6 text-yellow-600" />
          </div>
          <CardTitle>Approval Pending</CardTitle>
          <CardDescription>
            Your account is waiting for approval
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="text-center space-y-2">
            <p className="text-sm text-gray-600">
              Welcome, <span className="font-medium">{user?.name}</span>!
            </p>
            <p className="text-sm text-gray-600">
              Your account has been created successfully, but it needs to be
              approved before you can access the trading platform.
            </p>
            {inviteCode && (
              <p className="text-xs text-gray-500">
                Invited with code:{" "}
                <span className="font-mono bg-gray-100 px-1 rounded">
                  {inviteCode}
                </span>
              </p>
            )}
          </div>

          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <h4 className="text-sm font-medium text-blue-900 mb-2">
              What happens next?
            </h4>
            <ul className="text-xs text-blue-800 space-y-1">
              <li>• The person who invited you will be notified</li>
              <li>• They will review and approve your account</li>
              <li>• You'll be automatically redirected once approved</li>
              <li>• This page will update in real-time</li>
            </ul>
          </div>

          <div className="flex flex-col space-y-2">
            <Button variant="outline" onClick={refetch} className="w-full">
              Check Status
            </Button>
            <Button
              variant="ghost"
              onClick={() => router.push("/auth/login")}
              className="w-full text-sm"
            >
              Sign Out
            </Button>
          </div>

          <div className="text-center">
            <p className="text-xs text-gray-500">
              Account created:{" "}
              {user?.created
                ? new Date(user.created).toLocaleDateString()
                : "Unknown"}
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

export default PendingApproval;
