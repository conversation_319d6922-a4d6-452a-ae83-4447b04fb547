"use client"

import type { ReactNode } from "react"
import { useMobile } from "@/hooks/use-mobile"
import { usePathname } from "next/navigation"

interface LayoutWrapperProps {
  children: ReactNode
}

export function LayoutWrapper({ children }: LayoutWrapperProps) {
  const isMobile = useMobile()
  const pathname = usePathname()

  // Don't add padding on auth pages
  const isAuthPage = pathname.startsWith("/auth")

  return <div className={`${isMobile && !isAuthPage ? "pb-16" : ""}`}>{children}</div>
}
