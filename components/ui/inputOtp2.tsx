"use client";

import { Input } from "@/components/ui/input";
import { useRef, useState } from "react";

export const OTP_LENGTH = 8;

type InputOtpProps = {
  onChangeAction: (value: string) => void;
};

export function InputOtp2({ onChangeAction }: InputOtpProps) {
  const [verificationCode, setVerificationCode] = useState("");
  const inputRefs = useRef<any[]>([]);
  const CodeLength = Array.from({ length: OTP_LENGTH }, (_, index) => index);

  const handlePaste = (e: React.ClipboardEvent<HTMLInputElement>) => {
    e.preventDefault();
    const pastedData = e.clipboardData.getData("text").replace(/\D/g, "");
    
    if (pastedData.length > 0) {
      const newCode = pastedData.slice(0, OTP_LENGTH).padEnd(OTP_LENGTH, "");
      const codeArray = newCode.split("");
      
      setVerificationCode(newCode.replace(/\s/g, ""));
      onChangeAction(newCode.replace(/\s/g, ""));
      
      const lastFilledIndex = Math.min(pastedData.length - 1, OTP_LENGTH - 1);
      inputRefs.current[lastFilledIndex]?.focus();
    }
  };

  const handleVerificationChange = (index: number, value: string) => {
    if (value.length > 1) {
      const digits = value.replace(/\D/g, "");
      if (digits.length >= OTP_LENGTH) {
        const newCode = digits.slice(0, OTP_LENGTH);
        setVerificationCode(newCode);
        onChangeAction(newCode);
        inputRefs.current[OTP_LENGTH - 1]?.focus();
        return;
      } else if (digits.length > 1) {
        const remainingSpace = OTP_LENGTH - index;
        const digitsToFill = digits.slice(0, remainingSpace);
        const newCode = verificationCode.split("");
        
        for (let i = 0; i < digitsToFill.length; i++) {
          newCode[index + i] = digitsToFill[i];
        }
        
        const finalCode = newCode.join("");
        setVerificationCode(finalCode);
        onChangeAction(finalCode);
        
        const nextIndex = Math.min(index + digitsToFill.length - 1, OTP_LENGTH - 1);
        inputRefs.current[nextIndex]?.focus();
        return;
      }
      value = digits[0] || "";
    }

    const newCode = verificationCode.split("");
    newCode[index] = value;
    const finalCode = newCode.join("");
    setVerificationCode(finalCode);
    onChangeAction(finalCode);

    if (value && index < OTP_LENGTH - 1) {
      inputRefs.current[index + 1]?.focus();
    }
  };

  const handleVerificationKeyDown = (
    index: number,
    e: React.KeyboardEvent<HTMLInputElement>
  ) => {
    if (e.key === "Backspace" && !verificationCode[index] && index > 0) {
      inputRefs.current[index - 1]?.focus();
    }
  };
  return (
    <>
      {CodeLength.map((index) => (
        <Input
          key={index}
          ref={(el) => (inputRefs.current[index] = el) as any}
          type="text"
          inputMode="numeric"
          pattern="[0-9]*"
          maxLength={OTP_LENGTH}
          className="w-9 h-9 md:w-11 md:h-11 text-center text-xs md:text-lg"
          value={verificationCode[index] || ""}
          onChange={(e) => handleVerificationChange(index, e.target.value)}
          onKeyDown={(e) => handleVerificationKeyDown(index, e)}
          onPaste={handlePaste}
          autoFocus={index === 0}
        />
      ))}
    </>
  );
}
