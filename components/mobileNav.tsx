"use client";

import { logOut } from "@/app/auth/actions";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { Button } from "@/components/ui/button";
import { Sheet, SheetContent, SheetTrigger } from "@/components/ui/sheet";
import { useUser } from "@/contexts/user-provider";
import { cn } from "@/lib/utils";
import {
  BarChart2,
  CreditCard,
  DollarSign,
  History,
  LayoutDashboard,
  LogIn,
  LogOut,
  Menu,
  Settings,
  User,
  UserPlus,
  Wallet,
  X,
} from "lucide-react";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { useState } from "react";

interface MobileNavProps {
  isAuthenticated: boolean;
  isAdmin?: boolean;
  initials: string;
}

export function MobileNav({
  isAuthenticated,
  isAdmin = false,
  initials,
}: MobileNavProps) {
  const [open, setOpen] = useState(false);
  const [isLoggingOut, setIsLoggingOut] = useState(false);
  const pathname = usePathname();
  const { user } = useUser();

  // Different routes for authenticated and non-authenticated users
  const baseAuthenticatedRoutes = [
    {
      href: "/dashboard",
      label: "Dashboard",
      icon: <LayoutDashboard className="h-5 w-5" />,
      active: pathname === "/dashboard",
    },
    {
      href: "/accounts",
      label: "Accounts",
      icon: <Wallet className="h-5 w-5" />,
      active: pathname === "/accounts",
    },
    {
      href: "/trading-history",
      label: "Trading History",
      icon: <History className="h-5 w-5" />,
      active: pathname === "/trading-history",
    },
    {
      href: "/profits",
      label: "Profits",
      icon: <DollarSign className="h-5 w-5" />,
      active: pathname === "/profits",
    },
    {
      href: "/pricing",
      label: "Upgrade Plan",
      icon: <CreditCard className="h-5 w-5" />,
      active: pathname === "/pricing",
    },
    {
      href: "/settings",
      label: "Settings",
      icon: <Settings className="h-5 w-5" />,
      active: pathname === "/settings",
    },
  ];

  // Admin routes - only shown to admin users
  const adminRoutes = [
    {
      href: "/admin/invitees",
      label: "Manage Invitees",
      icon: <User className="h-5 w-5" />,
      active: pathname === "/admin/invitees",
    },
  ];

  // Combine routes based on user role
  const authenticatedRoutes = isAdmin
    ? [...baseAuthenticatedRoutes, ...adminRoutes]
    : baseAuthenticatedRoutes;

  const nonAuthenticatedRoutes = [
    {
      href: "/",
      label: "Home",
      icon: <LayoutDashboard className="h-5 w-5" />,
      active: pathname === "/",
    },
    {
      href: "/#features",
      label: "Features",
      icon: <BarChart2 className="h-5 w-5" />,
      active:
        pathname === "/" &&
        typeof window !== "undefined" &&
        window.location.hash === "#features",
    },
    {
      href: "/#how-it-works",
      label: "How It Works",
      icon: <LayoutDashboard className="h-5 w-5" />,
      active:
        pathname === "/" &&
        typeof window !== "undefined" &&
        window.location.hash === "#how-it-works",
    },
    {
      href: "/#pricing",
      label: "Pricing",
      icon: <CreditCard className="h-5 w-5" />,
      active:
        pathname === "/" &&
        typeof window !== "undefined" &&
        window.location.hash === "#pricing",
    },
  ];

  const routes = isAuthenticated ? authenticatedRoutes : nonAuthenticatedRoutes;

  const handleLogout = async () => {
    try {
      setIsLoggingOut(true);
      setOpen(false);
      await logOut();
      window.location.href = "/";
    } catch (error) {
      console.error("Logout error:", error);
      setIsLoggingOut(false); // Only reset on error
    }

  };

  return (
    <Sheet open={open} onOpenChange={setOpen}>
      <SheetTrigger asChild>
        <Button variant="ghost" size="icon" className="md:hidden">
          {user ? (
            <Avatar className="h-10 w-10">
              <AvatarFallback className="bg-[#245c1a] text-white dark:bg-green-600">
                {initials}
              </AvatarFallback>
            </Avatar>
          ) : (
            <Menu className="h-6 w-6" />
          )}

        </Button>
      </SheetTrigger>
      <SheetContent
        side="left"
        className="w-[300px] sm:w-[350px] p-6"
        showDefaultClose={false}
      >
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <BarChart2 className="h-6 w-6 text-[#245c1a]" />
            <span className="text-xl font-bold">TradeSmart</span>
          </div>
          <Button variant="ghost" size="icon" onClick={() => setOpen(false)}>
            <X className="h-5 w-5" />
            <span className="sr-only">Close</span>
          </Button>
        </div>

        <nav className="mt-8 flex flex-col gap-4">
          {routes.map((route) => (
            <Link
              key={route.href}
              href={route.href}
              onClick={() => setOpen(false)}
              className={cn(
                "flex items-center gap-2 px-4 py-3 text-lg rounded-md hover:bg-gray-100",
                route.active ? "bg-gray-100 text-[#245c1a] font-medium" : ""
              )}
            >
              {route.icon}
              {route.label}
            </Link>
          ))}

          <div className="mt-4 pt-4 border-t">
            {isAuthenticated ? (
              <Button
                variant="ghost"
                className="w-full justify-start px-6 py-3 text-lg"
                onClick={handleLogout}
                disabled={isLoggingOut}
              >
                <LogOut className="h-5 w-5 mr-2" />
                {isLoggingOut ? "Logging out..." : "Logout"}
              </Button>
            ) : (
              <>
                <Link
                  href="/auth/login"
                  onClick={() => setOpen(false)}
                  className={`flex items-center gap-2 px-4 py-3 text-lg rounded-md hover:bg-gray-100 ${pathname === "/auth/login"
                    ? "bg-gray-100 text-[#245c1a] font-medium"
                    : ""
                    }`}
                >
                  <LogIn className="h-5 w-5" />
                  Log In
                </Link>
                <Link
                  href="/auth/signup"
                  onClick={() => setOpen(false)}
                  className={`flex items-center gap-2 px-4 py-3 text-lg rounded-md hover:bg-gray-100 ${pathname === "/auth/signup"
                    ? "bg-gray-100 text-[#245c1a] font-medium"
                    : ""
                    }`}
                >
                  <UserPlus className="h-5 w-5" />
                  Sign Up
                </Link>
              </>
            )}
          </div>
        </nav>
      </SheetContent>
    </Sheet>
  );
}
