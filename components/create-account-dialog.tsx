"use client"

import type React from "react"

import { useState, useTransition } from "react"
import { useRouter } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { toast } from "sonner"
import { Wallet } from "lucide-react"
import { createAccountAction } from "@/app/accounts/setup/actions"

interface CreateAccountDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
}

export function CreateAccountDialog({ open, onOpenChange }: CreateAccountDialogProps) {
  const [accountName, setAccountName] = useState("")
  const [error, setError] = useState("")
  const [isPending, startTransition] = useTransition()
  const router = useRouter()

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()

    // Validate account name
    if (!accountName.trim()) {
      setError("Please enter an account name")
      return
    }

    if (accountName.length > 50) {
      setError("Account name must be 50 characters or less")
      return
    }

    // Clear any previous errors
    setError("")

    // Create the account using server action
    startTransition(async () => {
      try {
        const result = await createAccountAction({
          name: accountName.trim(),
          exchange: "binance"
        })

        if (result.success) {
          toast.success("Account created successfully")
          onOpenChange(false)
          setAccountName("")

          // Redirect to account setup page
          router.push(`/accounts/setup/${encodeURIComponent(accountName.trim())}`)
        } else {
          setError(result.error || "Failed to create account")
          toast.error(result.error || "Failed to create account")
        }
      } catch (error) {
        console.error("Account creation error:", error)
        setError("An error occurred while creating the account")
        toast.error("An error occurred while creating the account")
      }
    })
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Wallet className="h-5 w-5 text-[#245c1a]" />
            Create New Trading Account
          </DialogTitle>
          <DialogDescription>Give your new trading account a name to help you identify it.</DialogDescription>
        </DialogHeader>
        <form onSubmit={handleSubmit}>
          <div className="grid gap-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="account-name" className="text-right">
                Account Name
              </Label>
              <Input
                id="account-name"
                placeholder="e.g., BTC Long-term, ETH Trading, etc."
                value={accountName}
                onChange={(e) => {
                  setAccountName(e.target.value)
                  if (error) setError("")
                }}
                className={error ? "border-red-500" : ""}
                autoFocus
              />
              {error && <p className="text-sm text-red-500">{error}</p>}
              <p className="text-xs text-gray-500">You can always change this name later in account settings.</p>
            </div>
          </div>
          <DialogFooter>
            <Button type="button" variant="outline" onClick={() => onOpenChange(false)} disabled={isPending}>
              Cancel
            </Button>
            <Button type="submit" className="bg-[#245c1a] hover:bg-[#1a4513]" disabled={isPending}>
              {isPending ? (
                <>
                  <span className="mr-2">Creating...</span>
                  <div className="h-4 w-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                </>
              ) : (
                "Create Account"
              )}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
}
