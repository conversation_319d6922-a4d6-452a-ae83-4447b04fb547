"use client";

import { useState } from "react";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { But<PERSON> } from "@/components/ui/button";
import { Sheet, SheetContent, SheetTrigger } from "@/components/ui/sheet";
import {
  BarChart2,
  Menu,
  X,
  Home,
  Settings,
  CreditCard,
  LayoutDashboard,
  History,
  LogOut,
  LogIn,
  UserPlus,
  Wallet,
} from "lucide-react";
import { useUser } from "@/contexts/user-provider";

export function MobileNav() {
  const [open, setOpen] = useState(false);
  const [isLoggingOut, setIsLoggingOut] = useState(false);
  const pathname = usePathname();
  const { user, isAuthenticated } = useUser();

  // Different routes for authenticated and non-authenticated users
  const authenticatedRoutes = [
    {
      href: "/dashboard",
      label: "Dashboard",
      icon: <LayoutDashboard className="h-5 w-5" />,
      active: pathname === "/dashboard",
    },
    {
      href: "/accounts",
      label: "Accounts",
      icon: <Wallet className="h-5 w-5" />,
      active: pathname === "/accounts",
    },
    {
      href: "/trading-history",
      label: "Trading History",
      icon: <History className="h-5 w-5" />,
      active: pathname === "/trading-history",
    },
    {
      href: "/pricing",
      label: "Upgrade Plan",
      icon: <CreditCard className="h-5 w-5" />,
      active: pathname === "/pricing",
    },
    {
      href: "/settings",
      label: "Settings",
      icon: <Settings className="h-5 w-5" />,
      active: pathname === "/settings",
    },
  ];

  const nonAuthenticatedRoutes = [
    {
      href: "/",
      label: "Home",
      icon: <Home className="h-5 w-5" />,
      active: pathname === "/",
    },
    {
      href: "/#features",
      label: "Features",
      icon: <BarChart2 className="h-5 w-5" />,
      active: false,
    },
    {
      href: "/#how-it-works",
      label: "How It Works",
      icon: <LayoutDashboard className="h-5 w-5" />,
      active: false,
    },
    {
      href: "/#pricing",
      label: "Pricing",
      icon: <CreditCard className="h-5 w-5" />,
      active: false,
    },
  ];

  // Only show routes when loaded to prevent flash of wrong content
  const routes = 
     isAuthenticated
    ? authenticatedRoutes
    : nonAuthenticatedRoutes;

  const handleLogout = async () => {
    try {
      setIsLoggingOut(true);
      setOpen(false);
      // Clear any local storage data first
      localStorage.clear();
      // Wait for Clerk to complete the signout process with redirect
      // await signOut({ redirectUrl: "/" });
    } catch (error) {
      console.error("Logout error:", error);
      // Force redirect even if signOut fails
      window.location.href = "/";
    } finally {
      setIsLoggingOut(false);
    }
  };

  return (
    <>
      <Sheet open={open} onOpenChange={setOpen}>
        <SheetTrigger asChild>
          <Button variant="ghost" size="icon" className="md:hidden">
            <Menu className="h-6 w-6" />
            <span className="sr-only">Toggle menu</span>
          </Button>
        </SheetTrigger>
        <SheetContent side="left" className="w-[300px] sm:w-[350px] p-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <BarChart2 className="h-6 w-6 text-[#245c1a]" />
              <span className="text-xl font-bold">TradeSmart</span>
            </div>
            <Button variant="ghost" size="icon" onClick={() => setOpen(false)}>
              <X className="h-5 w-5" />
              <span className="sr-only">Close</span>
            </Button>
          </div>
          <nav className="mt-8 flex flex-col gap-4">
            {routes.map((route) => (
              <Link
                key={route.href}
                href={route.href}
                onClick={() => setOpen(false)}
                className={`flex items-center gap-2 px-4 py-3 text-lg rounded-md hover:bg-gray-100 ${
                  route.active ? "bg-gray-100 text-[#245c1a] font-medium" : ""
                }`}
              >
                {route.icon}
                {route.label}
              </Link>
            ))}

            {/* Authentication actions */}
            <div className="mt-4 pt-4 border-t">
              { isAuthenticated ? (
                <Button
                  variant="ghost"
                  className="w-full justify-start px-4 py-3 text-lg"
                  onClick={handleLogout}
                  disabled={isLoggingOut}
                >
                  <LogOut className="h-5 w-5 mr-2" />
                  {isLoggingOut ? "Logging out..." : "Logout"}
                </Button>
              ) : (
                <>
                  <Link
                    href="/auth/login"
                    onClick={() => setOpen(false)}
                    className="flex items-center gap-2 px-4 py-3 text-lg rounded-md hover:bg-gray-100"
                  >
                    <LogIn className="h-5 w-5" />
                    Log In
                  </Link>
                  <Link
                    href="/auth/signup"
                    onClick={() => setOpen(false)}
                    className="flex items-center gap-2 px-4 py-3 text-lg rounded-md hover:bg-gray-100 text-[#245c1a] font-medium"
                  >
                    <UserPlus className="h-5 w-5" />
                    Sign Up
                  </Link>
                </>
              )}
            </div>
          </nav>
        </SheetContent>
      </Sheet>
    </>
  );
}
