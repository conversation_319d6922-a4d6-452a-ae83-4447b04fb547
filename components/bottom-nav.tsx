"use client";

import { useState, useEffect } from "react";
import Link from "next/link";
import { usePathname } from "next/navigation";
import {
  LayoutDashboard,
  History,
  Settings,
  Wallet,
  DollarSign,
} from "lucide-react";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { useUser } from "@/contexts/user-provider";

export function BottomNav() {
  const pathname = usePathname();
  const { user, isAuthenticated } = useUser();
  const [openTooltip, setOpenTooltip] = useState<string | null>(null);
  const [hasCompletedOnboarding, setHasCompletedOnboarding] = useState<
    boolean | null
  >(null);

  // Fetch onboarding status from our database


  // Don't show bottom nav for non-authenticated users or on auth pages
  if (!isAuthenticated || pathname.startsWith("/auth")) {
    return null;
  }

  const routes = [
    {
      href: "/dashboard",
      label: "Dashboard",
      icon: <LayoutDashboard className="h-5 w-5" />,
      active: pathname === "/dashboard",
    },
    {
      href: "/accounts",
      label: "Accounts",
      icon: <Wallet className="h-5 w-5" />,
      active: pathname === "/accounts",
    },
    {
      href: "/profits",
      label: "Profits",
      icon: <DollarSign className="h-5 w-5" />,
      active: pathname === "/profits",
    },
    {
      href: "/trading-history",
      label: "History",
      icon: <History className="h-5 w-5" />,
      active: pathname === "/trading-history",
    },
    {
      href: "/settings",
      label: "Settings",
      icon: <Settings className="h-5 w-5" />,
      active: pathname === "/settings",
    },
  ];

  // Show loading state while checking onboarding status
  if (hasCompletedOnboarding === null) {
    return null;
  }

  // For users in onboarding, show the nav but make it non-clickable
  if (!hasCompletedOnboarding && !pathname.startsWith("/onboarding")) {
    return (
      <div className="fixed bottom-0 left-0 right-0 z-50 bg-white border-t md:hidden">
        <div className="flex items-center justify-around h-16 px-2">
          <TooltipProvider>
            {routes.map((route) => (
              <Tooltip key={route.href} open={openTooltip === route.href}>
                <TooltipTrigger asChild>
                  <button
                    className="flex flex-col items-center justify-center w-full h-full text-gray-400"
                    onClick={() => setOpenTooltip(route.href)}
                  >
                    {route.icon}
                    <span className="text-xs mt-1">{route.label}</span>
                  </button>
                </TooltipTrigger>
                <TooltipContent
                  side="top"
                  className="max-w-[200px] text-center"
                >
                  <p>Complete the onboarding process to access this feature</p>
                </TooltipContent>
              </Tooltip>
            ))}
          </TooltipProvider>
        </div>
      </div>
    );
  }

  // For users who have completed onboarding, show fully functional nav
  return (
    <div className="fixed bottom-0 left-0 right-0 z-50 bg-white border-t md:hidden">
      <div className="flex items-center justify-around h-16 px-2">
        {routes.map((route) => (
          <Link
            key={route.href}
            href={route.href}
            className={`flex flex-col items-center justify-center w-full h-full ${
              route.active ? "text-[#245c1a]" : "text-gray-500"
            }`}
          >
            {route.icon}
            <span className="text-xs mt-1">{route.label}</span>
          </Link>
        ))}
      </div>
    </div>
  );
}
