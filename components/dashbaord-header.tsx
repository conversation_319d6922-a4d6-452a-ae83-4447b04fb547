"use client";

import { Badge } from "@/components/ui/badge";
import { BottomNav } from "@/components/bottom-nav";
import { Container } from "@/components/container";
import { useAdmin } from "@/contexts/user-provider";
import { Button } from "./ui/button";
import { Plus, Users } from "lucide-react";
import { useState } from "react";

const mockCircleMembers = [
  {
    id: "member_1",
    name: "<PERSON>",
    email: "<EMAIL>",
    addedAt: "2024-01-15T00:00:00Z",
  },
  {
    id: "member_2",
    name: "<PERSON>",
    email: "<EMAIL>",
    addedAt: "2024-02-01T00:00:00Z",
  },
];

export default function DashboardHeaderNav({
  children,
}: {
  children: React.ReactNode;
}) {
  const { stats, isAdmin } = useAdmin();
  const [isCreateDrawerOpen, setIsCreateDrawerOpen] = useState(false);
  const [isCircleDialogOpen, setIsCircleDialogOpen] = useState(false);
  const [circleMembers, setCircleMembers] = useState(mockCircleMembers);
  const pendingCount = stats?.pending || 0;

  return (
    <div className="flex min-h-screen flex-col">
      <main className="flex-1 py-6 pb-20 md:pb-6">
        <Container className="max-w-7xl">
          {/* Header Section */}
          <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-6 md:mb-8">
            {/* Title + Description */}
            <div>
              <h1 className="text-2xl md:text-3xl font-bold text-gray-900 dark:text-white">
                Investment Manager
              </h1>
              <p className="mt-1 text-sm md:text-base text-gray-600 dark:text-gray-300">
                Manage your USDT investments and track compound growth
              </p>
            </div>

            {/* Actions (Buttons) */}
            <div className="mt-4 flex flex-col space-y-2 md:mt-0 md:flex-row md:items-center md:space-y-0 md:space-x-3">
              <Button
                onClick={() => setIsCreateDrawerOpen(true)}
                className="bg-[#245c1a] hover:bg-[#1a4513] dark:bg-green-600 dark:hover:bg-green-700 flex items-center justify-center gap-2 h-11 md:h-10"
              >
                <Plus className="w-4 h-4" />
                New Investment
              </Button>
            </div>
          </div>

          {children}
        </Container>
      </main>
      <BottomNav />

      {/* Create Investment Drawer - Fixed Scrolling for All Screen Sizes */}
    </div>
  );
}
