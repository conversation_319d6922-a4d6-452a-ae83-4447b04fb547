"use client"

import { useState } from "react"
import { useRout<PERSON> } from "next/navigation"
import { But<PERSON> } from "@/components/ui/button"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { AlertCircle, ChevronDown, Wallet, Settings, Plus } from "lucide-react"
import { useActiveAccount, useLegendTradingAccount } from "@/lib/hooks/use-legend-trading-account"
import { observer } from "@legendapp/state/react"

const AccountSwitcher = observer(function AccountSwitcher() {
  const router = useRouter()
  const { activeAccount, switchAccount } = useActiveAccount()
  const { accounts } = useLegendTradingAccount()
  const [isLoading, setIsLoading] = useState<string | null>(null)

  const handleSwitchAccount = (accountId: string) => {
    setIsLoading(accountId)

    // Simulate a brief loading state
    setTimeout(() => {
      switchAccount(accountId)
      setIsLoading(null)
    }, 500)
  }

  const handleConfigureAccount = (account: any) => {
    setIsLoading(account.id)

    // Navigate to account setup
    setTimeout(() => {
      router.push(`/accounts/setup/${encodeURIComponent(account.name)}`)
      setIsLoading(null)
    }, 500)
  }

  const handleAddAccount = () => {
    router.push("/accounts")
  }

  if (!activeAccount) return null

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant="outline"
          className="flex items-center justify-between w-full md:w-[260px] px-3 gap-2 h-10 border-dashed"
        >
          <div className="flex items-center gap-2 truncate">
            <Wallet className="h-4 w-4 text-[#245c1a]" />
            <span className="truncate">{activeAccount.name}</span>
            {!activeAccount.setupComplete && (
              <span className="inline-flex items-center rounded-full bg-amber-100 px-2 py-0.5 text-xs font-medium text-amber-800">
                <AlertCircle className="mr-1 h-3 w-3" />
                Setup
              </span>
            )}
          </div>
          <ChevronDown className="h-4 w-4 opacity-50" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent className="w-[260px]" align="start">
        <DropdownMenuLabel>Switch Account</DropdownMenuLabel>
        <DropdownMenuSeparator />

        {accounts.map((account) => (
          <DropdownMenuItem
            key={account.id}
            className="flex items-center justify-between p-3 cursor-pointer"
            disabled={isLoading === account.id}
          >
            <div
              className="flex items-center gap-3 flex-1"
              onClick={() => account.setupComplete ? handleSwitchAccount(account.id) : handleConfigureAccount(account)}
            >
              <Wallet className={`h-4 w-4 ${account.isActive ? "text-[#245c1a]" : "text-gray-500"}`} />
              <div className="flex flex-col">
                <span className="font-medium text-sm">{account.name}</span>
                <span className="text-xs text-gray-500">
                  {account.setupComplete
                    ? `${account.exchange} • ${account.tradingPair}`
                    : "Setup incomplete"}
                </span>
              </div>
              {account.isActive && (
                <span className="ml-auto text-xs text-[#245c1a] font-medium">Active</span>
              )}
            </div>

            {!account.setupComplete && (
              <Button
                variant="ghost"
                size="sm"
                className="ml-2 h-6 px-2 text-xs"
                onClick={(e) => {
                  e.stopPropagation()
                  handleConfigureAccount(account)
                }}
              >
                <Settings className="h-3 w-3 mr-1" />
                Configure
              </Button>
            )}
          </DropdownMenuItem>
        ))}

        <DropdownMenuSeparator />
        <DropdownMenuItem onClick={handleAddAccount} className="cursor-pointer">
          <Plus className="h-4 w-4 mr-2" />
          Add New Account
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  )
})

export { AccountSwitcher }
