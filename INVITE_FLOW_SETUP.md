# Invite-Based Approval Flow Setup

## Overview

This document outlines the complete setup for the invite-based user approval flow using a single PocketBase connection file. The system allows users to sign up with an invite code, creates them with `approved: false` status, and provides real-time updates when they get approved.

## Database Schema

Based on the current PocketBase database, here's the users table structure:

```json
{
  "admin": "",
  "approved": false,
  "avatar": "",
  "collectionId": "_pb_users_auth_",
  "collectionName": "users",
  "created": "2025-08-05 09:18:51.886Z",
  "email": "<EMAIL>",
  "emailVisibility": false,
  "id": "pf06364xcdscgcl",
  "invite_code": "OnePieceMembers",
  "name": "Tayo",
  "role": "admin",
  "updated": "2025-08-05 09:19:05.035Z",
  "verified": true
}
```

### Key Fields:
- `email`: User's email address (unique identifier)
- `name`: User's display name
- `invite_code`: The invite code this user can use to invite others
- `approved`: Boolean flag indicating if user is approved to access the app
- `role`: User role (admin/user)
- `verified`: Email verification status

## Files Created

### 1. Core Service (`lib/pocketbase-service.ts`)
- Single PocketBase connection service
- Handles all invite-based operations
- Provides real-time subscription capabilities
- Manages user creation, approval, and validation

### 2. Server Actions (`lib/actions/invite-actions.ts`)
- Server-side actions for Next.js
- Integrates with Clerk authentication
- Handles invite validation and user creation
- Provides approval status checking

### 3. React Hooks (`lib/hooks/use-approval-status.ts`)
- Client-side hooks for real-time updates
- `useApprovalStatus`: Monitor individual user approval
- `usePendingUsers`: Admin hook for monitoring all pending users

### 4. UI Components
- `components/pending-approval.tsx`: User waiting screen
- `components/admin/pending-users.tsx`: Admin approval interface

### 5. Demo Scripts
- `scripts/demo-invite-flow.ts`: Complete flow demonstration
- `scripts/inspect-schema.ts`: Database schema inspection

## Current Database State

The database currently has:
- **2 pending users** waiting for approval
- **1 approved user** (after running the demo)

### Existing Users:
1. **<EMAIL> (Tayo)**
   - Role: admin
   - Approved: true (after demo)
   - Invite Code: "" (empty)

2. **<EMAIL> (Abiola)**
   - Role: admin
   - Approved: false
   - Invite Code: "OnePieceMembers"

## How the Flow Works

### 1. User Registration
```typescript
// After Clerk signup, create pending user
const result = await createPendingUserFromClerk(inviteCode);
```

### 2. Invite Code Validation
```typescript
// Validate invite code before user creation
const validation = await validateInviteCode(inviteCode);
```

### 3. Real-time Approval Monitoring
```typescript
// User sees pending screen with real-time updates
const { approved, loading, user } = useApprovalStatus(userEmail);
```

### 4. Admin Approval
```typescript
// Admin approves user
const result = await approveUser(userId);
```

### 5. Real-time Subscription
```typescript
// Listen for approval changes
pb.collection('users').subscribe(userId, (data) => {
  if (data.record.approved) {
    // Redirect to dashboard
  }
});
```

## Environment Variables Required

```env
POCKETBASE_HOST="https://pocketbase-new.all-apps.t-apps.xyz"
POCKETBASE_EMAIL="<EMAIL>"
POCKETBASE_PASSWORD="&sO#iwpv7Ctbnie2&o&0"
```

## Testing the Flow

### Run Demo Script
```bash
npx tsx scripts/demo-invite-flow.ts
```

### Inspect Schema
```bash
npx tsx scripts/inspect-schema.ts
```

## Integration with Existing App

### 1. Replace Existing PocketBase Connections
The new `PocketBaseService` can replace existing database connections:

```typescript
// Old way
import { getDatabaseService } from '@/lib/database/database-service';

// New way
import { getPocketBaseService } from '@/lib/pocketbase-service';
```

### 2. Update Authentication Flow
Integrate with existing Clerk authentication:

```typescript
// In your signup flow
import { createPendingUserFromClerk } from '@/lib/actions/invite-actions';

// After Clerk signup
await createPendingUserFromClerk(inviteCode);
```

### 3. Add Approval Check to Dashboard
```typescript
// In dashboard page
import { checkUserApprovalStatus } from '@/lib/actions/invite-actions';

const { approved } = await checkUserApprovalStatus();
if (!approved) {
  redirect('/pending-approval');
}
```

## Real-time Features

The system uses PocketBase's built-in real-time subscriptions:

- **User approval status changes** are pushed instantly
- **Admin dashboard** updates automatically when users are created/approved
- **No polling required** - true real-time updates

## Next Steps

1. **Create invite code generation system** for approved users
2. **Add admin dashboard** with the pending users component
3. **Integrate with existing onboarding flow**
4. **Add email notifications** for approval events
5. **Implement role-based permissions** for admin actions

## Security Considerations

- All PocketBase operations use admin authentication
- Invite codes are validated before user creation
- Real-time subscriptions respect PocketBase's built-in security rules
- User approval is required before accessing the main application
