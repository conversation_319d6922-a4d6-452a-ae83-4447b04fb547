#!/usr/bin/env node

/**
 * <PERSON><PERSON><PERSON> to analyze the @gbozee/ultimate library types and structure
 * This script will examine the library without requiring database connection
 */

const fs = require('fs');
const path = require('path');

async function analyzeLibraryTypes() {
  console.log('=== Analyzing @gbozee/ultimate Library Types ===\n');

  try {
    // Read the type definitions
    const typesPath = path.join(__dirname, '../node_modules/@gbozee/ultimate/dist/index.d.ts');
    
    if (!fs.existsSync(typesPath)) {
      console.log('✗ Type definitions file not found at:', typesPath);
      return;
    }

    const typesContent = fs.readFileSync(typesPath, 'utf8');
    console.log('✓ Successfully read type definitions');
    console.log(`File size: ${typesContent.length} characters`);
    console.log();

    // Extract interfaces
    console.log('1. Extracting interfaces...');
    const interfaceMatches = typesContent.match(/export interface \w+/g) || [];
    console.log(`Found ${interfaceMatches.length} exported interfaces:`);
    interfaceMatches.forEach(match => {
      const interfaceName = match.replace('export interface ', '');
      console.log(`  - ${interfaceName}`);
    });
    console.log();

    // Extract classes
    console.log('2. Extracting classes...');
    const classMatches = typesContent.match(/export (?:declare )?class \w+/g) || [];
    console.log(`Found ${classMatches.length} exported classes:`);
    classMatches.forEach(match => {
      const className = match.replace(/export (?:declare )?class /, '');
      console.log(`  - ${className}`);
    });
    console.log();

    // Extract functions
    console.log('3. Extracting functions...');
    const functionMatches = typesContent.match(/export (?:declare )?function \w+/g) || [];
    console.log(`Found ${functionMatches.length} exported functions:`);
    functionMatches.forEach(match => {
      const functionName = match.replace(/export (?:declare )?function /, '');
      console.log(`  - ${functionName}`);
    });
    console.log();

    // Extract types
    console.log('4. Extracting types...');
    const typeMatches = typesContent.match(/export type \w+/g) || [];
    console.log(`Found ${typeMatches.length} exported types:`);
    typeMatches.forEach(match => {
      const typeName = match.replace('export type ', '');
      console.log(`  - ${typeName}`);
    });
    console.log();

    // Analyze key interfaces in detail
    console.log('5. Analyzing key interfaces...');
    
    const keyInterfaces = [
      'ExchangeAccount',
      'AppDatabase',
      'BaseExchange',
      'PositionsView',
      'Order',
      'SymbolConfig',
      'ScheduledTrade'
    ];

    keyInterfaces.forEach(interfaceName => {
      const regex = new RegExp(`export interface ${interfaceName}[^}]*}`, 's');
      const match = typesContent.match(regex);
      if (match) {
        console.log(`\n--- ${interfaceName} ---`);
        const interfaceContent = match[0];
        const fields = interfaceContent.match(/\w+\??: [^;]+;/g) || [];
        console.log(`Fields (${fields.length}):`);
        fields.slice(0, 10).forEach(field => { // Show first 10 fields
          console.log(`  ${field.trim()}`);
        });
        if (fields.length > 10) {
          console.log(`  ... and ${fields.length - 10} more fields`);
        }
      }
    });
    console.log();

    // Analyze AppDatabase methods
    console.log('6. Analyzing AppDatabase methods...');
    const appDbRegex = /export declare class AppDatabase[^}]*}/s;
    const appDbMatch = typesContent.match(appDbRegex);
    
    if (appDbMatch) {
      const appDbContent = appDbMatch[0];
      const methods = appDbContent.match(/\w+\([^)]*\): Promise<[^>]+>/g) || [];
      console.log(`Found ${methods.length} async methods in AppDatabase:`);
      methods.slice(0, 15).forEach(method => { // Show first 15 methods
        const methodName = method.split('(')[0];
        console.log(`  - ${methodName}`);
      });
      if (methods.length > 15) {
        console.log(`  ... and ${methods.length - 15} more methods`);
      }
    }
    console.log();

    // Check for exchange types
    console.log('7. Analyzing exchange types...');
    const exchangeTypes = typesContent.match(/"binance" \| "bybit"/g);
    if (exchangeTypes) {
      console.log('✓ Supported exchanges: binance, bybit');
    }

    const orderKinds = typesContent.match(/"long" \| "short"/g);
    if (orderKinds) {
      console.log('✓ Supported order kinds: long, short');
    }

    const orderSides = typesContent.match(/"sell" \| "buy"/g);
    if (orderSides) {
      console.log('✓ Supported order sides: sell, buy');
    }
    console.log();

    console.log('=== Analysis Complete ===');

  } catch (error) {
    console.error('Analysis failed:', error);
    console.error('Stack trace:', error.stack);
  }
}

// Run the analysis
analyzeLibraryTypes().catch(console.error);
