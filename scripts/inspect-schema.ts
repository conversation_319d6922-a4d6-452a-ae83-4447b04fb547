/**
 * Simple script to inspect the current database schema
 */

import { config } from 'dotenv';
import { getPocketBaseService } from '../lib/pocketbase-service';

// Load environment variables
config({ path: '.env.local' });

async function inspectCurrentSchema() {
  console.log('🔍 Inspecting Current Database Schema...\n');
  
  const pbService = getPocketBaseService();

  try {
    // Get all pending users to see the actual schema
    console.log('1. Getting pending users...');
    const pendingUsers = await pbService.getPendingUsers();
    console.log(`   Found ${pendingUsers.length} pending users\n`);

    if (pendingUsers.length > 0) {
      console.log('2. Sample user record structure:');
      const sampleUser = pendingUsers[0];
      console.log(JSON.stringify(sampleUser, null, 2));
      
      console.log('\n3. Field types:');
      Object.entries(sampleUser).forEach(([key, value]) => {
        console.log(`   ${key}: ${typeof value} ${Array.isArray(value) ? '(array)' : ''}`);
      });

      console.log('\n4. All users summary:');
      pendingUsers.forEach((user, index) => {
        console.log(`   ${index + 1}. ${user.email} (${user.name})`);
        console.log(`      - ID: ${user.id}`);
        console.log(`      - Approved: ${user.approved}`);
        console.log(`      - Invite Code: ${user.invite_code}`);
        console.log(`      - Role: ${user.role}`);
        console.log(`      - Created: ${user.created}`);
        console.log('');
      });
    }

    // Test creating a user with a valid invite code from existing users
    if (pendingUsers.length > 0) {
      const existingUser = pendingUsers[0];
      console.log(`5. Testing user creation with invite code: ${existingUser.invite_code}`);
      
      // First approve the existing user so they can invite others
      console.log('   Approving existing user first...');
      const approvalResult = await pbService.approveUser(existingUser.id);
      console.log(`   Approval result: ${approvalResult.success ? 'Success' : 'Failed'}`);
      
      if (approvalResult.success) {
        // Now try to create a new user with this invite code
        const newUserData = {
          email: `demo-user-${Date.now()}@example.com`,
          name: 'Demo User',
          invite_code: existingUser.invite_code,
          password: 'demo123456',
          passwordConfirm: 'demo123456'
        };

        console.log('   Creating new user with approved invite code...');
        const createResult = await pbService.createPendingUser(newUserData);
        console.log(`   Creation result: ${createResult.success ? 'Success' : 'Failed'}`);
        console.log(`   Message: ${createResult.message}`);
        
        if (createResult.success && createResult.user) {
          console.log(`   New user ID: ${createResult.user.id}`);
          console.log(`   New user approved status: ${createResult.user.approved}`);
        }
      }
    }

  } catch (error) {
    console.error('❌ Schema inspection failed:', error);
  }
}

// Run the inspection
if (require.main === module) {
  inspectCurrentSchema().then(() => {
    console.log('✅ Schema inspection completed!');
    process.exit(0);
  }).catch(console.error);
}

export { inspectCurrentSchema };
