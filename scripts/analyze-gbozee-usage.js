#!/usr/bin/env node

/**
 * <PERSON>ript to analyze how @gbozee/ultimate is used in the codebase
 * This will help document real-world usage patterns
 */

const fs = require('fs');
const path = require('path');

function findFiles(dir, extension, files = []) {
  const items = fs.readdirSync(dir);
  
  for (const item of items) {
    const fullPath = path.join(dir, item);
    const stat = fs.statSync(fullPath);
    
    if (stat.isDirectory() && !item.startsWith('.') && item !== 'node_modules') {
      findFiles(fullPath, extension, files);
    } else if (stat.isFile() && item.endsWith(extension)) {
      files.push(fullPath);
    }
  }
  
  return files;
}

function analyzeUsagePatterns() {
  console.log('=== Analyzing @gbozee/ultimate Usage Patterns ===\n');

  try {
    // Find all TypeScript and JavaScript files
    const files = [
      ...findFiles('.', '.ts'),
      ...findFiles('.', '.js'),
      ...findFiles('.', '.tsx')
    ].filter(file => !file.includes('node_modules') && !file.includes('.next'));

    console.log(`Found ${files.length} files to analyze\n`);

    // Track usage patterns
    const usagePatterns = {
      imports: new Set(),
      functions: new Set(),
      methods: new Set(),
      interfaces: new Set(),
      files: []
    };

    // Analyze each file
    files.forEach(file => {
      try {
        const content = fs.readFileSync(file, 'utf8');
        
        // Check if file uses gbozee library
        if (content.includes('@gbozee/ultimate') || content.includes('initApp') || content.includes('app_db')) {
          usagePatterns.files.push(file);
          
          // Extract imports
          const importMatches = content.match(/import.*from.*[@"]gbozee\/ultimate["']/g) || [];
          importMatches.forEach(match => usagePatterns.imports.add(match.trim()));
          
          // Extract function calls
          const functionMatches = content.match(/\b(initApp|initializeUltimateApp|getUltimateApp|createUltimateService)\s*\(/g) || [];
          functionMatches.forEach(match => usagePatterns.functions.add(match.replace(/\s*\($/, '')));
          
          // Extract method calls
          const methodMatches = content.match(/app_db\.\w+\(/g) || [];
          methodMatches.forEach(match => usagePatterns.methods.add(match.replace(/\($/, '')));
          
          // Extract interface usage
          const interfaceMatches = content.match(/:\s*(ExchangeAccount|AppDatabase|PositionsView|Order|SymbolConfig)/g) || [];
          interfaceMatches.forEach(match => usagePatterns.interfaces.add(match.replace(/:\s*/, '')));
        }
      } catch (error) {
        // Skip files that can't be read
      }
    });

    // Report findings
    console.log('1. Files using @gbozee/ultimate:');
    usagePatterns.files.forEach(file => {
      console.log(`  - ${file}`);
    });
    console.log(`Total: ${usagePatterns.files.length} files\n`);

    console.log('2. Import patterns:');
    Array.from(usagePatterns.imports).forEach(imp => {
      console.log(`  ${imp}`);
    });
    console.log();

    console.log('3. Function usage:');
    Array.from(usagePatterns.functions).forEach(func => {
      console.log(`  - ${func}`);
    });
    console.log();

    console.log('4. AppDatabase method usage:');
    Array.from(usagePatterns.methods).forEach(method => {
      console.log(`  - ${method}`);
    });
    console.log();

    console.log('5. Interface usage:');
    Array.from(usagePatterns.interfaces).forEach(iface => {
      console.log(`  - ${iface}`);
    });
    console.log();

    // Analyze specific usage patterns in key files
    console.log('6. Detailed analysis of key files:\n');

    const keyFiles = [
      'lib/ultimate-app.ts',
      'lib/services/ultimate-service.ts',
      'lib/client/ultimate-client.ts'
    ];

    keyFiles.forEach(file => {
      if (fs.existsSync(file)) {
        console.log(`--- ${file} ---`);
        const content = fs.readFileSync(file, 'utf8');
        
        // Extract configuration patterns
        const configMatches = content.match(/initApp\s*\(\s*{[^}]*}/s) || [];
        if (configMatches.length > 0) {
          console.log('Configuration pattern:');
          console.log(configMatches[0].substring(0, 200) + '...');
        }
        
        // Extract error handling patterns
        const errorMatches = content.match(/catch\s*\([^)]*\)\s*{[^}]*}/g) || [];
        console.log(`Error handling blocks: ${errorMatches.length}`);
        
        // Extract async patterns
        const asyncMatches = content.match(/async\s+function\s+\w+/g) || [];
        console.log(`Async functions: ${asyncMatches.length}`);
        
        console.log();
      }
    });

    // Analyze environment variable usage
    console.log('7. Environment variable patterns:');
    const envVars = new Set();
    
    usagePatterns.files.forEach(file => {
      try {
        const content = fs.readFileSync(file, 'utf8');
        const envMatches = content.match(/process\.env\.\w+/g) || [];
        envMatches.forEach(match => envVars.add(match));
      } catch (error) {
        // Skip
      }
    });
    
    Array.from(envVars).forEach(envVar => {
      console.log(`  - ${envVar}`);
    });
    console.log();

    console.log('=== Usage Analysis Complete ===');

  } catch (error) {
    console.error('Analysis failed:', error);
  }
}

// Run the analysis
analyzeUsagePatterns();
