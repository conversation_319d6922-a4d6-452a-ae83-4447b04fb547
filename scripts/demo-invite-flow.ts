/**
 * <PERSON><PERSON> script to test the invite-based approval flow
 * Run with: npx tsx scripts/demo-invite-flow.ts
 */

import { config } from 'dotenv';
import { getPocketBaseService, InviteUser } from '../lib/pocketbase-service';

// Load environment variables
config({ path: '.env.local' });

async function runDemo() {
  console.log('🚀 Starting Invite Flow Demo...\n');
  
  const pbService = getPocketBaseService();

  try {
    // Test connection first
    console.log('1. Testing PocketBase connection...');
    const connectionTest = await pbService.testConnection();
    console.log(`   ${connectionTest.success ? '✅' : '❌'} ${connectionTest.message}\n`);

    if (!connectionTest.success) {
      console.log('❌ Cannot proceed without database connection');
      return;
    }

    // Check current users in the database
    console.log('2. Checking existing users...');
    const pendingUsers = await pbService.getPendingUsers();
    console.log(`   Found ${pendingUsers.length} pending users`);
    
    // Try to get a user by email to see the structure
    const testUser = await pbService.getUserByEmail('<EMAIL>');
    if (testUser) {
      console.log('   Sample user structure:');
      console.log('   ', JSON.stringify(testUser, null, 2));
    } else {
      console.log('   No test user found');
    }
    console.log('');

    // Test invite code validation
    console.log('3. Testing invite code validation...');
    const inviteValidation = await pbService.validateInviteCode('test');
    console.log(`   Invite code 'test' validation:`, inviteValidation);
    console.log('');

    // Test creating a new pending user
    console.log('4. Testing user creation with invite code...');
    const newUserData = {
      email: `demo-user-${Date.now()}@example.com`,
      name: 'Demo User',
      invite_code: 'test', // Using the test invite code
      password: 'demo123456',
      passwordConfirm: 'demo123456'
    };

    const createResult = await pbService.createPendingUser(newUserData);
    console.log(`   User creation result:`, createResult);
    
    let createdUserId: string | undefined;
    if (createResult.success && createResult.user) {
      createdUserId = createResult.user.id;
      console.log(`   ✅ Created user with ID: ${createdUserId}`);
      console.log(`   User approved status: ${createResult.user.approved}`);
    }
    console.log('');

    // Test approval process
    if (createdUserId) {
      console.log('5. Testing user approval...');
      const approvalResult = await pbService.approveUser(createdUserId);
      console.log(`   Approval result:`, approvalResult);
      
      if (approvalResult.success) {
        console.log(`   ✅ User approved successfully`);
        console.log(`   Updated approved status: ${approvalResult.user?.approved}`);
      }
      console.log('');
    }

    // Show final state
    console.log('6. Final database state...');
    const finalPendingUsers = await pbService.getPendingUsers();
    console.log(`   Pending users count: ${finalPendingUsers.length}`);
    
    if (finalPendingUsers.length > 0) {
      console.log('   Pending users:');
      finalPendingUsers.forEach(user => {
        console.log(`   - ${user.email} (${user.name}) - Approved: ${user.approved}`);
      });
    }

  } catch (error) {
    console.error('❌ Demo failed:', error);
  }
}

// Real-time subscription demo
async function demoRealtimeSubscription() {
  console.log('\n🔄 Starting Real-time Subscription Demo...');
  console.log('This will listen for user approval changes for 30 seconds...\n');
  
  const pbService = getPocketBaseService();
  
  // Subscribe to all user changes
  const unsubscribe = pbService.subscribeToAllUsers((data) => {
    console.log(`📡 Real-time update received:`);
    console.log(`   Action: ${data.action}`);
    console.log(`   User: ${data.record.email} (${data.record.name})`);
    console.log(`   Approved: ${data.record.approved}`);
    console.log(`   Timestamp: ${new Date().toISOString()}\n`);
  });

  // Listen for 30 seconds
  setTimeout(() => {
    unsubscribe();
    console.log('🔄 Real-time subscription ended\n');
  }, 30000);
}

// Schema inspection
async function inspectSchema() {
  console.log('\n📋 Database Schema Inspection...');
  
  const pbService = getPocketBaseService();
  
  try {
    // Get a sample user to show the schema
    const sampleUser = await pbService.getUserByEmail('<EMAIL>');
    
    if (sampleUser) {
      console.log('Current users table schema (based on sample record):');
      console.log(JSON.stringify(sampleUser, null, 2));
      
      console.log('\nField types:');
      Object.entries(sampleUser).forEach(([key, value]) => {
        console.log(`  ${key}: ${typeof value} ${Array.isArray(value) ? '(array)' : ''}`);
      });
    } else {
      console.log('No sample user found to inspect schema');
    }
  } catch (error) {
    console.error('Schema inspection failed:', error);
  }
}

// Main execution
async function main() {
  await runDemo();
  await inspectSchema();
  await demoRealtimeSubscription();
  
  console.log('✅ Demo completed!');
  process.exit(0);
}

// Run the demo
if (require.main === module) {
  main().catch(console.error);
}

export { runDemo, demoRealtimeSubscription, inspectSchema };
