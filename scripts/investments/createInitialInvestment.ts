import { getDBService } from "@/lib/services/db";

const dbService = await getDBService();

interface Investment {
  id: string;
  status: string;
  wallet: string;
  amount: number;
  profit?: number;
  user: string;
  [key: string]: any;
}

interface WithdrawalRequest {
  investment: string;
  wallet: string;
  amount: number;
  status: "pending" | "approved" | "rejected";
  user: string;
  type?: "investment_completion" | "manual";
}

/**
 * Process investments: Change status and create withdrawal requests
 */
async function processInvestments() {
  // IDs to initialize
  const initializeIds = [
    "b93630536l2mzyd",
    "n6nk0auxy9ifo78",
    "whyh1ufh3tpup6k",
  ];

  for (const id of initializeIds) {
    const investment = await dbService.pb
      .collection("investments")
      .update(id, { status: "initialize", start_date: "" });
    console.log(`  ✓ Updated investment ${id} to 'initialize'`);

    const request = await dbService.createWithdrawalRequest(
      id,
      "1ccvj6a5ba4twow"
    );
    console.log(`  ✓ Created withdrawal request: ${request.id}`);
  }

//   // Update another investment to matured
//   const maturedInvestment = await dbService.pb
//     .collection("investments")
//     .update("xr4gakffjhw7kad", { status: "matured", withdrawn_at: "" });
//  await dbService.createWithdrawalRequest(
//     "xr4gakffjhw7kad",
//     "1ccvj6a5ba4twow"
//   );
//   console.log(`  ✓ Updated investment xr4gakffjhw7kad to 'matured'`);

  
}

// Export functions
export { processInvestments };

// Run if executed directly
if (require.main === module) {
  processInvestments()
    .then(() => {
      console.log("\n✅ Script completed successfully");
      process.exit(0);
    })
    .catch((error) => {
      console.error("\n❌ Script failed:", error);
      process.exit(1);
    });
}
