import { getDBService } from "@/lib/services/db";

async function clearInvestments() {
  const dbService = await getDBService();

  try {
    console.log("🚀 Starting to clear investments and withdrawal requests...");

    // 1️⃣ Delete all withdrawal requests
    const withdrawalRequests = await dbService.pb
      .collection("withdrawal_requests")
      .getFullList({ batch: 200 });

    for (const req of withdrawalRequests) {
      await dbService.pb.collection("withdrawal_requests").delete(req.id);
      console.log(`  🗑️ Deleted withdrawal request: ${req.id}`);
    }

    // 2️⃣ Delete all investments
    const investments = await dbService.pb
      .collection("investments")
      .getFullList({ batch: 200 });

    for (const inv of investments) {
      await dbService.pb.collection("investments").delete(inv.id);
      console.log(`  🗑️ Deleted investment: ${inv.id}`);
    }

    console.log("\n✅ All investments and withdrawal requests deleted successfully!");
  } catch (error) {
    console.error("\n❌ Error clearing data:", error);
  }
}

// Export so it can be imported or run directly
export { clearInvestments };

// Run if executed directly (node clearInvestments.ts)
if (require.main === module) {
  clearInvestments()
    .then(() => process.exit(0))
    .catch(() => process.exit(1));
}
