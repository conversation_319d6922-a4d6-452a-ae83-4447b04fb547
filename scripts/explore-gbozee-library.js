#!/usr/bin/env node

/**
 * <PERSON><PERSON>t to explore and document the @gbozee/ultimate library
 * This script will test various functions and document findings
 */

const { initApp } = require('@gbozee/ultimate');

async function exploreLibrary() {
  console.log('=== Exploring @gbozee/ultimate Library ===\n');

  try {
    // Test 1: Basic library import and structure
    console.log('1. Testing library import...');
    console.log('✓ Successfully imported initApp function');
    console.log('Type of initApp:', typeof initApp);
    console.log();

    // Test 2: Check environment variables
    console.log('2. Checking environment variables...');
    const requiredEnvVars = ['POCKETBASE_HOST', 'POCKETBASE_EMAIL', 'POCKETBASE_PASSWORD', 'SALT'];
    const envStatus = {};
    
    requiredEnvVars.forEach(envVar => {
      envStatus[envVar] = process.env[envVar] ? '✓ Set' : '✗ Missing';
      console.log(`${envVar}: ${envStatus[envVar]}`);
    });
    console.log();

    // Test 3: Initialize the app with minimal config
    console.log('3. Testing app initialization...');
    
    if (!process.env.POCKETBASE_HOST || !process.env.POCKETBASE_EMAIL || !process.env.POCKETBASE_PASSWORD) {
      console.log('⚠️  Cannot test app initialization - missing required environment variables');
      console.log('Please ensure POCKETBASE_HOST, POCKETBASE_EMAIL, and POCKETBASE_PASSWORD are set');
      return;
    }

    const testEmail = '<EMAIL>';
    const testSalt = process.env.SALT || 'test-salt';

    console.log(`Initializing app with email: ${testEmail}`);
    
    const app = await initApp({
      db: {
        host: process.env.POCKETBASE_HOST,
        email: process.env.POCKETBASE_EMAIL,
        password: process.env.POCKETBASE_PASSWORD,
      },
      email: testEmail,
      salt: testSalt,
      getCredentials: (account, exchange) => {
        console.log(`getCredentials called with account: ${account}, exchange: ${exchange}`);
        return {
          api_key: '',
          api_secret: '',
          email: '',
        };
      },
    });

    console.log('✓ App initialized successfully');
    console.log('App type:', typeof app);
    console.log('App keys:', Object.keys(app));
    console.log();

    // Test 4: Explore app structure
    console.log('4. Exploring app structure...');
    
    // Check for app_db property
    if (app.app_db) {
      console.log('✓ app.app_db exists');
      console.log('app_db type:', typeof app.app_db);
      console.log('app_db keys:', Object.keys(app.app_db));
      
      // Check for PocketBase instance
      if (app.app_db.pb) {
        console.log('✓ app.app_db.pb (PocketBase) exists');
        console.log('PocketBase type:', typeof app.app_db.pb);
        console.log('PocketBase constructor name:', app.app_db.pb.constructor.name);
      }
    }
    console.log();

    // Test 5: Test database connection
    console.log('5. Testing database connection...');
    
    try {
      const pb = app.app_db.pb;
      const testQuery = await pb.collection('users').getList(1, 1);
      console.log('✓ Database connection successful');
      console.log(`Found ${testQuery.items.length} users in test query`);
      console.log('Total users:', testQuery.totalItems);
    } catch (error) {
      console.log('✗ Database connection failed:', error.message);
    }
    console.log();

    // Test 6: Explore AppDatabase methods
    console.log('6. Exploring AppDatabase methods...');
    
    if (app.app_db) {
      const appDbMethods = Object.getOwnPropertyNames(Object.getPrototypeOf(app.app_db))
        .filter(name => typeof app.app_db[name] === 'function' && name !== 'constructor');
      
      console.log('Available AppDatabase methods:');
      appDbMethods.forEach(method => {
        console.log(`  - ${method}`);
      });
      console.log(`Total methods: ${appDbMethods.length}`);
    }
    console.log();

    // Test 7: Test user-related methods
    console.log('7. Testing user-related methods...');
    
    try {
      // Test getUserByEmail
      console.log('Testing getUserByEmail...');
      const user = await app.app_db.getUserByEmail();
      console.log('✓ getUserByEmail successful');
      console.log('User ID:', user?.id);
      console.log('User email:', user?.email);
      console.log('User settings type:', typeof user?.settings);
    } catch (error) {
      console.log('getUserByEmail error:', error.message);
    }

    try {
      // Test generateUserPassword
      console.log('Testing generateUserPassword...');
      await app.app_db.generateUserPassword();
      console.log('✓ generateUserPassword successful');
    } catch (error) {
      console.log('generateUserPassword error:', error.message);
    }

    try {
      // Test getUserCredentials
      console.log('Testing getUserCredentials...');
      const credentials = await app.app_db.getUserCredentials();
      console.log('✓ getUserCredentials successful');
      console.log('Credentials type:', typeof credentials);
      console.log('Credentials length:', Array.isArray(credentials) ? credentials.length : 'Not an array');
    } catch (error) {
      console.log('getUserCredentials error:', error.message);
    }
    console.log();

    // Test 8: Test collection access
    console.log('8. Testing collection access...');
    
    const collections = ['users', 'exchange_accounts', 'proxies', 'orders', 'positions'];
    
    for (const collection of collections) {
      try {
        const result = await pb.collection(collection).getList(1, 1);
        console.log(`✓ ${collection}: ${result.totalItems} total items`);
      } catch (error) {
        console.log(`✗ ${collection}: ${error.message}`);
      }
    }
    console.log();

    console.log('=== Exploration Complete ===');

  } catch (error) {
    console.error('Exploration failed:', error);
    console.error('Stack trace:', error.stack);
  }
}

// Run the exploration
exploreLibrary().catch(console.error);
