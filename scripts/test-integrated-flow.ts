/**
 * Test script for the integrated invite-based onboarding flow
 * Run with: npx tsx scripts/test-integrated-flow.ts
 */

import { config } from 'dotenv';
import { getPocketBaseService } from '../lib/pocketbase-service';

// Load environment variables
config({ path: '.env.local' });

async function testIntegratedFlow() {
  console.log('🚀 Testing Integrated Invite-Based Onboarding Flow...\n');
  
  const pbService = getPocketBaseService();

  try {
    // Test 1: Connection
    console.log('1. Testing PocketBase connection...');
    const connectionTest = await pbService.testConnection();
    console.log(`   ${connectionTest.success ? '✅' : '❌'} ${connectionTest.message}\n`);

    if (!connectionTest.success) {
      console.log('❌ Cannot proceed without database connection');
      return;
    }

    // Test 2: Current state
    console.log('2. Checking current database state...');
    const pendingUsers = await pbService.getPendingUsers();
    console.log(`   Found ${pendingUsers.length} pending users`);
    
    // Show existing users with their invite codes
    if (pendingUsers.length > 0) {
      console.log('   Existing users:');
      pendingUsers.forEach((user, index) => {
        console.log(`   ${index + 1}. ${user.email} (${user.name})`);
        console.log(`      - Invite Code: "${user.invite_code}"`);
        console.log(`      - Approved: ${user.approved}`);
        console.log(`      - Role: ${user.role}`);
      });
    }
    console.log('');

    // Test 3: Find an approved user to use their invite code
    console.log('3. Looking for approved users with invite codes...');
    let approvedUserWithCode = null;
    
    // First, let's approve a user if none are approved
    if (pendingUsers.length > 0) {
      const userToApprove = pendingUsers.find(u => u.invite_code && u.invite_code.trim() !== '');
      if (userToApprove && !userToApprove.approved) {
        console.log(`   Approving user ${userToApprove.email} to use their invite code...`);
        const approvalResult = await pbService.approveUser(userToApprove.id);
        if (approvalResult.success) {
          approvedUserWithCode = approvalResult.user;
          console.log(`   ✅ Approved user: ${approvedUserWithCode?.email}`);
        }
      } else if (userToApprove && userToApprove.approved) {
        approvedUserWithCode = userToApprove;
        console.log(`   ✅ Found already approved user: ${approvedUserWithCode.email}`);
      }
    }

    if (!approvedUserWithCode || !approvedUserWithCode.invite_code) {
      console.log('   ❌ No approved user with invite code found');
      console.log('   Creating a test approved user...');
      
      // Create a test approved user
      const testUserResult = await pbService.createPendingUser({
        email: `test-inviter-${Date.now()}@example.com`,
        name: 'Test Inviter',
        invite_code: 'MASTER_CODE',
        password: 'test123456',
        passwordConfirm: 'test123456'
      });
      
      if (testUserResult.success && testUserResult.user) {
        const approvalResult = await pbService.approveUser(testUserResult.user.id);
        if (approvalResult.success) {
          approvedUserWithCode = approvalResult.user;
          console.log(`   ✅ Created and approved test user: ${approvedUserWithCode?.email}`);
        }
      }
    }

    if (!approvedUserWithCode) {
      console.log('   ❌ Failed to get approved user with invite code');
      return;
    }

    console.log(`   Using invite code: "${approvedUserWithCode.invite_code}"\n`);

    // Test 4: Simulate beginner onboarding flow
    console.log('4. Simulating beginner onboarding flow...');
    
    // Step 4a: Validate invite code (what happens in the API)
    console.log('   Step 4a: Validating invite code...');
    const inviteValidation = await pbService.validateInviteCode(approvedUserWithCode.invite_code);
    console.log(`   ${inviteValidation.valid ? '✅' : '❌'} Invite validation: ${inviteValidation.message}`);
    
    if (!inviteValidation.valid) {
      console.log('   ❌ Cannot proceed with invalid invite code');
      return;
    }

    // Step 4b: Create pending user (what happens in createPendingUserFromClerk)
    console.log('   Step 4b: Creating pending user...');
    const newUserEmail = `beginner-${Date.now()}@example.com`;
    const newUserResult = await pbService.createPendingUser({
      email: newUserEmail,
      name: 'Test Beginner',
      invite_code: approvedUserWithCode.invite_code,
      password: 'beginner123456',
      passwordConfirm: 'beginner123456'
    });

    console.log(`   ${newUserResult.success ? '✅' : '❌'} User creation: ${newUserResult.message}`);
    
    if (!newUserResult.success || !newUserResult.user) {
      console.log('   ❌ Cannot proceed without creating user');
      return;
    }

    const newUser = newUserResult.user;
    console.log(`   Created user ID: ${newUser.id}`);
    console.log(`   User approved status: ${newUser.approved}`);

    // Test 5: Simulate pending approval state
    console.log('\n5. Simulating pending approval state...');
    console.log('   User would see pending approval page with real-time updates');
    console.log('   Admin would see user in pending users list');

    // Test 6: Simulate admin approval
    console.log('\n6. Simulating admin approval...');
    const adminApprovalResult = await pbService.approveUser(newUser.id);
    console.log(`   ${adminApprovalResult.success ? '✅' : '❌'} Admin approval: ${adminApprovalResult.message}`);
    
    if (adminApprovalResult.success) {
      console.log(`   User ${newUser.email} is now approved and can proceed to experienced onboarding`);
    }

    // Test 7: Final state check
    console.log('\n7. Final state check...');
    const finalPendingUsers = await pbService.getPendingUsers();
    console.log(`   Pending users count: ${finalPendingUsers.length}`);
    console.log(`   The newly created user should now be approved and not in pending list`);

    console.log('\n✅ Integrated flow test completed successfully!');
    console.log('\n📋 Flow Summary:');
    console.log('   1. ✅ PocketBase connection working');
    console.log('   2. ✅ Invite code validation working');
    console.log('   3. ✅ Pending user creation working');
    console.log('   4. ✅ Admin approval working');
    console.log('   5. ✅ Real-time status updates available');
    console.log('\n🎯 Integration Points:');
    console.log('   • Beginner onboarding → Pending approval page');
    console.log('   • Pending approval page → Experienced onboarding (when approved)');
    console.log('   • Admin panel → User approval management');
    console.log('   • Real-time updates throughout the flow');

  } catch (error) {
    console.error('❌ Integrated flow test failed:', error);
  }
}

// Run the test
if (require.main === module) {
  testIntegratedFlow().then(() => {
    console.log('\n🏁 Test completed!');
    process.exit(0);
  }).catch(console.error);
}

export { testIntegratedFlow };
