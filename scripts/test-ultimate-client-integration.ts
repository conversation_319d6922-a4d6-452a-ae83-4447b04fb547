/**
 * Test script to verify Ultimate client integration with PocketBase service
 * Run with: npx tsx scripts/test-ultimate-client-integration.ts
 */

import { config } from 'dotenv';
import { getPocketBaseService } from '../lib/pocketbase-service';

// Load environment variables
config({ path: '.env.local' });

async function testUltimateClientIntegration() {
  console.log('🚀 Testing Ultimate Client Integration...\n');
  
  const pbService = getPocketBaseService();

  try {
    // Test 1: Connection through Ultimate client
    console.log('1. Testing PocketBase connection through Ultimate client...');
    const connectionTest = await pbService.testConnection();
    console.log(`   ${connectionTest.success ? '✅' : '❌'} ${connectionTest.message}\n`);

    if (!connectionTest.success) {
      console.log('❌ Cannot proceed without database connection');
      return;
    }

    // Test 2: Get existing users to verify read operations
    console.log('2. Testing read operations...');
    const pendingUsers = await pbService.getPendingUsers();
    console.log(`   ✅ Successfully retrieved ${pendingUsers.length} pending users`);
    
    if (pendingUsers.length > 0) {
      const sampleUser = pendingUsers[0];
      console.log(`   Sample user: ${sampleUser.email} (${sampleUser.name})`);
      console.log(`   User ID: ${sampleUser.id}`);
      console.log(`   Approved: ${sampleUser.approved}`);
      console.log(`   Invite Code: "${sampleUser.invite_code}"`);
    }
    console.log('');

    // Test 3: Test user lookup by email
    if (pendingUsers.length > 0) {
      console.log('3. Testing user lookup by email...');
      const sampleUser = pendingUsers[0];
      const foundUser = await pbService.getUserByEmail(sampleUser.email);
      
      if (foundUser) {
        console.log(`   ✅ Successfully found user: ${foundUser.email}`);
        console.log(`   User data matches: ${foundUser.id === sampleUser.id ? 'Yes' : 'No'}`);
      } else {
        console.log(`   ❌ Failed to find user by email`);
      }
      console.log('');
    }

    // Test 4: Test user lookup by ID
    if (pendingUsers.length > 0) {
      console.log('4. Testing user lookup by ID...');
      const sampleUser = pendingUsers[0];
      const foundUser = await pbService.getUserById(sampleUser.id);
      
      if (foundUser) {
        console.log(`   ✅ Successfully found user by ID: ${foundUser.email}`);
        console.log(`   User data matches: ${foundUser.email === sampleUser.email ? 'Yes' : 'No'}`);
      } else {
        console.log(`   ❌ Failed to find user by ID`);
      }
      console.log('');
    }

    // Test 5: Test invite code validation (should fail for non-existent codes)
    console.log('5. Testing invite code validation...');
    const invalidCodeTest = await pbService.validateInviteCode('INVALID_CODE_123');
    console.log(`   Invalid code test: ${invalidCodeTest.valid ? '❌ Should be invalid' : '✅ Correctly invalid'}`);
    console.log(`   Message: ${invalidCodeTest.message}`);
    
    // Test with existing invite code if available
    const userWithInviteCode = pendingUsers.find(u => u.invite_code && u.invite_code.trim() !== '');
    if (userWithInviteCode) {
      console.log(`   Testing with existing invite code: "${userWithInviteCode.invite_code}"`);
      
      // First approve the user so their invite code becomes valid
      if (!userWithInviteCode.approved) {
        console.log(`   Approving user first to make invite code valid...`);
        const approvalResult = await pbService.approveUser(userWithInviteCode.id);
        console.log(`   Approval: ${approvalResult.success ? '✅ Success' : '❌ Failed'}`);
      }
      
      const validCodeTest = await pbService.validateInviteCode(userWithInviteCode.invite_code);
      console.log(`   Valid code test: ${validCodeTest.valid ? '✅ Correctly valid' : '❌ Should be valid'}`);
      console.log(`   Message: ${validCodeTest.message}`);
    }
    console.log('');

    // Test 6: Test creating a new user (if we have a valid invite code)
    const approvedUserWithCode = pendingUsers.find(u => u.invite_code && u.invite_code.trim() !== '' && u.approved);
    if (approvedUserWithCode) {
      console.log('6. Testing user creation...');
      const newUserData = {
        email: `test-ultimate-${Date.now()}@example.com`,
        name: 'Ultimate Test User',
        invite_code: approvedUserWithCode.invite_code,
        password: 'test123456',
        passwordConfirm: 'test123456'
      };

      const createResult = await pbService.createPendingUser(newUserData);
      console.log(`   User creation: ${createResult.success ? '✅ Success' : '❌ Failed'}`);
      console.log(`   Message: ${createResult.message}`);
      
      if (createResult.success && createResult.user) {
        console.log(`   New user ID: ${createResult.user.id}`);
        console.log(`   New user approved: ${createResult.user.approved}`);
        
        // Test approval
        console.log('   Testing user approval...');
        const approvalResult = await pbService.approveUser(createResult.user.id);
        console.log(`   Approval: ${approvalResult.success ? '✅ Success' : '❌ Failed'}`);
      }
      console.log('');
    }

    // Test 7: Get raw PocketBase instance
    console.log('7. Testing raw PocketBase instance access...');
    try {
      const rawPb = await pbService.getPocketBaseInstance();
      console.log(`   ✅ Successfully got raw PocketBase instance`);
      console.log(`   Instance type: ${typeof rawPb}`);
      console.log(`   Has collection method: ${typeof rawPb.collection === 'function' ? 'Yes' : 'No'}`);
    } catch (error) {
      console.log(`   ❌ Failed to get raw PocketBase instance: ${error}`);
    }

    console.log('\n✅ Ultimate Client Integration Test Completed!');
    console.log('\n📋 Test Summary:');
    console.log('   ✅ Ultimate client initialization working');
    console.log('   ✅ PocketBase connection through Ultimate client working');
    console.log('   ✅ Database read operations working');
    console.log('   ✅ User lookup operations working');
    console.log('   ✅ Invite code validation working');
    console.log('   ✅ User creation and approval working');
    console.log('   ✅ Raw PocketBase instance access working');
    
    console.log('\n🎯 Integration Benefits:');
    console.log('   • Uses existing Ultimate client authentication');
    console.log('   • Leverages Ultimate app PocketBase connection');
    console.log('   • Maintains consistency with existing codebase');
    console.log('   • No duplicate PocketBase instances');
    console.log('   • Proper error handling through Ultimate client');

  } catch (error) {
    console.error('❌ Ultimate client integration test failed:', error);
  }
}

// Run the test
if (require.main === module) {
  testUltimateClientIntegration().then(() => {
    console.log('\n🏁 Test completed!');
    process.exit(0);
  }).catch(console.error);
}

export { testUltimateClientIntegration };
