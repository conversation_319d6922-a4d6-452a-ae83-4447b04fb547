# @gbozee/ultimate Library Documentation

## Overview

The `@gbozee/ultimate` library (version 0.0.2-113) is a comprehensive trading automation library that provides:
- Exchange integration (Binance, Bybit)
- Database management with PocketBase
- Trading strategy implementation
- Position and order management
- Risk management and profit calculation

## Installation

```bash
pnpm add @gbozee/ultimate
```

## Core Architecture

### Main Entry Point

```typescript
import { initApp } from "@gbozee/ultimate";

const app = await initApp({
  db: {
    host: "your-pocketbase-host",
    email: "admin-email",
    password: "admin-password",
  },
  email: "user-email",
  salt: "encryption-salt",
  getCredentials: (account: string, exchange: string) => ({
    api_key: "exchange-api-key",
    api_secret: "exchange-api-secret", 
    email: "exchange-email",
  }),
});
```

### Key Components

1. **AppDatabase** - Database operations and PocketBase integration
2. **BaseExchange** - Abstract exchange interface
3. **Strategy** - Trading strategy calculations
4. **ExchangeAccount** - Account-specific operations

## Database Schema

### Core Collections

#### users
- User authentication and settings
- Encrypted credentials storage
- Password management

#### exchange_accounts
- Exchange account configurations
- Proxy settings
- Risk parameters
- Trading preferences

#### positions
- Active trading positions
- Long/short position data
- PnL tracking

#### orders
- Order management
- Entry, stop, and take-profit orders
- Order status tracking

#### proxies
- Proxy configurations
- HTTP/SOCKS5 support
- IP address management

## Key Interfaces

### ExchangeAccount
```typescript
interface ExchangeAccount {
  exchange: "binance" | "bybit";
  owner: string;
  email?: string;
  user?: string;
  usdt?: number;
  usdc?: number;
  proxy?: string;
  bullish?: boolean;
  bearish?: boolean;
  movePercent?: number;
  totalRisk?: number;
  max_non_essential?: number;
  profit_percent?: number;
  risk_reward?: number;
  exclude_coins?: { bullish?: string[] };
  include_delisted?: boolean;
}
```

### Order
```typescript
interface Order {
  symbol: string;
  account: string;
  kind: "long" | "short";
  price: number;
  quantity: number;
  side: "sell" | "buy";
  stop: number;
  order_id: string;
}
```

### PositionsView
```typescript
interface PositionsView {
  id: string;
  symbol?: any;
  entry?: any;
  quantity?: any;
  take_profit?: any;
  account?: any;
  kind?: any;
  target_pnl?: number;
  liquidation?: number;
  avg_price?: number;
  avg_qty?: number;
  next_order?: number;
  last_order?: number;
  config?: any;
  stop_loss?: { price: number; quantity: number };
  stop_pnl?: any;
  leverage?: any;
  // ... more fields
}
```

## AppDatabase Methods

The AppDatabase class provides comprehensive database operations:

### User Management
- `getUserByEmail()` - Retrieve user by email
- `generateUserPassword()` - Generate encrypted password
- `getUserCredentials()` - Get user's exchange credentials
- `saveCredentials()` - Save encrypted credentials
- `addNewCredential()` - Add new exchange credential

### Account Operations
- `getAccounts()` - Get all exchange accounts
- `get_exchange_db_instance()` - Get account with proxy info
- `getProxyForAccount()` - Get proxy configuration

### Position Management
- `getPositions()` - Get trading positions
- `createOrUpdatePositions()` - Update position data
- `update_db_position()` - Update specific position

### Order Management
- `getOrders()` - Get orders for account/symbol
- `deleteAndRecreateOrders()` - Bulk order operations
- `cancelOrders()` - Cancel orders with filters

### Configuration
- `getAllSymbolConfigs()` - Get symbol configurations
- `getSymbolConfigFromDB()` - Get specific symbol config
- `updateSymbolConfigs()` - Update symbol settings

## Usage Patterns

### 1. Initialize App
```typescript
const app = await initializeUltimateApp({ 
  email: userEmail,
  salt: userSalt 
});
```

### 2. Access PocketBase
```typescript
const pb = app.app_db.pb;
const users = await pb.collection('users').getList(1, 10);
```

### 3. User Operations
```typescript
const user = await app.app_db.getUserByEmail();
await app.app_db.generateUserPassword();
const credentials = await app.app_db.getUserCredentials();
```

### 4. Account Management
```typescript
const accounts = await app.app_db.getAccounts();
const positions = await app.app_db.getPositions({
  account: { owner: "account-name", exchange: "binance" },
  symbol: "BTCUSDT"
});
```

## Trading Features

### Supported Exchanges
- **Binance** - Spot and futures trading
- **Bybit** - Derivatives trading

### Order Types
- **Long/Short** positions
- **Buy/Sell** orders
- **Limit/Market** orders
- **Stop-loss** orders
- **Take-profit** orders

### Risk Management
- Position sizing
- Risk-reward ratios
- Stop-loss automation
- Profit-taking strategies

## Security Features

### Encryption
- AES-256-GCM encryption for credentials
- User-specific salt for encryption keys
- Secure credential storage in database

### Proxy Support
- HTTP and SOCKS5 proxy support
- Per-account proxy configuration
- Proxy rotation capabilities

## Configuration

### Environment Variables
```bash
POCKETBASE_HOST=your-pocketbase-url
POCKETBASE_EMAIL=admin-email
POCKETBASE_PASSWORD=admin-password
SALT=encryption-salt
```

### Database Collections Required
- users
- exchange_accounts
- positions
- orders
- proxies
- symbol_configs
- scheduled_trades
- account_strategies

## Error Handling

The library uses Promise-based error handling. Always wrap calls in try-catch blocks:

```typescript
try {
  const app = await initApp(config);
  const user = await app.app_db.getUserByEmail();
} catch (error) {
  console.error("Operation failed:", error.message);
}
```

## Best Practices

1. **Always initialize with proper credentials**
2. **Use environment variables for sensitive data**
3. **Implement proper error handling**
4. **Cache app instances when possible**
5. **Use transactions for bulk operations**
6. **Validate data before database operations**

## Version Information

- **Current Version**: 0.0.2-113
- **Type**: ESM/CommonJS hybrid
- **Dependencies**: PocketBase, Binance API, Bybit API
- **Node.js**: Compatible with modern Node.js versions

## Limitations and Notes

1. **Database Dependency**: Requires PocketBase instance
2. **Exchange APIs**: Requires valid API credentials
3. **Network**: May require proxy for certain regions
4. **Rate Limits**: Subject to exchange rate limiting
5. **Beta Software**: Version 0.0.2-x indicates beta status

## Real-World Usage Patterns

Based on analysis of the trading application codebase, here are the common usage patterns:

### 1. Initialization Patterns

**Standard Initialization:**
```typescript
// lib/ultimate-app.ts
const app = await initApp({
  db: {
    host: process.env.POCKETBASE_HOST,
    email: process.env.POCKETBASE_EMAIL,
    password: process.env.POCKETBASE_PASSWORD,
  },
  email: userEmail,
  salt: process.env.SALT,
  getCredentials: (_account: string, _exchange: string) => ({
    api_key: "",
    api_secret: "",
    email: "",
  }),
});
```

**Service Layer Pattern:**
```typescript
// lib/services/ultimate-service.ts
const service = createUltimateService(userEmail);
const result = await service.getUserByEmail(email);
```

### 2. Database Access Patterns

**Direct PocketBase Access:**
```typescript
const pb = app.app_db.pb;
const users = await pb.collection('users').getList(1, 1);
```

**AppDatabase Methods:**
```typescript
const user = await app.app_db.getUserByEmail();
await app.app_db.generateUserPassword();
const credentials = await app.app_db.getUserCredentials();
await app.app_db.addNewCredential(params);
```

### 3. Error Handling Patterns

```typescript
try {
  const app = await initializeUltimateApp({ email });
  return { success: true, data: app };
} catch (error) {
  console.error("Failed to initialize Ultimate app:", error);
  return { success: false, error: error.message };
}
```

### 4. Service Integration

The library is commonly wrapped in service layers:

- **UltimateService** - Main service wrapper
- **DatabaseService** - Database operations
- **UserService** - User management (deprecated in favor of UltimateService)

### 5. Common Methods Used

Most frequently used AppDatabase methods:
- `getUserByEmail()` - User retrieval
- `generateUserPassword()` - Password generation
- `getUserCredentials()` - Credential management
- `addNewCredential()` - Adding exchange credentials

## Discovered Limitations

1. **Environment Dependency**: Heavily relies on environment variables
2. **PocketBase Required**: Cannot function without PocketBase instance
3. **Async Only**: All operations are Promise-based
4. **Limited Documentation**: Minimal official documentation available
5. **Beta Status**: Version 0.0.2-x indicates ongoing development

## Integration Notes

### With Next.js
- Works in server-side contexts (API routes, server actions)
- Requires environment variables in production
- Compatible with Clerk authentication

### With PocketBase
- Requires admin credentials for initialization
- Uses collections: users, exchange_accounts, positions, orders, proxies
- Supports real-time subscriptions through PocketBase

### With Trading Exchanges
- Supports Binance and Bybit APIs
- Requires exchange API credentials
- Handles proxy configurations for regional restrictions

---

*This documentation is based on analysis of @gbozee/ultimate v0.0.2-113 and actual usage patterns in the trading application.*
