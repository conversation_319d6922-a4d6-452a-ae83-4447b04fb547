# TradeSmart API Integration Documentation

## Overview
This document outlines all the API integrations required for the TradeSmart trading platform based on the current codebase analysis. The platform is a beginner-friendly cryptocurrency trading bot that supports automated trading strategies.

## Table of Contents
1. [Authentication & User Management APIs](#authentication--user-management-apis)
2. [Exchange Integration APIs](#exchange-integration-apis)
3. [Market Data APIs](#market-data-apis)
4. [Trading Bot APIs](#trading-bot-apis)
5. [Payment & Subscription APIs](#payment--subscription-apis)
6. [Notification APIs](#notification-apis)
7. [Data Structures](#data-structures)
8. [Environment Variables](#environment-variables)

---

## Authentication & User Management APIs

### Base URL: `/api/auth`

#### 1. User Registration
```typescript
POST /api/auth/register
Content-Type: application/json

Request Body:
{
  "name": string,
  "email": string,
  "password": string
}

Response:
{
  "success": boolean,
  "user": User,
  "token": string
}
```

#### 2. User Login
```typescript
POST /api/auth/login
Content-Type: application/json

Request Body:
{
  "email": string,
  "password": string
}

Response:
{
  "success": boolean,
  "user": User,
  "token": string
}
```

#### 3. Email Verification
```typescript
POST /api/auth/verify
Content-Type: application/json

Request Body:
{
  "email": string,
  "verificationCode": string
}

Response:
{
  "success": boolean,
  "message": string
}
```

#### 4. OAuth Integration
```typescript
// Google OAuth
GET /api/auth/google
// Apple OAuth  
GET /api/auth/apple

Response: Redirect to OAuth provider
```

#### 5. User Profile Management
```typescript
GET /api/user/profile
Authorization: Bearer {token}

Response:
{
  "user": User
}

PUT /api/user/profile
Authorization: Bearer {token}
Content-Type: application/json

Request Body:
{
  "name"?: string,
  "email"?: string
}
```

---

## Exchange Integration APIs

### Base URL: `/api/exchanges`

#### 1. Binance Integration
```typescript
POST /api/exchanges/binance/connect
Authorization: Bearer {token}
Content-Type: application/json

Request Body:
{
  "apiKey": string,
  "secretKey": string,
  "accountId": string
}

Response:
{
  "success": boolean,
  "account": TradingAccount
}
```

#### 2. Account Balance
```typescript
GET /api/exchanges/{exchange}/balance/{accountId}
Authorization: Bearer {token}

Response:
{
  "balance": {
    "total": number,
    "available": number,
    "locked": number,
    "currency": string
  }
}
```

#### 3. Trading Pairs
```typescript
GET /api/exchanges/{exchange}/trading-pairs
Authorization: Bearer {token}

Response:
{
  "pairs": [
    {
      "symbol": string,
      "baseAsset": string,
      "quoteAsset": string,
      "status": string
    }
  ]
}
```

#### 4. Account Validation
```typescript
POST /api/exchanges/{exchange}/validate
Authorization: Bearer {token}
Content-Type: application/json

Request Body:
{
  "apiKey": string,
  "secretKey": string
}

Response:
{
  "valid": boolean,
  "permissions": string[],
  "message": string
}
```

---

## Market Data APIs

### Base URL: `/api/market`

#### 1. Real-time Price Data
```typescript
GET /api/market/price/{symbol}
Authorization: Bearer {token}

Response:
{
  "symbol": string,
  "price": number,
  "change24h": number,
  "changePercent24h": number,
  "volume24h": number,
  "timestamp": string
}
```

#### 2. Historical Price Data
```typescript
GET /api/market/history/{symbol}
Authorization: Bearer {token}
Query Parameters:
- interval: string (1m, 5m, 15m, 1h, 4h, 1d)
- limit: number (default: 100)
- startTime?: number
- endTime?: number

Response:
{
  "symbol": string,
  "data": [
    {
      "timestamp": number,
      "open": number,
      "high": number,
      "low": number,
      "close": number,
      "volume": number
    }
  ]
}
```

#### 3. WebSocket Price Streams
```typescript
WebSocket: wss://api.tradesmart.com/ws/market
Authorization: Bearer {token}

Subscribe Message:
{
  "method": "SUBSCRIBE",
  "params": ["btcusdt@ticker"],
  "id": 1
}

Price Update Message:
{
  "stream": "btcusdt@ticker",
  "data": {
    "symbol": "BTCUSDT",
    "price": number,
    "change": number,
    "changePercent": number,
    "timestamp": number
  }
}
```

---

## Trading Bot APIs

### Base URL: `/api/trading`

#### 1. Bot Configuration
```typescript
POST /api/trading/bot/configure
Authorization: Bearer {token}
Content-Type: application/json

Request Body:
{
  "accountId": string,
  "strategy": {
    "riskLevel": "low" | "medium" | "high",
    "takeProfit": number,
    "stopLoss": number,
    "maxTradeSize": number,
    "tradingPair": string
  }
}

Response:
{
  "success": boolean,
  "botId": string,
  "configuration": BotConfiguration
}
```

#### 2. Start/Stop Bot
```typescript
POST /api/trading/bot/{botId}/start
Authorization: Bearer {token}

POST /api/trading/bot/{botId}/stop
Authorization: Bearer {token}

Response:
{
  "success": boolean,
  "status": "running" | "stopped",
  "message": string
}
```

#### 3. Bot Status
```typescript
GET /api/trading/bot/{botId}/status
Authorization: Bearer {token}

Response:
{
  "botId": string,
  "status": "running" | "stopped" | "paused",
  "performance": {
    "totalTrades": number,
    "profitableTrades": number,
    "totalProfit": number,
    "winRate": number
  },
  "currentPosition": Position | null
}
```

#### 4. Trading History
```typescript
GET /api/trading/history/{accountId}
Authorization: Bearer {token}
Query Parameters:
- page?: number (default: 1)
- limit?: number (default: 50)
- type?: "buy" | "sell" | "all"
- startDate?: string
- endDate?: string

Response:
{
  "trades": Trade[],
  "pagination": {
    "page": number,
    "limit": number,
    "total": number,
    "totalPages": number
  }
}
```

#### 5. Execute Manual Trade
```typescript
POST /api/trading/execute
Authorization: Bearer {token}
Content-Type: application/json

Request Body:
{
  "accountId": string,
  "type": "buy" | "sell",
  "symbol": string,
  "quantity": number,
  "price"?: number, // Optional for market orders
  "orderType": "market" | "limit"
}

Response:
{
  "success": boolean,
  "orderId": string,
  "trade": Trade
}
```

---

## Payment & Subscription APIs

### Base URL: `/api/payments`

#### 1. Subscription Plans
```typescript
GET /api/payments/plans

Response:
{
  "plans": [
    {
      "id": string,
      "name": string,
      "price": number,
      "features": string[],
      "tradingVolumeLimit": number
    }
  ]
}
```

#### 2. Create Subscription
```typescript
POST /api/payments/subscribe
Authorization: Bearer {token}
Content-Type: application/json

Request Body:
{
  "planId": string,
  "paymentMethodId": string
}

Response:
{
  "success": boolean,
  "subscriptionId": string,
  "status": string,
  "nextBillingDate": string
}
```

#### 3. Payment Methods
```typescript
GET /api/payments/methods
Authorization: Bearer {token}

POST /api/payments/methods
Authorization: Bearer {token}
Content-Type: application/json

Request Body:
{
  "type": "card" | "bank",
  "details": PaymentMethodDetails
}
```

#### 4. Billing History
```typescript
GET /api/payments/billing-history
Authorization: Bearer {token}

Response:
{
  "invoices": [
    {
      "id": string,
      "amount": number,
      "date": string,
      "status": "paid" | "pending" | "failed",
      "planName": string
    }
  ]
}
```

---

## Notification APIs

### Base URL: `/api/notifications`

#### 1. Send Notifications
```typescript
POST /api/notifications/send
Authorization: Bearer {token}
Content-Type: application/json

Request Body:
{
  "userId": string,
  "type": "trade_executed" | "profit_alert" | "system_update",
  "title": string,
  "message": string,
  "data"?: object
}
```

#### 2. Email Notifications
```typescript
POST /api/notifications/email
Authorization: Bearer {token}
Content-Type: application/json

Request Body:
{
  "to": string,
  "subject": string,
  "template": string,
  "data": object
}
```

#### 3. Push Notifications
```typescript
POST /api/notifications/push
Authorization: Bearer {token}
Content-Type: application/json

Request Body:
{
  "userId": string,
  "title": string,
  "body": string,
  "data"?: object
}
```

---

## Data Structures

### User Type
```typescript
interface User {
  id: string;
  name: string;
  email: string;
  hasCompletedOnboarding: boolean;
  accounts: TradingAccount[];
  activeAccountId: string;
  subscription?: {
    planId: string;
    status: string;
    nextBillingDate: string;
  };
  createdAt: string;
  updatedAt: string;
}
```

### TradingAccount Type
```typescript
interface TradingAccount {
  id: string;
  name: string;
  exchange: string;
  apiKey: string;
  balance: string;
  tradingPair: string;
  isActive: boolean;
  setupComplete: boolean;
  createdAt: string;
}
```

### Trade Type
```typescript
interface Trade {
  id: string;
  accountId: string;
  type: "buy" | "sell";
  symbol: string;
  amount: string;
  price: string;
  profit?: string;
  status: "pending" | "completed" | "failed";
  timestamp: string;
  orderId?: string;
}
```

### BotConfiguration Type
```typescript
interface BotConfiguration {
  id: string;
  accountId: string;
  riskLevel: "low" | "medium" | "high";
  takeProfit: number;
  stopLoss: number;
  maxTradeSize: number;
  tradingPair: string;
  isActive: boolean;
  strategy: string;
}
```

### Position Type
```typescript
interface Position {
  symbol: string;
  side: "long" | "short";
  size: number;
  entryPrice: number;
  currentPrice: number;
  unrealizedPnl: number;
  timestamp: string;
}
```

---

## Environment Variables

```bash
# Database
DATABASE_URL=postgresql://username:password@localhost:5432/tradesmart

# JWT
JWT_SECRET=your-jwt-secret-key
JWT_EXPIRES_IN=7d

# Exchange APIs
BINANCE_API_URL=https://api.binance.com
BINANCE_TESTNET_URL=https://testnet.binance.vision

# Payment Processing
STRIPE_SECRET_KEY=sk_test_...
STRIPE_WEBHOOK_SECRET=whsec_...

# Email Service
SENDGRID_API_KEY=SG.xxx
FROM_EMAIL=<EMAIL>

# Push Notifications
FIREBASE_PROJECT_ID=tradesmart-app
FIREBASE_PRIVATE_KEY=-----BEGIN PRIVATE KEY-----...

# Redis (for caching and sessions)
REDIS_URL=redis://localhost:6379

# WebSocket
WS_PORT=8080

# App Configuration
NEXT_PUBLIC_APP_URL=http://localhost:3000
NEXT_PUBLIC_WS_URL=ws://localhost:8080
```

---

## API Rate Limits

- Authentication endpoints: 10 requests per minute per IP
- Trading endpoints: 100 requests per minute per user
- Market data endpoints: 1000 requests per minute per user
- WebSocket connections: 5 concurrent connections per user

## Error Handling

All APIs return standardized error responses:

```typescript
{
  "success": false,
  "error": {
    "code": string,
    "message": string,
    "details"?: object
  }
}
```

Common error codes:
- `UNAUTHORIZED`: Invalid or missing authentication
- `FORBIDDEN`: Insufficient permissions
- `VALIDATION_ERROR`: Invalid request data
- `RATE_LIMIT_EXCEEDED`: Too many requests
- `EXCHANGE_ERROR`: External exchange API error
- `INSUFFICIENT_BALANCE`: Not enough funds for trade
- `INVALID_TRADING_PAIR`: Unsupported trading pair

## Security Considerations

1. All API keys should be encrypted at rest
2. Use HTTPS for all communications
3. Implement proper CORS policies
4. Rate limiting on all endpoints
5. Input validation and sanitization
6. Audit logging for all trading operations
7. Two-factor authentication for sensitive operations

---

## Additional Integration Requirements

### 1. Account Management APIs

#### Account Creation
```typescript
POST /api/accounts/create
Authorization: Bearer {token}
Content-Type: application/json

Request Body:
{
  "name": string,
  "exchange": "binance" | "coinbase" | "kraken"
}

Response:
{
  "success": boolean,
  "account": TradingAccount
}
```

#### Account Settings Update
```typescript
PUT /api/accounts/{accountId}/settings
Authorization: Bearer {token}
Content-Type: application/json

Request Body:
{
  "name"?: string,
  "tradingPair"?: string,
  "isActive"?: boolean
}
```

#### Account Deletion
```typescript
DELETE /api/accounts/{accountId}
Authorization: Bearer {token}

Response:
{
  "success": boolean,
  "message": string
}
```

### 2. Profit Tracking APIs

#### Profit Transfers
```typescript
GET /api/profits/transfers/{accountId}
Authorization: Bearer {token}
Query Parameters:
- timeframe?: "day" | "week" | "month" | "year" | "all"
- startDate?: string
- endDate?: string

Response:
{
  "transfers": [
    {
      "id": string,
      "amount": number,
      "date": string,
      "tradingPair": string,
      "status": "completed" | "pending",
      "txId": string
    }
  ],
  "summary": {
    "totalProfit": number,
    "totalTransfers": number,
    "averageProfit": number
  }
}
```

#### Profit Analytics
```typescript
GET /api/profits/analytics/{accountId}
Authorization: Bearer {token}
Query Parameters:
- period: "7d" | "30d" | "90d" | "1y"

Response:
{
  "performance": {
    "totalProfit": number,
    "profitPercentage": number,
    "bestDay": {
      "date": string,
      "profit": number
    },
    "worstDay": {
      "date": string,
      "loss": number
    }
  },
  "chartData": [
    {
      "date": string,
      "profit": number,
      "cumulativeProfit": number
    }
  ]
}
```

### 3. Settings Management APIs

#### Trading Preferences
```typescript
GET /api/settings/trading/{accountId}
Authorization: Bearer {token}

PUT /api/settings/trading/{accountId}
Authorization: Bearer {token}
Content-Type: application/json

Request Body:
{
  "riskLevel": "low" | "medium" | "high",
  "takeProfit": number,
  "stopLoss": number,
  "maxTradeSize": number,
  "notifications": boolean,
  "autoRenew": boolean
}
```

#### Security Settings
```typescript
POST /api/settings/security/secret-key
Authorization: Bearer {token}
Content-Type: application/json

Request Body:
{
  "currentKey": string,
  "newKey": string
}

Response:
{
  "success": boolean,
  "message": string
}
```

### 4. External Service Integrations

#### Email Service (SendGrid/AWS SES)
```typescript
// Email templates for different notifications
interface EmailTemplate {
  welcome: {
    subject: "Welcome to TradeSmart",
    variables: ["userName"]
  },
  tradeAlert: {
    subject: "Trade Executed - {{tradingPair}}",
    variables: ["userName", "tradeType", "amount", "price", "profit"]
  },
  profitAlert: {
    subject: "Profit Alert - {{amount}} earned",
    variables: ["userName", "amount", "tradingPair"]
  },
  accountSetup: {
    subject: "Complete Your Account Setup",
    variables: ["userName", "setupUrl"]
  }
}
```

#### SMS Notifications (Twilio)
```typescript
POST /api/notifications/sms
Authorization: Bearer {token}
Content-Type: application/json

Request Body:
{
  "phoneNumber": string,
  "message": string,
  "type": "verification" | "alert" | "marketing"
}
```

#### Push Notifications (Firebase)
```typescript
// Push notification payload structure
interface PushNotification {
  title: string,
  body: string,
  data?: {
    type: "trade" | "profit" | "alert",
    accountId?: string,
    tradeId?: string,
    amount?: number
  },
  badge?: number,
  sound?: string
}
```

### 5. Analytics & Monitoring APIs

#### System Health
```typescript
GET /api/system/health

Response:
{
  "status": "healthy" | "degraded" | "down",
  "services": {
    "database": "up" | "down",
    "redis": "up" | "down",
    "exchanges": {
      "binance": "up" | "down"
    },
    "notifications": "up" | "down"
  },
  "timestamp": string
}
```

#### User Activity Tracking
```typescript
POST /api/analytics/track
Authorization: Bearer {token}
Content-Type: application/json

Request Body:
{
  "event": string,
  "properties": object,
  "timestamp": string
}
```

### 6. File Upload APIs

#### Document Upload (KYC)
```typescript
POST /api/upload/documents
Authorization: Bearer {token}
Content-Type: multipart/form-data

Form Data:
- file: File
- type: "id" | "proof_of_address" | "bank_statement"
- accountId: string

Response:
{
  "success": boolean,
  "fileId": string,
  "url": string
}
```

### 7. Backup & Recovery APIs

#### Account Backup
```typescript
GET /api/backup/account/{accountId}
Authorization: Bearer {token}

Response:
{
  "backup": {
    "account": TradingAccount,
    "settings": object,
    "trades": Trade[],
    "createdAt": string
  }
}
```

#### Account Recovery
```typescript
POST /api/recovery/account
Authorization: Bearer {token}
Content-Type: application/json

Request Body:
{
  "backupData": object,
  "recoveryKey": string
}
```

---

## Database Schema Requirements

### Users Table
```sql
CREATE TABLE users (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name VARCHAR(255) NOT NULL,
  email VARCHAR(255) UNIQUE NOT NULL,
  password_hash VARCHAR(255) NOT NULL,
  email_verified BOOLEAN DEFAULT FALSE,
  has_completed_onboarding BOOLEAN DEFAULT FALSE,
  subscription_id VARCHAR(255),
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);
```

### Trading Accounts Table
```sql
CREATE TABLE trading_accounts (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  name VARCHAR(255) NOT NULL,
  exchange VARCHAR(50) NOT NULL,
  api_key_encrypted TEXT,
  secret_key_encrypted TEXT,
  balance DECIMAL(20, 8) DEFAULT 0,
  trading_pair VARCHAR(20) DEFAULT 'BTC/USDT',
  is_active BOOLEAN DEFAULT FALSE,
  setup_complete BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);
```

### Trades Table
```sql
CREATE TABLE trades (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  account_id UUID REFERENCES trading_accounts(id) ON DELETE CASCADE,
  exchange_order_id VARCHAR(255),
  type VARCHAR(10) NOT NULL CHECK (type IN ('buy', 'sell')),
  symbol VARCHAR(20) NOT NULL,
  quantity DECIMAL(20, 8) NOT NULL,
  price DECIMAL(20, 8) NOT NULL,
  total_value DECIMAL(20, 8) NOT NULL,
  fees DECIMAL(20, 8) DEFAULT 0,
  profit_loss DECIMAL(20, 8),
  status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'completed', 'failed', 'cancelled')),
  executed_at TIMESTAMP,
  created_at TIMESTAMP DEFAULT NOW()
);
```

### Bot Configurations Table
```sql
CREATE TABLE bot_configurations (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  account_id UUID REFERENCES trading_accounts(id) ON DELETE CASCADE,
  strategy_name VARCHAR(100) NOT NULL,
  risk_level VARCHAR(10) CHECK (risk_level IN ('low', 'medium', 'high')),
  take_profit DECIMAL(5, 2),
  stop_loss DECIMAL(5, 2),
  max_trade_size DECIMAL(5, 2),
  is_active BOOLEAN DEFAULT FALSE,
  configuration JSONB,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);
```

### Subscriptions Table
```sql
CREATE TABLE subscriptions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  plan_id VARCHAR(50) NOT NULL,
  status VARCHAR(20) DEFAULT 'active',
  current_period_start TIMESTAMP,
  current_period_end TIMESTAMP,
  stripe_subscription_id VARCHAR(255),
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);
```

---

## Implementation Priority

### Phase 1 (Core Features)
1. User authentication and registration
2. Basic account management
3. Binance API integration
4. Real-time price data
5. Manual trading execution

### Phase 2 (Trading Bot)
1. Bot configuration APIs
2. Automated trading logic
3. Trading history tracking
4. Basic profit calculations

### Phase 3 (Advanced Features)
1. Multiple exchange support
2. Advanced analytics
3. Subscription management
4. Enhanced notifications

### Phase 4 (Enterprise Features)
1. Advanced trading strategies
2. Portfolio management
3. Risk management tools
4. Compliance and reporting

---

## Testing Strategy

### Unit Tests
- API endpoint functionality
- Data validation
- Authentication logic
- Trading calculations

### Integration Tests
- Exchange API connectivity
- Database operations
- Email/SMS delivery
- Payment processing

### End-to-End Tests
- Complete user workflows
- Trading bot operations
- Subscription management
- Error handling scenarios

---

## Deployment Considerations

### Infrastructure Requirements
- Load balancer for API endpoints
- Redis for session management and caching
- PostgreSQL for primary data storage
- WebSocket server for real-time data
- Background job processing (Bull/Agenda)
- Monitoring and logging (DataDog/New Relic)

### Scaling Considerations
- Horizontal scaling for API servers
- Database read replicas
- CDN for static assets
- Message queues for async processing
- Microservices architecture for complex features
