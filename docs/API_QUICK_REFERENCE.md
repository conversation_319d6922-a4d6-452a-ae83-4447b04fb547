# TradeSmart API Quick Reference

## Essential API Endpoints

### Authentication
```bash
# Register
POST /api/auth/register
{
  "name": "<PERSON>",
  "email": "<EMAIL>", 
  "password": "securePassword123"
}

# Login
POST /api/auth/login
{
  "email": "<EMAIL>",
  "password": "securePassword123"
}

# Verify Email
POST /api/auth/verify
{
  "email": "<EMAIL>",
  "verificationCode": "123456"
}
```

### Account Management
```bash
# Create Trading Account
POST /api/accounts/create
Authorization: Bearer {token}
{
  "name": "My BTC Account",
  "exchange": "binance"
}

# Connect Exchange
POST /api/exchanges/binance/connect
Authorization: Bearer {token}
{
  "apiKey": "your-binance-api-key",
  "secretKey": "your-binance-secret",
  "accountId": "account-uuid"
}

# Get Account Balance
GET /api/exchanges/binance/balance/{accountId}
Authorization: Bearer {token}
```

### Trading Operations
```bash
# Execute Trade
POST /api/trading/execute
Authorization: Bearer {token}
{
  "accountId": "account-uuid",
  "type": "buy",
  "symbol": "BTCUSDT",
  "quantity": 0.001,
  "orderType": "market"
}

# Get Trading History
GET /api/trading/history/{accountId}?page=1&limit=50
Authorization: Bearer {token}

# Configure Trading Bot
POST /api/trading/bot/configure
Authorization: Bearer {token}
{
  "accountId": "account-uuid",
  "strategy": {
    "riskLevel": "medium",
    "takeProfit": 3.5,
    "stopLoss": 2.5,
    "maxTradeSize": 10,
    "tradingPair": "BTC/USDT"
  }
}
```

### Market Data
```bash
# Get Current Price
GET /api/market/price/BTCUSDT
Authorization: Bearer {token}

# Get Historical Data
GET /api/market/history/BTCUSDT?interval=1h&limit=100
Authorization: Bearer {token}

# WebSocket Price Stream
WebSocket: wss://api.tradesmart.com/ws/market
Subscribe: {"method": "SUBSCRIBE", "params": ["btcusdt@ticker"], "id": 1}
```

### Profit Tracking
```bash
# Get Profit Transfers
GET /api/profits/transfers/{accountId}?timeframe=month
Authorization: Bearer {token}

# Get Profit Analytics
GET /api/profits/analytics/{accountId}?period=30d
Authorization: Bearer {token}
```

## Key Data Structures

### User
```typescript
interface User {
  id: string;
  name: string;
  email: string;
  hasCompletedOnboarding: boolean;
  accounts: TradingAccount[];
  activeAccountId: string;
}
```

### TradingAccount
```typescript
interface TradingAccount {
  id: string;
  name: string;
  exchange: string;
  apiKey: string;
  balance: string;
  tradingPair: string;
  isActive: boolean;
  setupComplete: boolean;
  createdAt: string;
}
```

### Trade
```typescript
interface Trade {
  id: string;
  accountId: string;
  type: "buy" | "sell";
  symbol: string;
  amount: string;
  price: string;
  profit?: string;
  status: "pending" | "completed" | "failed";
  timestamp: string;
}
```

## Environment Variables Setup

```bash
# Database
DATABASE_URL=postgresql://username:password@localhost:5432/tradesmart

# JWT Authentication
JWT_SECRET=your-super-secret-jwt-key
JWT_EXPIRES_IN=7d

# Binance API
BINANCE_API_URL=https://api.binance.com
BINANCE_TESTNET_URL=https://testnet.binance.vision

# Payment Processing
STRIPE_SECRET_KEY=sk_test_your_stripe_key
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret

# Email Service
SENDGRID_API_KEY=SG.your_sendgrid_key
FROM_EMAIL=<EMAIL>

# Redis Cache
REDIS_URL=redis://localhost:6379

# App Configuration
NEXT_PUBLIC_APP_URL=http://localhost:3000
NEXT_PUBLIC_WS_URL=ws://localhost:8080
```

## Error Codes Reference

| Code | Description |
|------|-------------|
| `UNAUTHORIZED` | Invalid or missing authentication token |
| `FORBIDDEN` | Insufficient permissions for operation |
| `VALIDATION_ERROR` | Invalid request data or parameters |
| `RATE_LIMIT_EXCEEDED` | Too many requests in time window |
| `EXCHANGE_ERROR` | External exchange API error |
| `INSUFFICIENT_BALANCE` | Not enough funds for trade |
| `INVALID_TRADING_PAIR` | Unsupported trading pair |
| `BOT_NOT_CONFIGURED` | Trading bot not set up |
| `ACCOUNT_NOT_VERIFIED` | Account verification required |

## Rate Limits

| Endpoint Category | Limit |
|------------------|-------|
| Authentication | 10 requests/minute per IP |
| Trading | 100 requests/minute per user |
| Market Data | 1000 requests/minute per user |
| WebSocket | 5 concurrent connections per user |

## Common Response Formats

### Success Response
```json
{
  "success": true,
  "data": { /* response data */ },
  "message": "Operation completed successfully"
}
```

### Error Response
```json
{
  "success": false,
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "Invalid trading pair",
    "details": {
      "field": "symbol",
      "value": "INVALID"
    }
  }
}
```

## WebSocket Events

### Price Updates
```json
{
  "stream": "btcusdt@ticker",
  "data": {
    "symbol": "BTCUSDT",
    "price": 64230.45,
    "change": 1250.30,
    "changePercent": 1.98,
    "timestamp": *************
  }
}
```

### Trade Execution
```json
{
  "event": "trade_executed",
  "data": {
    "tradeId": "trade-uuid",
    "accountId": "account-uuid",
    "type": "buy",
    "symbol": "BTCUSDT",
    "quantity": 0.001,
    "price": 64230.45,
    "timestamp": *************
  }
}
```

## Testing Endpoints

### Health Check
```bash
GET /api/system/health
# Returns system status and service availability
```

### Test Authentication
```bash
GET /api/auth/me
Authorization: Bearer {token}
# Returns current user information
```

### Test Exchange Connection
```bash
POST /api/exchanges/binance/test
Authorization: Bearer {token}
{
  "apiKey": "test-key",
  "secretKey": "test-secret"
}
# Tests exchange API connectivity
```

## Implementation Checklist

### Phase 1 - Core Setup
- [ ] User authentication system
- [ ] Database schema implementation
- [ ] Basic account management
- [ ] Binance API integration
- [ ] Real-time price data

### Phase 2 - Trading Features
- [ ] Manual trade execution
- [ ] Trading history tracking
- [ ] Bot configuration system
- [ ] Automated trading logic
- [ ] Profit calculations

### Phase 3 - Advanced Features
- [ ] Multiple exchange support
- [ ] Advanced analytics
- [ ] Subscription management
- [ ] Email/SMS notifications
- [ ] WebSocket real-time updates

### Phase 4 - Production Ready
- [ ] Comprehensive error handling
- [ ] Rate limiting implementation
- [ ] Security hardening
- [ ] Performance optimization
- [ ] Monitoring and logging

## Security Best Practices

1. **API Key Encryption**: Always encrypt exchange API keys at rest
2. **JWT Security**: Use strong secrets and appropriate expiration times
3. **Input Validation**: Validate all inputs on both client and server
4. **Rate Limiting**: Implement rate limiting on all endpoints
5. **HTTPS Only**: Use HTTPS for all API communications
6. **Audit Logging**: Log all trading operations for compliance
7. **2FA**: Implement two-factor authentication for sensitive operations

## Support & Documentation

- **Full API Documentation**: `/docs/API_INTEGRATION_DOCUMENTATION.md`
- **Database Schema**: See database section in main documentation
- **Error Handling**: Comprehensive error codes and messages
- **Testing**: Unit, integration, and E2E test strategies
- **Deployment**: Infrastructure and scaling considerations
