# @gbozee/ultimate Library - Practical Examples

## Basic Setup Examples

### 1. Environment Configuration

```bash
# .env.local
POCKETBASE_HOST=https://your-pocketbase-instance.com
POCKETBASE_EMAIL=<EMAIL>
POCKETBASE_PASSWORD=your-admin-password
SALT=your-encryption-salt
```

### 2. Basic App Initialization

```typescript
import { initApp } from "@gbozee/ultimate";

async function initializeApp(userEmail: string) {
  try {
    const app = await initApp({
      db: {
        host: process.env.POCKETBASE_HOST!,
        email: process.env.POCKETBASE_EMAIL!,
        password: process.env.POCKETBASE_PASSWORD!,
      },
      email: userEmail,
      salt: process.env.SALT!,
      getCredentials: (account: string, exchange: string) => {
        // Return stored credentials for the account/exchange
        return {
          api_key: "your-api-key",
          api_secret: "your-api-secret",
          email: "<EMAIL>",
        };
      },
    });
    
    return { success: true, app };
  } catch (error) {
    return { success: false, error: error.message };
  }
}
```

## User Management Examples

### 3. User Creation and Password Generation

```typescript
async function createUserWithPassword(email: string) {
  const app = await initializeApp(email);
  
  if (!app.success) {
    throw new Error("Failed to initialize app");
  }
  
  try {
    // Get or create user
    let user = await app.app.app_db.getUserByEmail();
    
    if (!user) {
      // User doesn't exist, create one
      // Note: User creation might need to be done through PocketBase directly
      const pb = app.app.app_db.pb;
      user = await pb.collection('users').create({
        email: email,
        password: "temporary-password",
        passwordConfirm: "temporary-password",
        emailVisibility: true,
        verified: true,
        name: email.split('@')[0],
        settings: JSON.stringify({})
      });
    }
    
    // Generate encrypted password
    await app.app.app_db.generateUserPassword();
    
    // Get updated user with password
    const updatedUser = await app.app.app_db.getUserByEmail();
    
    return { success: true, user: updatedUser };
  } catch (error) {
    return { success: false, error: error.message };
  }
}
```

### 4. Credential Management

```typescript
async function addExchangeCredentials(
  userEmail: string,
  credentialData: {
    name: string;
    exchange: string;
    api_key: string;
    api_secret: string;
    email: string;
  }
) {
  const app = await initializeApp(userEmail);
  
  if (!app.success) {
    throw new Error("Failed to initialize app");
  }
  
  try {
    // Add new credential
    await app.app.app_db.addNewCredential({
      password: undefined, // Will use generated password
      payload: credentialData
    });
    
    // Retrieve updated credentials
    const credentials = await app.app.app_db.getUserCredentials();
    
    return { success: true, credentials };
  } catch (error) {
    return { success: false, error: error.message };
  }
}
```

### 5. Get User Credentials

```typescript
async function getUserCredentials(userEmail: string) {
  const app = await initializeApp(userEmail);
  
  if (!app.success) {
    throw new Error("Failed to initialize app");
  }
  
  try {
    const credentials = await app.app.app_db.getUserCredentials();
    return { success: true, credentials };
  } catch (error) {
    return { success: false, error: error.message };
  }
}
```

## Database Operations Examples

### 6. Direct PocketBase Access

```typescript
async function queryDatabase(userEmail: string) {
  const app = await initializeApp(userEmail);
  
  if (!app.success) {
    throw new Error("Failed to initialize app");
  }
  
  const pb = app.app.app_db.pb;
  
  try {
    // Get users
    const users = await pb.collection('users').getList(1, 10);
    
    // Get exchange accounts
    const accounts = await pb.collection('exchange_accounts').getList(1, 10, {
      filter: `user = "${userEmail}"`
    });
    
    // Get proxies
    const proxies = await pb.collection('proxies').getList(1, 10);
    
    return {
      success: true,
      data: {
        users: users.items,
        accounts: accounts.items,
        proxies: proxies.items
      }
    };
  } catch (error) {
    return { success: false, error: error.message };
  }
}
```

### 7. Exchange Account Operations

```typescript
async function getExchangeAccounts(userEmail: string) {
  const app = await initializeApp(userEmail);
  
  if (!app.success) {
    throw new Error("Failed to initialize app");
  }
  
  try {
    const accounts = await app.app.app_db.getAccounts();
    return { success: true, accounts };
  } catch (error) {
    return { success: false, error: error.message };
  }
}
```

## Service Layer Integration

### 8. Service Wrapper Pattern

```typescript
class UltimateService {
  private userEmail: string;
  private app: any;
  
  constructor(userEmail: string) {
    this.userEmail = userEmail;
  }
  
  async initialize() {
    const result = await initializeApp(this.userEmail);
    if (!result.success) {
      throw new Error(`Failed to initialize: ${result.error}`);
    }
    this.app = result.app;
  }
  
  async getUserByEmail() {
    if (!this.app) await this.initialize();
    return await this.app.app_db.getUserByEmail();
  }
  
  async generateUserPassword() {
    if (!this.app) await this.initialize();
    return await this.app.app_db.generateUserPassword();
  }
  
  async getUserCredentials() {
    if (!this.app) await this.initialize();
    return await this.app.app_db.getUserCredentials();
  }
  
  async addCredential(payload: any) {
    if (!this.app) await this.initialize();
    return await this.app.app_db.addNewCredential({
      password: undefined,
      payload
    });
  }
}

// Usage
const service = new UltimateService("<EMAIL>");
const user = await service.getUserByEmail();
```

## Error Handling Patterns

### 9. Comprehensive Error Handling

```typescript
async function safeOperation(userEmail: string) {
  try {
    const app = await initializeApp(userEmail);
    
    if (!app.success) {
      return {
        success: false,
        error: "App initialization failed",
        details: app.error
      };
    }
    
    const user = await app.app.app_db.getUserByEmail();
    
    return {
      success: true,
      data: user
    };
    
  } catch (error) {
    console.error("Operation failed:", error);
    
    return {
      success: false,
      error: "Operation failed",
      details: error instanceof Error ? error.message : "Unknown error"
    };
  }
}
```

## Testing Patterns

### 10. Connection Testing

```typescript
async function testConnection(userEmail: string = "<EMAIL>") {
  try {
    console.log("Testing @gbozee/ultimate connection...");
    
    const app = await initializeApp(userEmail);
    
    if (!app.success) {
      console.log("❌ App initialization failed:", app.error);
      return false;
    }
    
    console.log("✅ App initialized successfully");
    
    // Test database connection
    const pb = app.app.app_db.pb;
    const testQuery = await pb.collection('users').getList(1, 1);
    console.log("✅ Database connection successful");
    console.log(`Found ${testQuery.totalItems} users`);
    
    // Test user operations
    const user = await app.app.app_db.getUserByEmail();
    console.log("✅ User operations working");
    console.log("User ID:", user?.id);
    
    return true;
    
  } catch (error) {
    console.log("❌ Connection test failed:", error.message);
    return false;
  }
}
```

## Next.js Integration

### 11. API Route Example

```typescript
// app/api/test-ultimate/route.ts
import { NextRequest, NextResponse } from "next/server";
import { initApp } from "@gbozee/ultimate";

export async function GET(request: NextRequest) {
  try {
    const app = await initApp({
      db: {
        host: process.env.POCKETBASE_HOST!,
        email: process.env.POCKETBASE_EMAIL!,
        password: process.env.POCKETBASE_PASSWORD!,
      },
      email: "<EMAIL>",
      salt: process.env.SALT!,
      getCredentials: () => ({
        api_key: "",
        api_secret: "",
        email: "",
      }),
    });
    
    const user = await app.app_db.getUserByEmail();
    
    return NextResponse.json({
      success: true,
      data: { user }
    });
    
  } catch (error) {
    return NextResponse.json(
      { success: false, error: error.message },
      { status: 500 }
    );
  }
}
```

### 12. Server Action Example

```typescript
// app/actions/user-actions.ts
"use server";

import { initApp } from "@gbozee/ultimate";
import { currentUser } from "@clerk/nextjs/server";

export async function syncUser() {
  try {
    const clerkUser = await currentUser();
    if (!clerkUser?.emailAddresses?.[0]?.emailAddress) {
      return { success: false, error: "User not authenticated" };
    }
    
    const email = clerkUser.emailAddresses[0].emailAddress;
    
    const app = await initApp({
      db: {
        host: process.env.POCKETBASE_HOST!,
        email: process.env.POCKETBASE_EMAIL!,
        password: process.env.POCKETBASE_PASSWORD!,
      },
      email,
      salt: process.env.SALT!,
      getCredentials: () => ({
        api_key: "",
        api_secret: "",
        email: "",
      }),
    });
    
    let user = await app.app_db.getUserByEmail();
    
    if (!user) {
      // Create user if doesn't exist
      const pb = app.app_db.pb;
      user = await pb.collection('users').create({
        email,
        password: "temp",
        passwordConfirm: "temp",
        emailVisibility: true,
        verified: true,
        name: clerkUser.firstName || email.split('@')[0],
        settings: JSON.stringify({})
      });
      
      await app.app_db.generateUserPassword();
      user = await app.app_db.getUserByEmail();
    }
    
    return { success: true, user };
    
  } catch (error) {
    return { success: false, error: error.message };
  }
}
```

---

*These examples are based on real usage patterns found in the trading application codebase.*
